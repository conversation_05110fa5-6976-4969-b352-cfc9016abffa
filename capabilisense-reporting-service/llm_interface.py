# frontend/services/llm_interface.py
import json
import logging
from typing import Optional, Dict, List, Any
from urllib.parse import urlencode, urlparse, urlunparse, parse_qs


import httpx

from frontend.core import config
from frontend.services.api_client import AnalysisResults

logger = logging.getLogger(__name__)

def _get_model_and_settings(prompt_config: Dict[str, Any]):
    model_aliases_val = prompt_config.get("model_aliases") # Expecting a list
    alias = None

    if isinstance(model_aliases_val, list) and model_aliases_val:
        alias = model_aliases_val[0]
        if len(model_aliases_val) > 1:
            logger.debug(f"Multiple model aliases found for prompt config, using the first: '{alias}'. Full list: {model_aliases_val}")
    elif isinstance(model_aliases_val, str):
        alias = model_aliases_val
        logger.warning(f"Prompt config uses 'model_aliases' as a string ('{alias}'). Please update to a list.")
    else:
        logger.error(f"No valid 'model_aliases' (list of strings) found in prompt_config: {prompt_config}. Cannot determine model.")
        pass

    model_info = config.prompts_library.get("models", {}).get(alias, {}) if alias else {}
    if not model_info and alias:
        logger.error(f"Model configuration not found in library for resolved alias: '{alias}'")
    elif not model_info and not alias:
         logger.error(f"Could not resolve a model alias from prompt_config: {prompt_config}")

    provider_url = model_info.get("provider_url")
    model_name = model_info.get("model_name")
    api_key_env = model_info.get("api_key_env")
    api_key = None
    if api_key_env:
        import os
        api_key_raw = os.getenv(api_key_env)
        api_key = api_key_raw.strip() if api_key_raw else None


    temperature = prompt_config.get("temperature", 0.6)
    system_prompt = prompt_config.get("system_prompt_text", "")
    safety_provider_key = model_info.get("safety_tags_provider")
    safety_settings = config.prompts_library.get("provider_safety_tags", {}).get(safety_provider_key, [])

    # Extract auth configuration
    auth_mode = model_info.get("auth_mode", "bearer").lower() # Default to bearer
    auth_header_name = model_info.get("auth_header_name")
    auth_query_param_name = model_info.get("auth_query_param_name", "key") # Default to 'key' for Google

    # Basic validation for auth config (more can be added if strictness is needed)
    if auth_mode not in ["bearer", "header", "query_param"]:
        logger.error(f"Invalid auth_mode '{auth_mode}' for alias '{alias}'. Defaulting to 'bearer'.")
        auth_mode = "bearer"
    if auth_mode == "header" and not auth_header_name:
        logger.error(f"auth_mode is 'header' but 'auth_header_name' is missing for alias '{alias}'. Auth may fail.")

    return (
        model_name, temperature, system_prompt, provider_url, api_key, safety_settings,
        auth_mode, auth_header_name, auth_query_param_name, alias # Return alias for logging
    )

def ask_llm_with_tool_support(
    user_query: Optional[str],
    chat_history: List[Dict[str, Any]],
    tools: List[Dict[str, Any]],
    prompt_config: Dict[str, Any],
    current_analysis_context: Optional[Any] = None,
    project_id_context: Optional[str] = None,
    run_id_context: Optional[str] = None
) -> Dict:
    (model_name, temperature, system_prompt, provider_url, api_key, safety_settings,
     auth_mode, auth_header_name, auth_query_param_name, model_alias_for_log) = _get_model_and_settings(prompt_config)

    if not provider_url or not model_name:
         error_msg_detail = f"Provider URL ('{provider_url}') or Model Name ('{model_name}') is missing for alias used from config '{model_alias_for_log}'."
         logger.error(f"Cannot make LLM request: {error_msg_detail}")
         return {"error": {"message": f"LLM configuration error: {error_msg_detail}", "code": "config_error"}}
    if not api_key:
         api_key_env_from_config = "Not found in model_info"
         if model_alias_for_log:
             _model_info_for_log = config.prompts_library.get("models", {}).get(model_alias_for_log, {})
             api_key_env_from_config = _model_info_for_log.get("api_key_env", "Not specified in config")
         logger.error(f"Cannot make LLM request: API Key is missing (env var expected: {api_key_env_from_config}).")
         return {"error": {"message": "LLM configuration error: API key missing.", "code": "config_error"}}

    messages = []
    if system_prompt:
        if isinstance(system_prompt, str) and len(system_prompt) < 5000:
             messages.append({"role": "system", "content": system_prompt})
        else:
             logger.warning("System prompt is invalid or too long. Skipping.")

    if isinstance(chat_history, list):
        valid_history = []
        for msg in chat_history:
            if isinstance(msg, dict) and "role" in msg and "content" in msg:
                 valid_history.append(msg)
            elif isinstance(msg, dict) and msg.get("role") == "assistant" and msg.get("tool_calls"):
                 valid_history.append(msg)
            elif isinstance(msg, dict) and msg.get("role") == "tool" and msg.get("tool_call_id") and "content" in msg:
                 valid_history.append(msg)
            else:
                 logger.warning(f"Skipping invalid message structure in chat history: {msg}")
        messages.extend(valid_history)
    else:
         logger.warning("Chat history is not a list. Skipping.")

    if user_query:
        if isinstance(user_query, str) and len(user_query) < 10000:
             messages.append({"role": "user", "content": user_query})
        else:
             logger.warning("User query is invalid or too long. Skipping.")

    headers = {"Content-Type": "application/json"}
    request_url = provider_url

    # Apply authentication based on auth_mode
    if auth_mode == "bearer":
        headers["Authorization"] = f"Bearer {api_key}"
    elif auth_mode == "header":
        if auth_header_name and isinstance(auth_header_name, str):
            headers[auth_header_name] = api_key
        else:
            logger.error(f"auth_mode is 'header' but auth_header_name ('{auth_header_name}') is invalid for model '{model_alias_for_log}'. Request will likely fail.")
            return {"error": {"message": f"LLM auth config error: Invalid 'auth_header_name' for model '{model_alias_for_log}'.", "code": "config_error"}}
    elif auth_mode == "query_param":
        if auth_query_param_name and isinstance(auth_query_param_name, str):
            try:
                parsed_url = urlparse(request_url)
                current_query_params = parse_qs(parsed_url.query)
                current_query_params[auth_query_param_name] = [api_key] # Add or update the key
                new_query_string = urlencode(current_query_params, doseq=True)
                request_url = urlunparse(parsed_url._replace(query=new_query_string))
                logger.debug(f"URL modified for query_param auth. New URL (key part redacted for log): {request_url.split('?')[0]}?...")
            except Exception as url_e:
                logger.error(f"Failed to append API key to URL for query_param auth: {url_e}")
                return {"error": {"message": f"LLM auth config error: Failed to build URL for query_param auth for model '{model_alias_for_log}'.", "code": "config_error"}}
        else:
            logger.error(f"auth_mode is 'query_param' but auth_query_param_name ('{auth_query_param_name}') is invalid for model '{model_alias_for_log}'. Request will likely fail.")
            return {"error": {"message": f"LLM auth config error: Invalid 'auth_query_param_name' for model '{model_alias_for_log}'.", "code": "config_error"}}


    # Provider-specific headers (that are not auth related)
    provider_domain_for_specific_headers = urlparse(provider_url).netloc.lower()
    if "anthropic" in provider_domain_for_specific_headers:
        headers["anthropic-version"] = model_info.get("anthropic_version", "2023-06-01") # Use from config or default
        logger.debug(f"Applied Anthropic version header: {headers['anthropic-version']}")

    # Construct payload core
    payload: Dict[str, Any] = {"messages": messages, "temperature": temperature}

    # Provider-specific payload structuring
    if "google" in provider_domain_for_specific_headers and "generativelanguage" in provider_domain_for_specific_headers : # Gemini REST API
        # Transform messages to Google's format
        google_contents = []
        for msg in messages:
            role = "user" if msg["role"] == "user" else "model" # Google uses "user" and "model"
            # Gemini doesn't have a direct "system" role in the main contents array.
            # System prompts are often handled differently (e.g., separate parameter or pre-pended to user message)
            # For simplicity here, if it's a system message, it might need to be merged or handled by specific prompt construction.
            # Current logic assumes system prompt is part of the first user message or handled by the API itself if supported.
            if msg["role"] == "system": # Skip system for google contents for now, assume handled by prompt strategy
                logger.debug("Skipping 'system' role message for Google Gemini payload construction.")
                continue
            google_contents.append({"role": role, "parts": [{"text": msg["content"]}]})

        payload = {"contents": google_contents, "generationConfig": {"temperature": temperature}}
        if tools: payload["tools"] = [{"functionDeclarations": [t["function"] for t in tools]}]
        if safety_settings: payload["safetySettings"] = safety_settings

    else: # OpenAI, OpenRouter, Azure, Mistral, Anthropic (messages format is similar)
        payload["model"] = model_name # Model name is part of the main payload for these
        if tools: payload["tools"] = tools
        # Safety settings might be applicable for some of these but often aren't top-level like Google's
        # or are handled by content filters by default.


    # --- DETAILED LOGGING OF REQUEST ---
    try:
        log_payload = payload.copy()
        if "messages" in log_payload: log_payload['messages'] = f"[{len(payload['messages'])} messages: roles={[m.get('role') for m in payload['messages'] if isinstance(m,dict)]}]"
        if "contents" in log_payload: log_payload['contents'] = f"[{len(payload['contents'])} contents blocks]"
        if 'tools' in log_payload: log_payload['tools'] = f"[{len(payload['tools'])} tools defined]"

        log_headers = headers.copy()
        # Redact based on actual auth method used
        if auth_mode == "bearer" and "Authorization" in log_headers: log_headers["Authorization"] = "Bearer [REDACTED]"
        elif auth_mode == "header" and auth_header_name and auth_header_name in log_headers: log_headers[auth_header_name] = "[REDACTED]"
        # Query param redaction is handled by the request_url log line already

        # Log the URL that will be hit
        log_request_url = request_url
        if auth_mode == "query_param" and auth_query_param_name and api_key:
            log_request_url = request_url.replace(api_key, "[REDACTED_API_KEY_IN_URL]")


        logger.debug(f"--- Sending LLM API Request ---")
        logger.debug(f"URL: POST {log_request_url}")
        logger.debug(f"Headers: {log_headers}")
        logger.debug(f"Payload for Log: {json.dumps(log_payload, indent=2)}")
        logger.debug(f"-----------------------------")

    except Exception as log_e:
        logger.error(f"Error creating log message for LLM request: {log_e}")

    try:
        with httpx.Client(timeout=120.0) as client:
            response = client.post(request_url, headers=headers, json=payload) # Use request_url
            response.raise_for_status()
            resp_json = response.json()
            logger.debug(f"LLM API Response Status: {response.status_code}, Keys: {list(resp_json.keys())}")
            return resp_json
    except httpx.HTTPStatusError as e:
        response_text = "[Could not decode response text]"
        try: response_text = e.response.text
        except Exception: pass
        logger.error(f"LLM API HTTP Error: {e.response.status_code} for {e.request.url}. Response: {response_text[:500]}...")
        try: return e.response.json()
        except json.JSONDecodeError: return {"error": {"message": f"LLM API HTTP Error: {e.response.status_code}. Non-JSON error response.", "code": e.response.status_code, "raw_body": response_text[:200]}}
    except httpx.RequestError as e:
        logger.error(f"LLM API Request Error (Network/Connection): {e}")
        return {"error": {"message": f"Network error contacting LLM API: {e}", "code": "network_error"}}
    except Exception as e:
        logger.error(f"Unexpected error during LLM API call: {e}", exc_info=True)
        return {"error": {"message": f"Unexpected error during LLM API call: {e}", "code": "unexpected_error"}}


def extract_text_response(resp_json: Dict) -> Optional[str]:
    if not resp_json or not isinstance(resp_json, dict): return None
    try:
        # OpenAI / OpenRouter / Mistral / Azure style response
        choices = resp_json.get("choices")
        if choices and isinstance(choices, list) and choices[0]:
            message = choices[0].get("message")
            if message and isinstance(message, dict) and "content" in message:
                return message.get("content")
            # Sometimes content might be directly in choice for simpler models/APIs
            if "text" in choices[0]: return choices[0].get("text") # e.g. some older OpenAI models or completions endpoint

        # Anthropic style
        if "content" in resp_json and isinstance(resp_json["content"], list):
            for block in resp_json["content"]:
                if block.get("type") == "text": return block.get("text")
        elif "completion" in resp_json and isinstance(resp_json["completion"], str): # Older Anthropic
            return resp_json["completion"]

        # Google Gemini REST style
        if "candidates" in resp_json and isinstance(resp_json["candidates"], list) and resp_json["candidates"]:
            candidate = resp_json["candidates"][0]
            if candidate and isinstance(candidate, dict) and "content" in candidate:
                content = candidate["content"]
                if content and isinstance(content, dict) and "parts" in content and isinstance(content["parts"], list) and content["parts"]:
                    part = content["parts"][0]
                    if part and isinstance(part, dict) and "text" in part:
                        return part["text"]

        # If a simple error message is returned directly
        if "message" in resp_json and isinstance(resp_json.get("error"), dict): # e.g. from API itself
            return f"Error from API: {resp_json['error'].get('message', 'Unknown error')}"

    except Exception as e:
        logger.warning(f"Error extracting text response: {e} from {str(resp_json)[:200]}...")
    return None


def extract_tool_calls(resp_json: Dict) -> List[Dict]:
    calls = []
    if not resp_json or not isinstance(resp_json, dict): return calls
    try:
        # OpenAI / OpenRouter / Mistral / Azure style
        choices = resp_json.get("choices", [])
        if choices and choices[0].get("message", {}).get("tool_calls"):
            tool_calls_raw = choices[0].get("message", {}).get("tool_calls")
            if tool_calls_raw and isinstance(tool_calls_raw, list):
                for tc_raw in tool_calls_raw: # Ensure each call is a dict
                    if isinstance(tc_raw, dict): calls.append(tc_raw)
                if calls: return calls

        # Anthropic style (Claude 3 Opus/Sonnet/Haiku with tool use)
        if "content" in resp_json and isinstance(resp_json["content"], list):
            for block in resp_json["content"]:
                if block.get("type") == "tool_use":
                    calls.append({
                        "id": block.get("id"), "type": "function",
                        "function": {"name": block.get("name"), "arguments": json.dumps(block.get("input", {}))}
                    })
            if calls: return calls

        # Google Gemini REST style (tool calls are inside 'parts' of a 'model' role content block)
        if "candidates" in resp_json and isinstance(resp_json["candidates"], list) and resp_json["candidates"]:
            candidate_content = resp_json["candidates"][0].get("content", {})
            if candidate_content.get("role") == "model": # Tool calls are from the model
                parts = candidate_content.get("parts", [])
                for part in parts:
                    if "functionCall" in part and isinstance(part["functionCall"], dict):
                        fc = part["functionCall"]
                        # Gemini function calls need an ID. If not provided by API, generate one.
                        # However, for proper tool use loop, the API should provide an ID.
                        # This part might need review based on actual Gemini API tool call response format.
                        # For now, we assume the caller (chat_handler) can manage if IDs are missing/different.
                        # A common pattern for Gemini is to expect the `functionResponse` to identify the call by name if IDs are not used in request.
                        # For OpenAI compatibility, an ID is good.
                        # For now, let's assume Gemini does not give an ID for the call itself, but the response should be tied to the NAME.
                        # The `format_tool_response` will need to handle this.
                        # To be compatible with current loop, we need an ID.
                        # Placeholder ID if Gemini doesn't provide one (This is NOT ideal for multi-turn tools)
                        call_id = fc.get("id", f"gemini_tool_call_{fc.get('name', 'unknown')}_{len(calls)}")

                        calls.append({
                            "id": call_id,
                            "type": "function",
                            "function": {
                                "name": fc.get("name"),
                                "arguments": json.dumps(fc.get("args", {})) # Gemini uses "args"
                            }
                        })
            if calls: return calls

    except Exception as e:
        logger.warning(f"Error extracting tool calls: {e} from {str(resp_json)[:200]}...")
    return calls


def format_tool_response(tool_call_request: Dict, result_data: Any) -> Dict:
    # tool_call_request is assumed to be in OpenAI format (id, type, function:{name, arguments})
    # This function prepares a message for the LLM history indicating the tool's result.

    content_str = json.dumps(result_data) if not isinstance(result_data, str) else result_data

    # For Google Gemini, the format for a tool result message is:
    # {"role": "function", "parts": [{"functionResponse": {"name": "tool_name", "response": {"content": ...}}}]}
    # This needs to be known by the calling code that constructs the *next* request to Gemini.
    # This function's output is for the generic history.
    # The main `ask_llm_with_tool_support` needs to format `messages` correctly for EACH provider.

    return {
        "role": "tool",
        "tool_call_id": tool_call_request.get("id"),
        "name": tool_call_request.get("function", {}).get("name"),
        "content": content_str
    }

def get_raw_llm_response_for_history(resp_json: Dict) -> Dict:
    # This function gets the assistant's part of the conversation (text + tool calls) for history.

    if not resp_json or not isinstance(resp_json, dict):
        return {"role": "assistant", "content": "[Error: Invalid response JSON]"}

    # OpenAI / OpenRouter / Mistral / Azure style
    try:
        choices = resp_json.get("choices", [])
        if choices and isinstance(choices, list) and len(choices) > 0:
            msg = choices[0].get("message", {})
            if isinstance(msg, dict):
                # Ensure 'role' is present, default to 'assistant' if missing
                return {"role": msg.get("role", "assistant"), **msg}
    except Exception as e:
        logger.warning(f"Could not extract OpenAI-style raw message for history: {e}")

    # Anthropic style
    try:
        if "content" in resp_json and isinstance(resp_json["content"], list):
            assistant_msg_for_history = {"role": "assistant", "content": None, "tool_calls": []}
            text_content_parts = []
            for block in resp_json["content"]:
                if block.get("type") == "text": text_content_parts.append(block.get("text", ""))
                elif block.get("type") == "tool_use":
                    assistant_msg_for_history["tool_calls"].append({
                        "id": block.get("id"), "type": "function",
                        "function": {"name": block.get("name"), "arguments": json.dumps(block.get("input", {}))}
                    })
            if text_content_parts: assistant_msg_for_history["content"] = "".join(text_content_parts)
            if assistant_msg_for_history["tool_calls"] or assistant_msg_for_history["content"] is not None:
                return assistant_msg_for_history
    except Exception as e:
        logger.warning(f"Could not extract Anthropic-style raw message for history: {e}")

    # Google Gemini REST style
    # The 'raw response' for Gemini to be added to history should represent the model's turn.
    # If it made function calls, the history message should reflect that.
    try:
        if "candidates" in resp_json and isinstance(resp_json["candidates"], list) and resp_json["candidates"]:
            candidate = resp_json["candidates"][0]
            if candidate and isinstance(candidate, dict) and "content" in candidate:
                content_block = candidate["content"] # This is the model's turn content
                if content_block and isinstance(content_block, dict) and content_block.get("role") == "model":
                    # Reconstruct an OpenAI-like assistant message from Gemini's response
                    assistant_msg_for_history = {"role": "assistant", "content": None, "tool_calls": []}
                    text_parts = []
                    function_call_parts = []

                    for part in content_block.get("parts", []):
                        if "text" in part: text_parts.append(part["text"])
                        elif "functionCall" in part and isinstance(part["functionCall"], dict):
                            fc = part["functionCall"]
                            # This ID needs to be consistent with what `extract_tool_calls` produces for Gemini
                            call_id = fc.get("id", f"gemini_tool_call_{fc.get('name', 'unknown')}_{len(assistant_msg_for_history['tool_calls'])}")
                            function_call_parts.append({
                                "id": call_id, "type": "function",
                                "function": {"name": fc.get("name"),"arguments": json.dumps(fc.get("args", {}))}
                            })

                    if text_parts: assistant_msg_for_history["content"] = "".join(text_parts)
                    if function_call_parts: assistant_msg_for_history["tool_calls"] = function_call_parts

                    if assistant_msg_for_history["content"] is not None or assistant_msg_for_history["tool_calls"]:
                        return assistant_msg_for_history
    except Exception as e:
        logger.warning(f"Could not extract Google Gemini-style raw message for history: {e}")

    logger.warning("Failed to extract raw LLM response for history from any known format. Returning default error content.")
    return {"role": "assistant", "content": "[Error extracting response for history]"}


def ask_llm_basic_chat(
    user_query: str,
    chat_history: List[Dict[str, Any]],
    prompt_config: Dict[str, Any],
    analysis_results: Optional[AnalysisResults] = None
) -> str:
    try:
        resp_json = ask_llm_with_tool_support(
            user_query=user_query, chat_history=chat_history, tools=[],
            prompt_config=prompt_config, current_analysis_context=analysis_results
        )
        if resp_json and 'error' in resp_json:
            error_detail = resp_json['error']
            logger.error(f"Basic chat LLM API returned an error: {error_detail}")
            error_message_for_user = f"Sorry, I encountered an error communicating with the AI assistant: {error_detail.get('message', 'Unknown error')}"
            if isinstance(error_detail.get("raw_body"), str) and config.DEBUG_MODE: # Show raw body in debug
                 error_message_for_user += f"\n(Raw Error: {error_detail['raw_body']})"
            return error_message_for_user

        text_response = extract_text_response(resp_json)
        return text_response or "Sorry, I couldn't get a response."
    except Exception as e:
        logger.error(f"Basic chat error: {e}", exc_info=True)
        return f"Sorry, an error occurred: {e}"

def generate_emoji_for_taxonomy(
    name: str,
    description: Optional[str] = None,
    prompt_config: Optional[Dict[str, Any]] = None
) -> str:
    """
    Generates an appropriate Unicode emoji for a taxonomy item based on its name and description.

    Args:
        name: The name of the taxonomy item
        description: Optional description of the taxonomy item
        prompt_config: Optional configuration for the LLM prompt

    Returns:
        A single Unicode emoji character that represents the taxonomy item
    """
    try:
        # Use emoji generator prompt config if none provided
        if not prompt_config:
            from frontend.core import config as fe_config
            prompt_config = fe_config.prompts_library.get("emoji_generator", {})
            if not prompt_config:
                # Fall back to frontend chat config if emoji generator config is not available
                prompt_config = fe_config.prompts_library.get(fe_config.FRONTEND_CHAT_CONFIG_ID, {})

        # Create a simple prompt for the LLM
        desc_text = f" Description: {description}" if description else ""
        user_query = f"Generate a single Unicode emoji that best represents this taxonomy category. Name: {name}{desc_text}. Respond with ONLY the emoji character, nothing else."

        # Create a minimal chat history with the system prompt from the config
        system_prompt = prompt_config.get("system_prompt", "You are a helpful assistant that generates appropriate Unicode emojis for taxonomy categories. Respond with only the emoji character, no text.")
        chat_history = [
            {
                "role": "system",
                "content": system_prompt
            }
        ]

        logger.info(f"Calling LLM for emoji generation with prompt: '{user_query}'")
        logger.info(f"Using prompt config: {prompt_config}")

        # Call the LLM
        resp_json = ask_llm_with_tool_support(
            user_query=user_query, chat_history=chat_history, tools=[],
            prompt_config=prompt_config
        )

        logger.info(f"LLM response for emoji generation: {resp_json}")

        if resp_json and 'error' in resp_json:
            error_detail = resp_json['error']
            logger.error(f"Emoji generation LLM API returned an error: {error_detail}")
            logger.info("Using default emoji '📊' due to LLM error")
            return "📊"  # Default emoji for error case

        text_response = extract_text_response(resp_json)
        logger.info(f"Extracted text response from LLM: '{text_response}'")

        if not text_response:
            logger.warning("No text response from LLM for emoji generation")
            logger.info("Using default emoji '📊' due to empty response")
            return "📊"  # Default emoji

        # Extract just the first emoji character
        import re
        emoji_pattern = re.compile(r'[\U00010000-\U0010ffff\U0001F300-\U0001F64F\U0001F680-\U0001F6FF\u2600-\u26FF\u2700-\u27BF]')
        emojis = emoji_pattern.findall(text_response)

        logger.info(f"Emojis found in response: {emojis}")

        if emojis:
            logger.info(f"Selected emoji: '{emojis[0]}'")
            return emojis[0]  # Return the first emoji found
        else:
            # If no emoji found in the response, use a default
            logger.warning(f"No emoji found in LLM response: '{text_response}'")
            logger.info("Using default emoji '📊' due to no emoji in response")
            return "📊"  # Default emoji

    except Exception as e:
        logger.error(f"Emoji generation error: {e}", exc_info=True)
        return "📊"  # Default emoji for error case