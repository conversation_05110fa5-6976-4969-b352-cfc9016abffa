# CapabiliSense PDF Reporting Service

A Go-based service that generates professional PDF reports for digital transformation capability assessments.

## 🚀 Current Status: v0.1.0 - Fully Functional

✅ **Working PDF Generation**
✅ **Complete Data Pipeline**
✅ **Mock AI Insights**
✅ **Professional Report Layout**

## 📋 Features

- **Dual PDF Rendering Approach**:
  - **Simple PDF Renderer** (Primary): Direct fpdf implementation for reliable PDF generation
  - **mdtopdf Renderer** (Secondary): Markdown-based rendering for future enhancements
- **Complete Assessment Data Processing**: Domain scoring, rankings, strengths/weaknesses analysis
- **AI Insights Integration**: Mock LLM client with structured responses
- **Professional Report Sections**: Overview, domain scores, key strengths, focus areas, AI spotlight, ETS solutions
- **RESTful API**: HTTP endpoints for PDF generation

## 🏗️ Architecture

The service follows a three-stage pipeline:

1. **Stage A - Data Extraction** (`pkg/dataextraction/`): Raw assessment data processing and algorithmic analysis
2. **Stage B - AI Insights** (`pkg/aiinsights/`): LLM-powered insight generation with structured outputs
3. **Stage C - PDF Generation** (`pkg/pdfgenerator/`): Professional PDF report creation

## 🚀 Quick Start

### Prerequisites
- Go 1.24+
- No external dependencies required for basic operation

### Running the Service

```bash
cd cmd/reportgenerator
go run main.go
```

The service will start on port 8081 with the following endpoints:

- **Primary**: `http://localhost:8081/generate-report` (Simple PDF Renderer)
- **Debug**: `http://localhost:8081/generate-report-mdtopdf` (mdtopdf Renderer)

### Testing

Visit `http://localhost:8081/generate-report` in your browser to download a sample PDF report.

## 📊 Sample Report Content

The service generates comprehensive reports including:

- **Organization Overview**: Assessment summary and key metrics
- **Domain Scores**: Performance across capability domains (Vision, People, Process, Technology)
- **Key Strengths**: Top-performing areas with AI-generated insights
- **Areas for Focus**: Critical improvement opportunities
- **AI Spotlight**: Key insights and recommendations
- **ETS Solutions**: Specific solution recommendations

## 🔧 Configuration

### Environment Variables

- `PORT`: Service port (default: 8081)
- `PROMPTS_LIBRARY_PATH`: Path to prompts configuration (default: configs/prompts_library.json)
- `TEMPLATES_PATH`: Path to report templates (default: templates)
- `LOG_LEVEL`: Logging level (default: info)

### Mock Data

Currently uses comprehensive mock data for development and testing. See `getMockReportData()` in `cmd/reportgenerator/main.go`.

## 📁 Project Structure

```
capabilisense-reporting-service/
├── cmd/reportgenerator/           # Main application entry point
├── pkg/
│   ├── config/                    # Configuration management
│   ├── dataextraction/           # Data processing and analysis
│   ├── aiinsights/               # AI insights generation
│   ├── pdfgenerator/             # PDF rendering (dual approach)
│   └── models/                   # Shared data models
├── configs/                      # Configuration files
├── templates/                    # Report templates
└── docs/                        # Documentation
```

## 🔄 Development Status

### ✅ Completed
- Core service architecture
- Data extraction and processing algorithms
- Mock AI insights pipeline
- Simple PDF renderer with professional layout
- HTTP API endpoints
- Comprehensive error handling

### 🔄 Future Enhancements
- Real database integration for data extraction
- Live LLM integration (OpenAI, Google)
- Enhanced mdtopdf renderer debugging
- HTML templating with headless browser rendering
- Advanced report customization options

## 🐛 Known Issues

- **mdtopdf Renderer**: Currently produces 0-byte PDFs due to library issues. Enhanced with fallback content and maintained for future debugging.
- **Solution**: Simple PDF Renderer provides reliable alternative with professional output.

## 📚 Documentation

- [`CODEBASE.md`](CODEBASE.md) - Detailed codebase structure and architecture
- [`CHANGELOG_AND STATUS.md`](CHANGELOG_AND STATUS.md) - Development progress and status updates
- [`PRD.md`](PRD.md) - Product requirements and specifications

## 🤝 Contributing

1. Ensure Go 1.24+ is installed
2. Run tests: `go test ./...`
3. Follow Go conventions and add appropriate documentation
4. Test both PDF renderers when making changes

## 📄 License

[Add your license information here]