# Product Requirements Document: CapabiliSense PDF Reporting Service (Go)

## 1. Introduction

This document outlines the requirements for a Go-based service responsible for generating a one-page "Transformation Assessment Snapshot" PDF report from CapabiliSense assessment data.

## 2. Goals

*   Provide users with a concise, shareable PDF summary of their transformation assessment.
*   Incorporate both quantitative data (scores) and qualitative AI-generated insights.
*   Automate the generation of this report.
*   Lay the groundwork for a future Go-based backend for CapabiliSense.

## 3. Target Users

*   Consultants using CapabiliSense to deliver assessments to clients.
*   Internal stakeholders reviewing project maturity.

## 4. Functional Requirements

### 4.1. Report Content (as per target snapshot v1.0)
    *   **Header:**
        *   Organization Name (Dynamically extracted by AI from input documents/context).
        *   Report Title: "Transformation Assessment Snapshot".
        *   Assessment Date (from `AnalysisResults.start_time`).
        *   Document Evidence "As-of" Date (from assessment metadata).
    *   **Overall Maturity:**
        *   Score (Median of all leaf capability scores, e.g., "2.9 / 5.0").
        *   AI-generated summary statement (1-2 sentences).
    *   **Spider Chart (Placeholder for v1 Go service):**
        *   Textual representation of average scores for top-level domains (e.g., "Spider Chart Area (Scores: Strategic Vision - 3.5, ...)")
        *   Note indicating visual chart is for a future version.
    *   **Key Strengths:**
        *   Section Title.
        *   Top 3 highest-scoring top-level domains.
        *   For each strength: Domain Name, Average Score, AI-generated Insight.
    *   **Critical Areas for Focus:**
        *   Section Title.
        *   Bottom 3 lowest-scoring top-level domains.
        *   For each focus area: Domain Name, Average Score, AI-generated Insight.
    *   **AI Spotlight:**
        *   AI-generated Title (e.g., "CMDB & Central Repository").
        *   AI-generated Text: "Current Reality vs. Ambition" narrative.
    *   **ETS Solutions to Uplift (or equivalent "Recommended Actions"):**
        *   AI-generated Area Title (e.g., "Tech & Data Foundation").
        *   AI-generated Solution/Action Text.
    *   **Footer:**
        *   "Powered by CapabiliSense AI (c) [Current Year]"
        *   "Contact: [Configurable Email]"
        *   Page Number (if PDF generation supports it easily).

### 4.2. Data Input
    *   The service will require `project_id` and `run_id` to fetch necessary assessment data.
    *   It will consume `AnalysisResults` and `FrameworkContent` (or their Go equivalents fetched from the database/API).

### 4.3. AI Insight Generation
    *   Utilize a Go library for interacting with LLMs as defined in `prompts_library.json`.
    *   This library must support:
        *   Multiple LLM providers.
        *   Model selection and prioritization.
        *   Requesting and parsing **structured output** (JSON preferred) from LLMs.
        *   Fallback mechanisms (e.g., loading predefined XML/JSON content) on LLM failure, configured via `prompts_library.json`.
    *   Specific prompts will be designed for:
        *   Extracting Organization Name & Focus.
        *   Generating Overall Summary.
        *   Generating Domain Insights (Strengths & Focus).
        *   Generating AI Spotlight (Title & Text).
        *   Generating ETS Solutions/Recommendations (Area & Text).

### 4.4. PDF Generation (Initial Placeholder Approach)
    *   Generate a Markdown document dynamically using Go's `text/template` package and the enriched data (algorithmic + AI insights).
    *   **Current Decision:** Use the `github.com/mandolyte/mdtopdf/v2` library (or its CLI `md2pdf`) to convert the generated Markdown to PDF.
    *   **Styling:** For the initial implementation, rely on `mdtopdf`'s default "light" or "dark" theme. No complex custom styling or graphics (like the spider chart image).
    *   **Content:** All text must be ASCII-fied during Markdown generation to avoid font/encoding issues with `mdtopdf`'s default fonts (e.g., `©` -> `(c)`, `–` -> `-`, `✔` -> `*`).
    *   Output PDF as a byte stream.

### 4.5. Service Interface
    *   Expose an HTTP GET endpoint (e.g., `/generate-report?project_id=<X>&run_id=<Y>`).
    *   The endpoint shall return the PDF as a downloadable file (`application/pdf` with `Content-Disposition: attachment`).

## 5. Non-Functional Requirements

*   **Modularity:** Code should be organized into logical packages (config, data extraction, AI insights, PDF generation).
*   **Testability:** Structure should allow for unit testing of individual components.
*   **Configuration:** API keys, prompt paths, template paths should be configurable (e.g., via environment variables).
*   **Logging:** Comprehensive logging for debugging and monitoring.

## 6. Future Considerations (Out of Scope for Initial Placeholder)

*   **Visual Spider Chart:** Integration of a proper spider chart image into the PDF.
*   **Advanced PDF Styling & Layout:** Achieving a pixel-perfect layout matching the target snapshot, potentially using headless Chrome (`chromedp`) or a more capable PDF library if `mdtopdf` proves too limiting for styling.
*   **Full Unicode Support in PDF:** Implementing robust font management to correctly render all special characters.
*   **Error Handling:** More graceful error handling and user feedback for PDF generation failures.
*   **Caching:** Caching of generated reports or intermediate data.

## 7. Current Status & Decisions (as of 2025-05-23)

*   **PDF Library:** `mdtopdf` selected as an *initial placeholder* due to its pure Go nature.
*   **Current `mdtopdf` Issue:** Producing 0-byte PDFs even with simple, correctly parsed Markdown and default themes. Direct `fpdf` calls work, indicating the issue is within `mdtopdf`'s use of `fpdf` or its default styling for rendered Markdown elements.
*   **Mitigation:** For the placeholder, all Markdown content will be strictly ASCII. Complex Markdown features like tables are deferred. Focus is on getting *any* textual content into the PDF.
*   **Next Step for PDF:** If the current `mdtopdf` path remains problematic even for basic ASCII text, the immediate fallback for PDF generation will be to investigate generating HTML and using a headless browser (like `chromedp`) for conversion, as this offers better control over rendering and font handling.
