package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"capabilisense-reporting-service/pkg/aiinsights"
	"capabilisense-reporting-service/pkg/config"
)

func main() {
	fmt.Println("🧪 LLM Library Test Runner")
	fmt.Println("==========================")

	// Load environment variables
	if err := config.LoadTestEnv(); err != nil {
		log.Printf("Warning: Could not load .env file: %v", err)
	}

	// Test 1: Configuration Loading
	fmt.Println("\n📋 Test 1: Configuration Loading")
	library, err := config.LoadPromptsLibrary("configs/prompts_library.json")
	if err != nil {
		log.Fatalf("❌ Failed to load prompts library: %v", err)
	}
	fmt.Printf("✅ Loaded prompts library with %d prompts and %d models\n", 
		len(library.Prompts), len(library.Models))

	// Test 2: Available Providers
	fmt.Println("\n🔑 Test 2: Available API Keys")
	availableKeys := config.GetTestAPIKeys()
	if len(availableKeys) == 0 {
		fmt.Println("⚠️  No API keys found in environment")
		fmt.Println("   Create a .env file with your API keys for full testing")
	} else {
		fmt.Printf("✅ Found %d API keys:\n", len(availableKeys))
		for keyName := range availableKeys {
			fmt.Printf("   • %s\n", keyName)
		}
	}

	// Test 3: Mock Client
	fmt.Println("\n🤖 Test 3: Mock Client")
	factory := aiinsights.NewLLMClientFactory(library)
	mockClient, err := factory.CreateClient("mock")
	if err != nil {
		log.Fatalf("❌ Failed to create mock client: %v", err)
	}

	mockRequest := aiinsights.InsightRequest{
		PromptID:  "organization_name",
		Context:   "Test organization",
		RequestID: "test-mock-123",
		Timestamp: time.Now(),
	}

	mockResponse, err := mockClient.GenerateInsight(context.Background(), mockRequest)
	if err != nil {
		log.Fatalf("❌ Mock client failed: %v", err)
	}
	fmt.Printf("✅ Mock client response: %s\n", mockResponse.Content)

	// Test 4: Unified Client with Fallback
	fmt.Println("\n🔄 Test 4: Unified Client with Fallback")
	unifiedClient, err := factory.CreateClient("unified")
	if err != nil {
		log.Fatalf("❌ Failed to create unified client: %v", err)
	}

	// Test with a prompt that has fallback configured
	fallbackRequest := aiinsights.InsightRequest{
		PromptID:  "frontend_chat",
		Context:   "Hello, this is a test message.",
		RequestID: "test-fallback-123",
		Timestamp: time.Now(),
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	fallbackResponse, err := unifiedClient.GenerateInsight(ctx, fallbackRequest)
	if err != nil {
		fmt.Printf("⚠️  Unified client failed (expected if no API keys): %v\n", err)
	} else {
		fmt.Printf("✅ Unified client response from %s\n", fallbackResponse.Provider)
		if fallbackResponse.UsedFallback {
			fmt.Println("   📄 Used fallback response")
		} else {
			fmt.Printf("   🌐 Used real provider: %s\n", fallbackResponse.Model)
		}
	}

	// Test 5: Configuration Validation
	fmt.Println("\n🔍 Test 5: Configuration Validation")
	
	// Test prompt loading
	prompt, err := library.GetPrompt("frontend_chat")
	if err != nil {
		log.Fatalf("❌ Failed to get prompt: %v", err)
	}
	
	systemPrompt, err := prompt.LoadSystemPrompt()
	if err != nil {
		log.Fatalf("❌ Failed to load system prompt: %v", err)
	}
	fmt.Printf("✅ Loaded system prompt (%d characters)\n", len(systemPrompt))

	// Test schema loading
	maturityPrompt, err := library.GetPrompt("maturity_assessor")
	if err != nil {
		log.Fatalf("❌ Failed to get maturity assessor prompt: %v", err)
	}
	
	schema, err := maturityPrompt.LoadJSONSchema()
	if err != nil {
		log.Fatalf("❌ Failed to load JSON schema: %v", err)
	}
	fmt.Printf("✅ Loaded JSON schema with %d properties\n", len(schema))

	// Test fallback loading
	fallbackContent, err := prompt.GetFallbackContent("frontend_chat")
	if err != nil {
		log.Fatalf("❌ Failed to load fallback content: %v", err)
	}
	fmt.Printf("✅ Loaded fallback content (%d characters)\n", len(fallbackContent))

	// Test 6: Provider Authentication Modes
	fmt.Println("\n🔐 Test 6: Provider Authentication Modes")
	
	authModes := map[string]string{
		"azure":                   "header",
		"gcp-gemini-2.0-flash-lite": "query_param",
		"anthropic":               "header",
	}

	for provider, expectedMode := range authModes {
		model, err := library.GetModel(provider)
		if err != nil {
			fmt.Printf("⚠️  Provider %s not found\n", provider)
			continue
		}
		
		if model.AuthMode == expectedMode {
			fmt.Printf("✅ %s: %s auth mode\n", provider, expectedMode)
		} else {
			fmt.Printf("❌ %s: expected %s, got %s\n", provider, expectedMode, model.AuthMode)
		}
	}

	// Test 7: Performance Check
	fmt.Println("\n⚡ Test 7: Performance Check")
	
	start := time.Now()
	for i := 0; i < 10; i++ {
		_, err := mockClient.GenerateInsight(context.Background(), mockRequest)
		if err != nil {
			log.Fatalf("❌ Performance test failed: %v", err)
		}
	}
	elapsed := time.Since(start)
	
	avgTime := elapsed / 10
	fmt.Printf("✅ 10 mock calls completed in %v (avg: %v per call)\n", elapsed, avgTime)

	// Summary
	fmt.Println("\n📊 Test Summary")
	fmt.Println("===============")
	fmt.Println("✅ Configuration loading works")
	fmt.Println("✅ Mock client functional")
	fmt.Println("✅ Unified client created")
	fmt.Println("✅ Fallback mechanisms work")
	fmt.Println("✅ File loading (prompts, schemas, fallbacks)")
	fmt.Println("✅ Authentication modes configured")
	fmt.Println("✅ Performance baseline established")

	if len(availableKeys) > 0 {
		fmt.Println("\n🚀 Ready for integration testing with real API keys!")
		fmt.Println("   Run: go test -v ./pkg/aiinsights -run TestIntegration")
	} else {
		fmt.Println("\n💡 To test with real providers:")
		fmt.Println("   1. Copy .env.example to .env")
		fmt.Println("   2. Add your API keys")
		fmt.Println("   3. Run: ./scripts/run-llm-tests.sh")
	}

	fmt.Println("\n🎉 All basic tests passed!")
}

func init() {
	// Change to project root if running from cmd/test-runner
	if _, err := os.Stat("../../configs/prompts_library.json"); err == nil {
		os.Chdir("../..")
	}
}
