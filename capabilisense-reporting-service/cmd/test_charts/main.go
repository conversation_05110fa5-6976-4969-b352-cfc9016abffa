package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"capabilisense-reporting-service/pkg/charts"
	"capabilisense-reporting-service/pkg/dataextraction"
)

func main() {
	log.Println("🎨 Testing CapabiliSense Spider Chart Generation")

	// Create output directory
	outputDir := "test_charts"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Fatalf("Failed to create output directory: %v", err)
	}

	// Create chart generator
	generator := charts.NewChartGenerator(outputDir)

	// Test 1: Basic spider chart with sample data
	log.Println("📊 Test 1: Basic spider chart with sample data")
	testBasicSpiderChart(generator)

	// Test 2: Professional configuration
	log.Println("📊 Test 2: Professional configuration")
	testProfessionalChart(generator)

	// Test 3: High contrast configuration
	log.Println("📊 Test 3: High contrast configuration")
	testHighContrastChart(generator)

	// Test 4: Variable number of domains
	log.Println("📊 Test 4: Variable number of domains")
	testVariableDomains(generator)

	// Test 5: Integration with Stage A data structure
	log.Println("📊 Test 5: Integration with Stage A data")
	testStageAIntegration(generator)

	log.Println("✅ All spider chart tests completed successfully!")
	log.Printf("📁 Charts saved in: %s", outputDir)
}

func testBasicSpiderChart(generator *charts.ChartGenerator) {
	domains := []charts.DomainData{
		{Name: "Strategy", Score: 3.5},
		{Name: "Operations", Score: 2.8},
		{Name: "Technology", Score: 4.2},
		{Name: "People", Score: 3.1},
		{Name: "Governance", Score: 2.5},
	}

	outputPath, err := generator.GenerateCustomSpiderChart(
		domains,
		5,
		"Basic Maturity Assessment",
		"basic_spider_chart.png",
		nil, // Use default config
	)

	if err != nil {
		log.Printf("❌ Error generating basic chart: %v", err)
		return
	}

	log.Printf("✅ Basic chart generated: %s", outputPath)
}

func testProfessionalChart(generator *charts.ChartGenerator) {
	domains := []charts.DomainData{
		{Name: "Strategic Planning", Score: 4.1},
		{Name: "Process Management", Score: 3.7},
		{Name: "Technology Infrastructure", Score: 3.9},
		{Name: "Human Resources", Score: 3.2},
		{Name: "Risk Management", Score: 2.8},
		{Name: "Customer Experience", Score: 4.3},
	}

	config := charts.CreateProfessionalConfig()
	outputPath, err := generator.GenerateCustomSpiderChart(
		domains,
		5,
		"Professional Organizational Assessment",
		"professional_spider_chart.png",
		&config,
	)

	if err != nil {
		log.Printf("❌ Error generating professional chart: %v", err)
		return
	}

	log.Printf("✅ Professional chart generated: %s", outputPath)
}

func testHighContrastChart(generator *charts.ChartGenerator) {
	domains := []charts.DomainData{
		{Name: "Leadership", Score: 3.8},
		{Name: "Innovation", Score: 2.9},
		{Name: "Quality", Score: 4.0},
		{Name: "Efficiency", Score: 3.5},
	}

	config := charts.CreateHighContrastConfig()
	outputPath, err := generator.GenerateCustomSpiderChart(
		domains,
		5,
		"High Contrast Assessment Chart",
		"high_contrast_spider_chart.png",
		&config,
	)

	if err != nil {
		log.Printf("❌ Error generating high contrast chart: %v", err)
		return
	}

	log.Printf("✅ High contrast chart generated: %s", outputPath)
}

func testVariableDomains(generator *charts.ChartGenerator) {
	// Test with 3 domains
	domains3 := []charts.DomainData{
		{Name: "Core", Score: 4.2},
		{Name: "Support", Score: 3.1},
		{Name: "Innovation", Score: 2.7},
	}

	outputPath, err := generator.GenerateCustomSpiderChart(
		domains3,
		5,
		"Three Domain Assessment",
		"three_domain_chart.png",
		nil,
	)

	if err != nil {
		log.Printf("❌ Error generating 3-domain chart: %v", err)
	} else {
		log.Printf("✅ 3-domain chart generated: %s", outputPath)
	}

	// Test with 8 domains
	domains8 := []charts.DomainData{
		{Name: "Strategy", Score: 3.5},
		{Name: "Operations", Score: 2.8},
		{Name: "Technology", Score: 4.2},
		{Name: "People", Score: 3.1},
		{Name: "Governance", Score: 2.5},
		{Name: "Finance", Score: 3.8},
		{Name: "Marketing", Score: 3.3},
		{Name: "Innovation", Score: 2.9},
	}

	outputPath, err = generator.GenerateCustomSpiderChart(
		domains8,
		5,
		"Eight Domain Assessment",
		"eight_domain_chart.png",
		nil,
	)

	if err != nil {
		log.Printf("❌ Error generating 8-domain chart: %v", err)
	} else {
		log.Printf("✅ 8-domain chart generated: %s", outputPath)
	}
}

func testStageAIntegration(generator *charts.ChartGenerator) {
	// Create mock Stage A data
	stageAData := &dataextraction.StageAOutput{
		ReportMetadata: dataextraction.ReportMetadata{
			ProjectID:     "test-project",
			RunID:         "test-run-123",
			FrameworkName: "HR Transformation Assessment",
		},
		OverallMaturity: dataextraction.OverallMaturityData{
			Score:    3,
			ScaleMax: 5,
		},
		DomainScoresForSpiderChart: []dataextraction.DomainScore{
			{
				DomainName:      "Strategic Alignment",
				AverageScore:    3.2,
				CapabilityCount: 4,
			},
			{
				DomainName:      "Process Excellence",
				AverageScore:    2.8,
				CapabilityCount: 3,
			},
			{
				DomainName:      "Technology Integration",
				AverageScore:    4.1,
				CapabilityCount: 5,
			},
			{
				DomainName:      "People Development",
				AverageScore:    3.5,
				CapabilityCount: 4,
			},
		},
	}

	outputPath, err := generator.GenerateSpiderChartFromAssessment(
		stageAData,
		"stage_a_integration_chart.png",
	)

	if err != nil {
		log.Printf("❌ Error generating Stage A integration chart: %v", err)
		return
	}

	log.Printf("✅ Stage A integration chart generated: %s", outputPath)
}

// Additional utility functions for testing

func createSampleStageAData() *dataextraction.StageAOutput {
	return &dataextraction.StageAOutput{
		ReportMetadata: dataextraction.ReportMetadata{
			ProjectID:      "sample-project",
			RunID:          "run-" + fmt.Sprintf("%d", time.Now().Unix()),
			AssessmentDate: time.Now(),
			FrameworkName:  "Sample Assessment Framework",
		},
		OverallMaturity: dataextraction.OverallMaturityData{
			Score:    3,
			ScaleMax: 5,
		},
		DomainScoresForSpiderChart: []dataextraction.DomainScore{
			{DomainName: "Leadership", AverageScore: 3.8, CapabilityCount: 3},
			{DomainName: "Strategy", AverageScore: 3.2, CapabilityCount: 4},
			{DomainName: "Operations", AverageScore: 2.9, CapabilityCount: 5},
			{DomainName: "Technology", AverageScore: 4.1, CapabilityCount: 3},
			{DomainName: "People", AverageScore: 3.5, CapabilityCount: 4},
			{DomainName: "Culture", AverageScore: 2.7, CapabilityCount: 3},
		},
	}
}

func demonstrateValidation() {
	log.Println("🔍 Demonstrating chart data validation")

	// Valid data
	validData := charts.SpiderChartData{
		Domains: []charts.DomainData{
			{Name: "Domain1", Score: 3.5},
			{Name: "Domain2", Score: 2.8},
		},
		MaxLevel:   5,
		ChartTitle: "Valid Chart",
	}

	if err := charts.ValidateChartData(validData); err != nil {
		log.Printf("❌ Unexpected validation error: %v", err)
	} else {
		log.Printf("✅ Valid data passed validation")
	}

	// Invalid data - empty domains
	invalidData := charts.SpiderChartData{
		Domains:    []charts.DomainData{},
		MaxLevel:   5,
		ChartTitle: "Invalid Chart",
	}

	if err := charts.ValidateChartData(invalidData); err != nil {
		log.Printf("✅ Invalid data correctly rejected: %v", err)
	} else {
		log.Printf("❌ Invalid data incorrectly accepted")
	}
}
