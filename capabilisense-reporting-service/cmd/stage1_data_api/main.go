package main

import (
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"capabilisense-reporting-service/pkg/config"
	"capabilisense-reporting-service/pkg/dataextraction"
	"capabilisense-reporting-service/pkg/dataextraction/api"
)

func main() {
	// Set up logging
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("Starting CapabiliSense Stage A - Report Data Generation API")

	// Load configuration
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	log.Printf("Configuration loaded:")
	log.Printf("  Port: %s", cfg.Port)
	log.Printf("  Database Path: %s", cfg.DatabasePath)
	log.Printf("  Log Level: %s", cfg.LogLevel)

	// Initialize database repository
	log.Printf("Connecting to database: %s", cfg.DatabasePath)
	repo, err := dataextraction.NewDBRepository(cfg.DatabasePath)
	if err != nil {
		log.Fatalf("Failed to initialize database repository: %v", err)
	}
	defer func() {
		if err := repo.Close(); err != nil {
			log.Printf("Error closing database connection: %v", err)
		}
	}()
	log.Println("Database connection established")

	// Initialize service
	service := dataextraction.NewService(repo)
	log.Println("Service initialized")

	// Set up HTTP routes
	mux := api.SetupRoutes(service)

	// Add logging middleware
	handler := api.LoggingMiddleware(mux)

	// Create HTTP server
	server := &http.Server{
		Addr:         ":" + cfg.Port,
		Handler:      handler,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// Start server in a goroutine
	go func() {
		log.Printf("Starting HTTP server on port %s", cfg.Port)
		log.Printf("API endpoints:")
		log.Printf("  GET /api/v1/report-data?project_id=<id>&run_id=<optional>")
		log.Printf("  GET /health")
		log.Printf("  GET /status")
		log.Printf("  GET /")

		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Failed to start HTTP server: %v", err)
		}
	}()

	// Test database connectivity and show available data
	testDatabaseConnectivity(repo)

	log.Println("🚀 CapabiliSense Stage A API is running!")
	log.Printf("📊 Access the API at: http://localhost:%s", cfg.Port)
	log.Printf("🔍 Health check: http://localhost:%s/health", cfg.Port)
	log.Printf("📋 Status info: http://localhost:%s/status", cfg.Port)

	// Wait for interrupt signal to gracefully shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	log.Println("Shutting down server...")

	// Graceful shutdown would go here in a production implementation
	log.Println("Server stopped")
}

// testDatabaseConnectivity tests the database connection and shows available data
func testDatabaseConnectivity(repo *dataextraction.DBRepository) {
	log.Println("🔍 Testing database connectivity and showing available data...")

	// Test getting projects (we'll query analysis_runs to see what projects exist)
	// This is a simple test query to verify connectivity
	log.Println("📊 Available projects and runs:")

	// We can't easily query without knowing project IDs, so let's just log that the connection works
	log.Println("✅ Database connection test successful")
	log.Println("💡 To test the API, use a project_id from your database")
	log.Println("   Example: curl 'http://localhost:8081/api/v1/report-data?project_id=hr'")
	log.Println("   Example: curl 'http://localhost:8081/api/v1/report-data?project_id=hr&run_id=3df7959a-f2b6-483c-bc64-5dd6518aadca'")
}

// Example usage and testing information
func init() {
	// This could be used to set up any initialization logic
	// For now, we'll just ensure the program starts cleanly
}

/*
API Usage Examples:

1. Get latest report data for a project:
   GET /api/v1/report-data?project_id=hr

2. Get report data for a specific run:
   GET /api/v1/report-data?project_id=hr&run_id=3df7959a-f2b6-483c-bc64-5dd6518aadca

3. Health check:
   GET /health

4. Detailed status:
   GET /status

5. API info:
   GET /

Response format:
{
  "data": {
    "report_metadata": {
      "project_id": "hr",
      "run_id": "3df7959a-f2b6-483c-bc64-5dd6518aadca",
      "assessment_date": "2025-05-24T04:29:54.602428Z",
      "evidence_as_of_date": "2025-05-24",
      "framework_id": "fw-uuid-xyz",
      "framework_name": "Example Assessment Framework"
    },
    "overall_maturity": {
      "score": 2.9,
      "scale_max": 5.0
    },
    "domain_scores_for_spider_chart": [...],
    "key_strengths": [...],
    "critical_areas_focus": [...],
    "context_for_spotlight_and_solutions_ai": {...}
  },
  "timestamp": "2025-05-24T10:00:00Z",
  "request_id": ""
}

Error response format:
{
  "error": "Missing required parameter",
  "message": "project_id is required",
  "code": 400
}
*/
