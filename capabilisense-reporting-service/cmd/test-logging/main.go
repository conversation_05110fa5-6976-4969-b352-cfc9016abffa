package main

import (
	"bufio"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"capabilisense-reporting-service/pkg/aiinsights"
	"capabilisense-reporting-service/pkg/config"
)

func main() {
	fmt.Println("🔍 Testing LLM Logging Integration")
	fmt.Println("==================================")

	// Change to project root
	if _, err := os.Stat("../../configs/prompts_library.json"); err == nil {
		os.Chdir("../..")
	}

	// Load environment variables
	config.LoadTestEnv()

	// Load the prompts library
	library, err := config.LoadPromptsLibrary("configs/prompts_library.json")
	if err != nil {
		log.Fatalf("❌ Failed to load prompts library: %v", err)
	}
	fmt.Println("✅ Loaded prompts library")

	// Create LLM interface (this will initialize the logger)
	llm := aiinsights.NewLLMInterface(library)
	fmt.Println("✅ Created LLM interface with logger")

	// Test with a mock request that will fail and use fallback
	request := aiinsights.LLMRequest{
		PromptID:  "frontend_chat",
		UserQuery: "Hello, this is a test for logging!",
		RequestID: fmt.Sprintf("test-logging-%d", time.Now().Unix()),
	}

	fmt.Println("\n📝 Making LLM call (will likely use fallback)...")
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	response, err := llm.CallLLM(ctx, request)
	if err != nil {
		fmt.Printf("⚠️  LLM call failed (expected): %v\n", err)
	} else {
		fmt.Printf("✅ LLM call succeeded with provider: %s\n", response.Provider)
		fmt.Printf("   Used fallback: %t\n", response.UsedFallback)
		fmt.Printf("   Content length: %d characters\n", len(response.Content))
	}

	// Check if log file was created
	logFile := "logs/llm-calls.jsonl"
	if _, err := os.Stat(logFile); err == nil {
		fmt.Printf("\n📄 Log file created: %s\n", logFile)
		
		// Read and display log entries
		file, err := os.Open(logFile)
		if err != nil {
			log.Printf("❌ Failed to open log file: %v", err)
			return
		}
		defer file.Close()

		fmt.Println("\n📋 Log entries:")
		scanner := bufio.NewScanner(file)
		entryCount := 0
		
		for scanner.Scan() {
			entryCount++
			var entry map[string]interface{}
			if err := json.Unmarshal(scanner.Bytes(), &entry); err != nil {
				log.Printf("❌ Failed to parse log entry: %v", err)
				continue
			}

			// Display key information from the log entry
			eventType := entry["event_type"]
			timestamp := entry["timestamp"]
			provider := entry["provider_alias"]
			requestID := entry["request_id"]
			
			fmt.Printf("  %d. [%s] %s - Provider: %s, Request: %s\n", 
				entryCount, timestamp, eventType, provider, requestID)
			
			// Show additional details for different event types
			if eventType == "llm_request" {
				if payload, ok := entry["request_payload"].(map[string]interface{}); ok {
					fmt.Printf("      Request payload keys: ")
					for key := range payload {
						fmt.Printf("%s ", key)
					}
					fmt.Println()
				}
			} else if eventType == "llm_response" {
				if statusCode, ok := entry["response_status_code"].(float64); ok {
					fmt.Printf("      Status: %.0f", statusCode)
				}
				if tokensUsed, ok := entry["tokens_used"].(float64); ok {
					fmt.Printf(", Tokens: %.0f", tokensUsed)
				}
				if processingTime, ok := entry["processing_time"].(string); ok {
					fmt.Printf(", Time: %s", processingTime)
				}
				fmt.Println()
			} else if eventType == "llm_fallback" {
				if errorMsg, ok := entry["error_message"].(string); ok {
					fmt.Printf("      Error: %s\n", errorMsg)
				}
			} else if eventType == "llm_error" {
				if errorMsg, ok := entry["error_message"].(string); ok {
					fmt.Printf("      Error: %s\n", errorMsg)
				}
			}
		}

		fmt.Printf("\n📊 Total log entries: %d\n", entryCount)
		
		if entryCount > 0 {
			fmt.Println("✅ Logging is working correctly!")
		} else {
			fmt.Println("⚠️  No log entries found")
		}
	} else {
		fmt.Printf("⚠️  Log file not found: %s\n", logFile)
		fmt.Println("   This might be expected if the LLM call succeeded without errors")
	}

	fmt.Println("\n🎉 Logging test complete!")
	fmt.Println("\nThe LLM library now logs all calls to logs/llm-calls.jsonl with:")
	fmt.Println("  • Request details (payload, headers, timestamps)")
	fmt.Println("  • Response details (status, body, tokens, timing)")
	fmt.Println("  • Error information (failures, timeouts)")
	fmt.Println("  • Fallback usage (when all providers fail)")
	fmt.Println("  • Provider information (alias, URL, model)")
}
