package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"capabilisense-reporting-service/pkg/aiinsights"
	"capabilisense-reporting-service/pkg/dataextraction"
	"capabilisense-reporting-service/pkg/pdfgenerator"
)

// CombinedReportData represents the complete data for PDF generation
type CombinedReportData struct {
	StageA    dataextraction.StageAOutput `json:"stage_a"`
	StageB    aiinsights.StageBOutput     `json:"stage_b"`
	ChartPath string                      `json:"chart_path"`
}

func main() {
	log.Println("🎯 Generating AI Project PDF Report")

	// Load Stage A data
	stageAData, err := loadStageAData("mock_ai_stage_a.json")
	if err != nil {
		log.Fatalf("Failed to load Stage A data: %v", err)
	}
	log.Println("✅ Stage A data loaded")

	// Load Stage B data
	stageBData, err := loadStageBData("stage_b_ai.json")
	if err != nil {
		log.Fatalf("Failed to load Stage B data: %v", err)
	}
	log.Println("✅ Stage B data loaded")

	// Chart path
	chartPath := "generated_charts/ai_project_spider_chart.png"
	if _, err := os.Stat(chartPath); os.IsNotExist(err) {
		log.Printf("⚠️  Chart not found at %s, will generate PDF without chart", chartPath)
		chartPath = ""
	} else {
		log.Println("✅ Chart found")
	}

	// Create combined report data
	reportData := createReportData(stageAData, stageBData, chartPath)

	// Generate PDF
	outputPath := fmt.Sprintf("AI_Transformation_Assessment_%s.pdf", time.Now().Format("2006-01-02"))
	if err := generatePDF(reportData, outputPath); err != nil {
		log.Fatalf("Failed to generate PDF: %v", err)
	}

	log.Printf("🎉 PDF report generated successfully: %s", outputPath)
	
	// Print summary
	printReportSummary(reportData)
}

func loadStageAData(filename string) (*dataextraction.StageAOutput, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	var stageA dataextraction.StageAOutput
	if err := json.Unmarshal(data, &stageA); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return &stageA, nil
}

func loadStageBData(filename string) (*aiinsights.StageBOutput, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	var response struct {
		Data aiinsights.StageBOutput `json:"data"`
	}
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return &response.Data, nil
}

func createReportData(stageA *dataextraction.StageAOutput, stageB *aiinsights.StageBOutput, chartPath string) *pdfgenerator.ReportData {
	// Convert to the format expected by the PDF generator
	reportData := &pdfgenerator.ReportData{
		OrganizationName: stageB.OrganizationName.GeneratedName,
		AssessmentDate:   time.Now().Format("January 2, 2006"),
		FrameworkName:    stageA.ReportMetadata.FrameworkName,
		
		// Overall maturity
		OverallMaturityScore: fmt.Sprintf("%.1f", stageA.OverallMaturity.Score),
		MaturityLevel:        getMaturityLevel(stageA.OverallMaturity.Score),
		
		// Executive summary from Stage B
		ExecutiveSummary: stageB.BusinessSummary.ExecutiveSummary,
		KeyFindings:      stageB.BusinessSummary.KeyFindings,
		
		// Domain scores
		DomainScores: convertDomainScores(stageA.DomainScoresForSpiderChart),
		
		// Strengths and weaknesses
		KeyStrengths:   convertStrengths(stageB.StrengthDomains),
		CriticalAreas:  convertWeaknesses(stageB.WeaknessDomains),
		
		// AI Spotlight
		AISpotlight: pdfgenerator.AISpotlight{
			Title:       stageB.AISpotlight.Title,
			Content:     stageB.AISpotlight.Content,
			KeyInsights: stageB.AISpotlight.KeyInsights,
		},
		
		// Focus Area (replaces ETS Solutions)
		FocusArea: pdfgenerator.FocusArea{
			Title:           stageB.FocusArea.Title,
			Description:     stageB.FocusArea.Description,
			Recommendations: stageB.FocusArea.Recommendations,
		},
		
		// Chart path
		ChartImagePath: chartPath,
	}

	return reportData
}

func convertDomainScores(domains []dataextraction.DomainScore) []pdfgenerator.DomainScore {
	var scores []pdfgenerator.DomainScore
	for _, domain := range domains {
		scores = append(scores, pdfgenerator.DomainScore{
			DomainName: domain.DomainName,
			Score:      domain.AverageScore,
			Level:      getMaturityLevel(domain.AverageScore),
		})
	}
	return scores
}

func convertStrengths(strengths []aiinsights.StrengthDomain) []pdfgenerator.KeyStrength {
	var result []pdfgenerator.KeyStrength
	for _, strength := range strengths {
		result = append(result, pdfgenerator.KeyStrength{
			DomainName:  strength.DomainName,
			Score:       strength.AverageScore,
			Description: strength.KeyInsights,
			Impact:      strength.BusinessImpact,
		})
	}
	return result
}

func convertWeaknesses(weaknesses []aiinsights.WeaknessDomain) []pdfgenerator.CriticalArea {
	var result []pdfgenerator.CriticalArea
	for _, weakness := range weaknesses {
		result = append(result, pdfgenerator.CriticalArea{
			DomainName:      weakness.DomainName,
			Score:           weakness.AverageScore,
			Description:     weakness.KeyChallenges,
			Recommendations: weakness.ImprovementRecommendations,
		})
	}
	return result
}

func getMaturityLevel(score float64) string {
	switch {
	case score >= 4.5:
		return "Optimized"
	case score >= 3.5:
		return "Managed"
	case score >= 2.5:
		return "Defined"
	case score >= 1.5:
		return "Repeatable"
	default:
		return "Initial"
	}
}

func generatePDF(reportData *pdfgenerator.ReportData, outputPath string) error {
	// Use the simple PDF renderer
	renderer := pdfgenerator.NewSimpleRenderer()
	
	// Generate PDF
	pdfBytes, err := renderer.GeneratePDF(reportData)
	if err != nil {
		return fmt.Errorf("failed to generate PDF: %w", err)
	}

	// Write to file
	if err := os.WriteFile(outputPath, pdfBytes, 0644); err != nil {
		return fmt.Errorf("failed to write PDF file: %w", err)
	}

	return nil
}

func printReportSummary(reportData *pdfgenerator.ReportData) {
	log.Println("\n📊 AI Project Report Summary:")
	log.Printf("   Organization: %s", reportData.OrganizationName)
	log.Printf("   Framework: %s", reportData.FrameworkName)
	log.Printf("   Overall Maturity: %s (%s)", reportData.OverallMaturityScore, reportData.MaturityLevel)
	log.Printf("   Domain Count: %d", len(reportData.DomainScores))
	log.Printf("   Key Strengths: %d", len(reportData.KeyStrengths))
	log.Printf("   Critical Areas: %d", len(reportData.CriticalAreas))
	log.Printf("   Chart Included: %t", reportData.ChartImagePath != "")
	
	log.Println("\n🎯 Top Domains:")
	for i, domain := range reportData.DomainScores {
		if i < 3 { // Show top 3
			log.Printf("   %d. %s: %.1f (%s)", i+1, domain.DomainName, domain.Score, domain.Level)
		}
	}
	
	log.Println("\n🔍 Focus Area:")
	log.Printf("   Title: %s", reportData.FocusArea.Title)
	log.Printf("   Recommendations: %d", len(reportData.FocusArea.Recommendations))
}
