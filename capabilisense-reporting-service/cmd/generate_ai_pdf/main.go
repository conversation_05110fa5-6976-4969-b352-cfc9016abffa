package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"capabilisense-reporting-service/pkg/aiinsights"
	"capabilisense-reporting-service/pkg/dataextraction"
	"capabilisense-reporting-service/pkg/models"
	"capabilisense-reporting-service/pkg/pdfgenerator"
)

// CombinedReportData represents the complete data for PDF generation
type CombinedReportData struct {
	StageA    dataextraction.StageAOutput `json:"stage_a"`
	StageB    aiinsights.StageBOutput     `json:"stage_b"`
	ChartPath string                      `json:"chart_path"`
}

func main() {
	log.Println("🎯 Generating AI Project PDF Report")

	// Load Stage A data
	stageAData, err := loadStageAData("mock_ai_stage_a.json")
	if err != nil {
		log.Fatalf("Failed to load Stage A data: %v", err)
	}
	log.Println("✅ Stage A data loaded")

	// Load Stage B data
	stageBData, err := loadStageBData("stage_b_ai.json")
	if err != nil {
		log.Fatalf("Failed to load Stage B data: %v", err)
	}
	log.Println("✅ Stage B data loaded")

	// Chart path
	chartPath := "generated_charts/ai_project_spider_chart.png"
	if _, err := os.Stat(chartPath); os.IsNotExist(err) {
		log.Printf("⚠️  Chart not found at %s, will generate PDF without chart", chartPath)
		chartPath = ""
	} else {
		log.Println("✅ Chart found")
	}

	// Create combined report data
	reportData := createReportData(stageAData, stageBData, chartPath)

	// Generate PDF
	outputPath := fmt.Sprintf("AI_Transformation_Assessment_%s.pdf", time.Now().Format("2006-01-02"))
	if err := generatePDF(reportData, outputPath); err != nil {
		log.Fatalf("Failed to generate PDF: %v", err)
	}

	log.Printf("🎉 PDF report generated successfully: %s", outputPath)

	// Print summary
	printReportSummary(reportData)
}

func loadStageAData(filename string) (*dataextraction.StageAOutput, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	var stageA dataextraction.StageAOutput
	if err := json.Unmarshal(data, &stageA); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return &stageA, nil
}

func loadStageBData(filename string) (*aiinsights.StageBOutput, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	var response struct {
		Data aiinsights.StageBOutput `json:"data"`
	}
	if err := json.Unmarshal(data, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return &response.Data, nil
}

func createReportData(stageA *dataextraction.StageAOutput, stageB *aiinsights.StageBOutput, chartPath string) *models.ReportData {
	// Convert to the format expected by the PDF generator
	reportData := &models.ReportData{
		OrganizationName:        stageB.OrganizationName.GeneratedName,
		AssessmentDate:          time.Now().Format("January 2, 2006"),
		EvidenceAsOfDate:        stageA.ReportMetadata.EvidenceAsOfDate,
		OverallMaturityScore:    stageA.OverallMaturity.Score,
		OverallSummaryStatement: stageB.BusinessSummary.ExecutiveSummary,

		// Convert domain scores for spider chart
		SpiderChartData: convertDomainScores(stageA.DomainScoresForSpiderChart),

		// Convert strengths and weaknesses
		KeyStrengths:       convertStrengths(stageB.StrengthDomains),
		CriticalAreasFocus: convertWeaknesses(stageB.WeaknessDomains),

		// AI Spotlight
		AISpotlightTitle: stageB.AISpotlight.SpotlightTitle,
		AISpotlightText:  stageB.AISpotlight.Analysis,

		// Focus Area (replaces ETS Solutions)
		ETSSolutions: convertFocusAreaToSolutions(stageB.FocusArea),

		CurrentYear:  time.Now().Year(),
		ContactEmail: "<EMAIL>",
	}

	// Note: chartPath is available for future use if needed
	_ = chartPath

	return reportData
}

func convertDomainScores(domains []dataextraction.DomainScore) []models.DomainScoreData {
	var scores []models.DomainScoreData
	for _, domain := range domains {
		scores = append(scores, models.DomainScoreData{
			DomainName: domain.DomainName,
			Score:      domain.AverageScore,
		})
	}
	return scores
}

func convertStrengths(strengths []aiinsights.DomainInsight) []models.StrengthFocusData {
	var result []models.StrengthFocusData
	for _, strength := range strengths {
		// Create AI insight text from the domain insight
		insightText := strength.CurrentState
		if len(strength.KeyStrengths) > 0 {
			insightText += " Key strengths: " + strings.Join(strength.KeyStrengths, ", ")
		}

		result = append(result, models.StrengthFocusData{
			DomainName:    strength.DomainName,
			Score:         3.5, // Default score since DomainInsight doesn't have score
			AIInsightText: insightText,
		})
	}
	return result
}

func convertWeaknesses(weaknesses []aiinsights.DomainInsight) []models.StrengthFocusData {
	var result []models.StrengthFocusData
	for _, weakness := range weaknesses {
		// Create AI insight text from the domain insight
		insightText := weakness.CurrentState
		if len(weakness.ImprovementAreas) > 0 {
			insightText += " Areas for improvement: " + strings.Join(weakness.ImprovementAreas, ", ")
		}

		result = append(result, models.StrengthFocusData{
			DomainName:    weakness.DomainName,
			Score:         2.0, // Default low score for weaknesses
			AIInsightText: insightText,
		})
	}
	return result
}

func convertFocusAreaToSolutions(focusArea aiinsights.FocusAreaInsight) []models.SolutionData {
	var solutions []models.SolutionData

	// Create a main solution from the focus area
	solutions = append(solutions, models.SolutionData{
		AreaTitle:    focusArea.FocusTitle,
		SolutionText: focusArea.FocusDescription,
	})

	// Add recommendations as additional solutions
	for i, recommendation := range focusArea.KeyRecommendations {
		if i < 2 { // Limit to 2 additional recommendations
			solutions = append(solutions, models.SolutionData{
				AreaTitle:    fmt.Sprintf("Recommendation %d", i+1),
				SolutionText: recommendation,
			})
		}
	}

	return solutions
}

func generatePDF(reportData *models.ReportData, outputPath string) error {
	// Use the simple PDF renderer
	renderer := pdfgenerator.NewSimplePDFRenderer(
		"assessment_report_simplified.md",
		reportData.OrganizationName+" Transformation Assessment",
		"CapabiliSense AI",
		true,
		"portrait",
		"A4",
	)

	// Generate PDF
	pdfBytes, err := renderer.Render(*reportData)
	if err != nil {
		return fmt.Errorf("failed to generate PDF: %w", err)
	}

	// Write to file
	if err := os.WriteFile(outputPath, pdfBytes, 0644); err != nil {
		return fmt.Errorf("failed to write PDF file: %w", err)
	}

	return nil
}

func printReportSummary(reportData *models.ReportData) {
	log.Println("\n📊 AI Project Report Summary:")
	log.Printf("   Organization: %s", reportData.OrganizationName)
	log.Printf("   Assessment Date: %s", reportData.AssessmentDate)
	log.Printf("   Overall Maturity: %.1f", reportData.OverallMaturityScore)
	log.Printf("   Domain Count: %d", len(reportData.SpiderChartData))
	log.Printf("   Key Strengths: %d", len(reportData.KeyStrengths))
	log.Printf("   Critical Areas: %d", len(reportData.CriticalAreasFocus))

	log.Println("\n🎯 Top Domains:")
	for i, domain := range reportData.SpiderChartData {
		if i < 3 { // Show top 3
			log.Printf("   %d. %s: %.1f", i+1, domain.DomainName, domain.Score)
		}
	}

	log.Println("\n🔍 AI Spotlight:")
	log.Printf("   Title: %s", reportData.AISpotlightTitle)

	log.Println("\n💡 Solutions:")
	log.Printf("   Solutions Count: %d", len(reportData.ETSSolutions))
}
