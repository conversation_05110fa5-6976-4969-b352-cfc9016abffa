package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"capabilisense-reporting-service/pkg/aiinsights"
	"capabilisense-reporting-service/pkg/config"
)

func main() {
	fmt.Println("=== LLM Library Integration Example ===")

	// Load the prompts library
	library, err := config.LoadPromptsLibrary("../../configs/prompts_library.json")
	if err != nil {
		log.Fatalf("Failed to load prompts library: %v", err)
	}

	// Create LLM client factory
	factory := aiinsights.NewLLMClientFactory(library)

	// Test with the new unified client
	fmt.Println("\n--- Testing Unified LLM Client ---")
	unifiedClient, err := factory.CreateClient("unified")
	if err != nil {
		log.Fatalf("Failed to create unified client: %v", err)
	}

	// Validate configuration
	if err := unifiedClient.ValidateConfiguration(); err != nil {
		log.Printf("Configuration validation failed: %v", err)
	} else {
		fmt.Println("✓ Configuration validation passed")
	}

	// Test basic insight generation
	request := aiinsights.InsightRequest{
		PromptID:  "frontend_chat",
		Context:   "Hello, can you help me understand digital transformation?",
		InputData: map[string]interface{}{
			"user_type": "business_analyst",
			"domain":    "digital_transformation",
		},
		RequestID: fmt.Sprintf("test-%d", time.Now().Unix()),
		Timestamp: time.Now(),
	}

	ctx := context.Background()
	response, err := unifiedClient.GenerateInsight(ctx, request)
	if err != nil {
		log.Printf("Insight generation failed: %v", err)
	} else {
		fmt.Printf("✓ Insight generated successfully\n")
		fmt.Printf("  Provider: %s\n", response.Provider)
		fmt.Printf("  Model: %s\n", response.Model)
		fmt.Printf("  Content: %.100s...\n", response.Content)
		fmt.Printf("  Tokens used: %d\n", response.TokensUsed)
		fmt.Printf("  Processing time: %v\n", response.ProcessingTime)
		fmt.Printf("  Used fallback: %t\n", response.UsedFallback)
	}

	// Test structured output with maturity assessor
	fmt.Println("\n--- Testing Structured Output ---")
	structuredRequest := aiinsights.InsightRequest{
		PromptID: "maturity_assessor",
		Context:  "Assess the digital transformation maturity of an organization with strong leadership commitment but weak data governance practices and limited process automation.",
		InputData: map[string]interface{}{
			"organization": "TechCorp Inc.",
			"domains": []string{
				"strategic_vision",
				"data_governance", 
				"technology_infrastructure",
				"process_automation",
				"culture_change",
			},
		},
		RequestID: fmt.Sprintf("assessment-%d", time.Now().Unix()),
		Timestamp: time.Now(),
	}

	structuredResponse, err := unifiedClient.GenerateInsight(ctx, structuredRequest)
	if err != nil {
		log.Printf("Structured insight generation failed: %v", err)
	} else {
		fmt.Printf("✓ Structured insight generated successfully\n")
		fmt.Printf("  Provider: %s\n", structuredResponse.Provider)
		fmt.Printf("  Model: %s\n", structuredResponse.Model)
		fmt.Printf("  Content: %.100s...\n", structuredResponse.Content)
		fmt.Printf("  Structured data available: %t\n", structuredResponse.StructuredData != nil)
		fmt.Printf("  Used fallback: %t\n", structuredResponse.UsedFallback)
		
		if structuredResponse.StructuredData != nil {
			fmt.Printf("  Structured data keys: ")
			for key := range structuredResponse.StructuredData {
				fmt.Printf("%s ", key)
			}
			fmt.Println()
		}
	}

	// Test fallback behavior (simulate all providers failing)
	fmt.Println("\n--- Testing Fallback Behavior ---")
	fmt.Println("Note: This would normally test with invalid API keys to trigger fallbacks")
	fmt.Println("In a real scenario, the system would:")
	fmt.Println("1. Try each provider in the configured order")
	fmt.Println("2. Fall back to the configured fallback file if all providers fail")
	fmt.Println("3. Return appropriate error messages if no fallback is configured")

	// Show supported models
	fmt.Println("\n--- Supported Models ---")
	models := unifiedClient.GetSupportedModels()
	fmt.Printf("Available model aliases: %v\n", models)

	// Compare with mock client
	fmt.Println("\n--- Comparing with Mock Client ---")
	mockClient, err := factory.CreateClient("mock")
	if err != nil {
		log.Printf("Failed to create mock client: %v", err)
	} else {
		mockResponse, err := mockClient.GenerateInsight(ctx, request)
		if err != nil {
			log.Printf("Mock insight generation failed: %v", err)
		} else {
			fmt.Printf("✓ Mock insight generated\n")
			fmt.Printf("  Provider: %s\n", mockResponse.Provider)
			fmt.Printf("  Content: %.100s...\n", mockResponse.Content)
		}
	}

	fmt.Println("\n=== Integration Example Complete ===")
}
