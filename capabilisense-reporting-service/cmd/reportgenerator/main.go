package main

import (
	"fmt"
	"log"
	"net/http"
	"strings"

	"capabilisense-reporting-service/pkg/models"       // Adjust module path if different
	"capabilisense-reporting-service/pkg/pdfgenerator" // Adjust module path if different
)

// getMockReportData provides sample data for the report.
func getMockReportData() models.ReportData {
	return models.ReportData{
		OrganizationName:        "MockCorp Inc.",
		AssessmentDate:          "January 15, 2024",
		EvidenceAsOfDate:        "January 10, 2024",
		OverallMaturityScore:    3.1,
		OverallSummaryStatement: "MockCorp shows good progress in several areas but needs to focus on data strategy and process automation.",
		SpiderChartData: []models.DomainScoreData{
			{DomainName: "Strategic Vision", Score: 4.0},
			{DomainName: "People & Culture", Score: 3.5},
			{DomainName: "Process Excellence", Score: 2.5},
			{DomainName: "Technology", Score: 2.2},
		},
		KeyStrengths: []models.StrengthFocusData{
			{DomainName: "Strategic Vision", Score: 4.0, AIInsightText: "Clear vision and strong leadership buy-in observed."},
			{DomainName: "People & Culture", Score: 3.5, AIInsightText: "Effective programs for upskilling and talent retention."},
		},
		CriticalAreasFocus: []models.StrengthFocusData{
			{DomainName: "Technology", Score: 2.2, AIInsightText: "Data quality and governance practices require significant improvement. Issues with data silos."},
			{DomainName: "Process Excellence", Score: 2.5, AIInsightText: "Many manual processes leading to inefficiencies and potential errors."},
		},
		AISpotlightTitle: "Data Silos Impacting Agility",
		AISpotlightText:  "Current data architecture has led to significant data silos, hampering cross-functional collaboration and timely decision-making. Addressing this is key for future agility.",
		ETSSolutions: []models.SolutionData{
			{AreaTitle: "Data Strategy", SolutionText: "Develop and implement a unified data governance framework and explore data mesh concepts."},
			{AreaTitle: "Process Automation", SolutionText: "Implement robotic process automation (RPA) for routine tasks to reduce manual effort and errors."},
		},
		CurrentYear:  2024,
		ContactEmail: "<EMAIL>",
	}

	// --- Previous fuller mock data (keep for when simple MD works) ---
	/*
			return models.ReportData{
				OrganizationName:        "MockCorp Inc.",
				AssessmentDate:          time.Now().Format("January 2, 2006"),
				EvidenceAsOfDate:        time.Now().AddDate(0, 0, -5).Format("January 2, 2006"),
				OverallMaturityScore:    3.1,
				OverallSummaryStatement: "MockCorp shows good progress in several areas but needs to focus on data strategy and process automation.",
				SpiderChartData: []models.DomainScoreData{
					{DomainName: "Vision", Score: 4.0},
					{DomainName: "People", Score: 3.5},
					{DomainName: "Process", Score: 2.5},
					{DomainName: "Tech", Score: 2.2},
				},
				KeyStrengths: []models.StrengthFocusData{
					{DomainName: "Strategic Vision", Score: 4.0, AIInsightText: "Clear vision and strong leadership buy-in observed."},
					{DomainName: "Talent Development", Score: 3.5, AIInsightText: "Effective programs for upskilling and talent retention."},
				},
				CriticalAreasFocus: []models.StrengthFocusData{
					{DomainName: "Data Governance", Score: 2.2, AIInsightText: "Data quality and governance practices require significant improvement. Issues with data silos."},
					{DomainName: "Process Automation", Score: 2.5, AIInsightText: "Many manual processes leading to inefficiencies and potential errors."},
				},
				AISpotlightTitle:   "Data Silos Impacting Agility",
				AISpotlightText:    "Current data architecture has led to significant data silos, hampering cross-functional collaboration and timely decision-making. Addressing this is key for future agility.",
				ETSSolutions:       []models.SolutionData{
		            {AreaTitle: "Data Strategy", SolutionText: "Develop and implement a unified data governance framework and explore data mesh concepts."},
		        },
				CurrentYear:        time.Now().Year(),
				ContactEmail:       "<EMAIL>",
			}
	*/
}

// reportHandler is the HTTP handler for generating reports
func reportHandler(w http.ResponseWriter, r *http.Request) {
	log.Println("Received request for /generate-report")

	reportData := getMockReportData()

	// Try the simple renderer first (more reliable)
	renderer := pdfgenerator.NewSimplePDFRenderer(
		"assessment_report_simplified.md",
		reportData.OrganizationName+" Transformation Snapshot",
		"CapabiliSense AI",
		true,
		"portrait",
		"A4",
	)

	log.Println("Calling renderer.Render() for HTTP request...")
	pdfBytes, err := renderer.Render(reportData)
	if err != nil {
		log.Printf("Error generating PDF for request: %v", err)
		http.Error(w, "Failed to generate report", http.StatusInternalServerError)
		return
	}

	safeOrgName := strings.ReplaceAll(reportData.OrganizationName, " ", "_")
	safeOrgName = strings.ReplaceAll(safeOrgName, "/", "_")
	filename := fmt.Sprintf("%s_Transformation_Snapshot.pdf", safeOrgName)
	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(pdfBytes)))

	_, err = w.Write(pdfBytes)
	if err != nil {
		log.Printf("Error writing PDF bytes to response: %v", err)
	}
	log.Printf("Successfully sent PDF: %s (Size: %d bytes)", filename, len(pdfBytes))
}

// mdtopdfHandler uses the mdtopdf renderer for comparison
func mdtopdfHandler(w http.ResponseWriter, _ *http.Request) {
	log.Println("Received request for /generate-report-mdtopdf")

	reportData := getMockReportData()

	renderer := pdfgenerator.NewMDToPDFRenderer(
		"assessment_report_simplified.md",
		"light",
		"portrait",
		"A4",
		"mdtopdf_render.log",
		reportData.OrganizationName+" Transformation Snapshot (mdtopdf)",
		"CapabiliSense AI",
		true,
	)

	log.Println("Calling mdtopdf renderer.Render() for HTTP request...")
	pdfBytes, err := renderer.Render(reportData)
	if err != nil {
		log.Printf("Error generating PDF with mdtopdf: %v", err)
		http.Error(w, "Failed to generate report with mdtopdf", http.StatusInternalServerError)
		return
	}

	safeOrgName := strings.ReplaceAll(reportData.OrganizationName, " ", "_")
	safeOrgName = strings.ReplaceAll(safeOrgName, "/", "_")
	filename := fmt.Sprintf("%s_Transformation_Snapshot_mdtopdf.pdf", safeOrgName)
	w.Header().Set("Content-Type", "application/pdf")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=\"%s\"", filename))
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(pdfBytes)))

	_, err = w.Write(pdfBytes)
	if err != nil {
		log.Printf("Error writing PDF bytes to response: %v", err)
	}
	log.Printf("Successfully sent mdtopdf PDF: %s (Size: %d bytes)", filename, len(pdfBytes))
}

func main() {
	log.Println("Report Generator Service Starting...")

	http.HandleFunc("/generate-report", reportHandler)
	http.HandleFunc("/generate-report-mdtopdf", mdtopdfHandler)

	port := "8081"
	log.Printf("CapabiliSense PDF Reporting Service v0.1.0")
	log.Printf("Listening on port %s", port)
	log.Printf("📄 Simple PDF renderer (PRIMARY): http://localhost:%s/generate-report", port)
	log.Printf("🔧 mdtopdf renderer (DEBUG): http://localhost:%s/generate-report-mdtopdf", port)
	log.Printf("📚 Documentation: See README.md and docs/ directory")
	err := http.ListenAndServe(":"+port, nil)
	if err != nil {
		log.Fatalf("Failed to start HTTP server: %v", err)
	}
}
