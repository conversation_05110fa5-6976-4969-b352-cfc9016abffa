package main

import (
	"fmt"
	"log"
	"os"

	"capabilisense-reporting-service/pkg/config"
)

func main() {
	fmt.Println("🔍 Testing JSON Schema Loading Fix")
	fmt.Println("==================================")

	// Load the prompts library
	library, err := config.LoadPromptsLibrary("configs/prompts_library.json")
	if err != nil {
		log.Fatalf("❌ Failed to load prompts library: %v", err)
	}

	// Get the maturity assessor prompt
	prompt, err := library.GetPrompt("maturity_assessor")
	if err != nil {
		log.Fatalf("❌ Failed to get maturity assessor prompt: %v", err)
	}

	// Test loading JSON schema
	schema, err := prompt.LoadJSONSchema()
	if err != nil {
		log.Fatalf("❌ Failed to load JSON schema: %v", err)
	}

	fmt.Printf("✅ Loaded JSON schema successfully\n")
	fmt.Printf("Schema has %d top-level keys\n", len(schema))

	// Check for properties
	if properties, ok := schema["properties"].(map[string]interface{}); ok {
		fmt.Printf("✅ Found 'properties' key with %d properties\n", len(properties))
		
		// Check for overall_maturity_level
		if _, exists := properties["overall_maturity_level"]; exists {
			fmt.Printf("✅ Found 'overall_maturity_level' in properties\n")
		} else {
			fmt.Printf("❌ 'overall_maturity_level' not found in properties\n")
			fmt.Printf("Available properties: ")
			for key := range properties {
				fmt.Printf("%s ", key)
			}
			fmt.Println()
		}
	} else {
		fmt.Printf("❌ 'properties' key not found or not a map\n")
		fmt.Printf("Top-level keys: ")
		for key := range schema {
			fmt.Printf("%s ", key)
		}
		fmt.Println()
	}

	// Check for required fields
	if required, ok := schema["required"].([]interface{}); ok {
		fmt.Printf("✅ Found 'required' array with %d items\n", len(required))
	} else {
		fmt.Printf("⚠️  'required' key not found or not an array\n")
	}

	// Check for type
	if schemaType, ok := schema["type"].(string); ok {
		fmt.Printf("✅ Schema type: %s\n", schemaType)
	} else {
		fmt.Printf("⚠️  'type' key not found or not a string\n")
	}

	fmt.Println("\n🎉 JSON Schema test complete!")
}

func init() {
	// Change to project root if running from cmd/test-schema-fix
	if _, err := os.Stat("../../configs/prompts_library.json"); err == nil {
		os.Chdir("../..")
	}
}
