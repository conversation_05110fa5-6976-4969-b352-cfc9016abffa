package main

import (
	"fmt"
	"log"
	"os"

	"capabilisense-reporting-service/pkg/config"
)

func main() {
	fmt.Println("🔍 Testing Path Resolution")
	fmt.Println("==========================")

	// Test from different working directories
	originalDir, _ := os.Getwd()
	fmt.Printf("Current directory: %s\n", originalDir)

	// Test 1: Load from current directory
	fmt.Println("\n📋 Test 1: Loading from current directory")
	library, err := config.LoadPromptsLibrary("configs/prompts_library.json")
	if err != nil {
		log.Printf("❌ Failed to load from current dir: %v", err)
	} else {
		fmt.Printf("✅ Loaded library with %d prompts\n", len(library.Prompts))
	}

	// Test 2: Change to pkg/aiinsights and try loading
	fmt.Println("\n📋 Test 2: Loading from pkg/aiinsights directory")
	err = os.Chdir("pkg/aiinsights")
	if err != nil {
		log.Printf("❌ Failed to change directory: %v", err)
		return
	}

	currentDir, _ := os.Getwd()
	fmt.Printf("Changed to directory: %s\n", currentDir)

	library2, err := config.LoadPromptsLibrary("configs/prompts_library.json")
	if err != nil {
		log.Printf("❌ Failed to load from pkg/aiinsights: %v", err)
	} else {
		fmt.Printf("✅ Loaded library with %d prompts\n", len(library2.Prompts))
	}

	// Test 3: Test prompt file loading
	if library2 != nil {
		fmt.Println("\n📄 Test 3: Loading prompt files")
		
		prompt, err := library2.GetPrompt("frontend_chat")
		if err != nil {
			log.Printf("❌ Failed to get prompt: %v", err)
		} else {
			systemPrompt, err := prompt.LoadSystemPrompt()
			if err != nil {
				log.Printf("❌ Failed to load system prompt: %v", err)
			} else {
				fmt.Printf("✅ Loaded system prompt (%d characters)\n", len(systemPrompt))
			}

			fallback, err := prompt.GetFallbackContent("frontend_chat")
			if err != nil {
				log.Printf("❌ Failed to load fallback: %v", err)
			} else {
				fmt.Printf("✅ Loaded fallback content (%d characters)\n", len(fallback))
			}
		}

		// Test schema loading
		maturityPrompt, err := library2.GetPrompt("maturity_assessor")
		if err != nil {
			log.Printf("❌ Failed to get maturity prompt: %v", err)
		} else {
			schema, err := maturityPrompt.LoadJSONSchema()
			if err != nil {
				log.Printf("❌ Failed to load schema: %v", err)
			} else {
				fmt.Printf("✅ Loaded JSON schema with %d properties\n", len(schema))
			}
		}
	}

	// Restore original directory
	os.Chdir(originalDir)

	fmt.Println("\n🎉 Path resolution test complete!")
}

func init() {
	// Change to project root if running from cmd/test-path-resolution
	if _, err := os.Stat("../../configs/prompts_library.json"); err == nil {
		os.Chdir("../..")
	}
}
