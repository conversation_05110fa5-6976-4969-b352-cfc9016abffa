package main

import (
	"fmt"
	"log"
	"os"

	"capabilisense-reporting-service/pkg/config"
)

func main() {
	fmt.Println("🔧 Verifying JSON Schema Test Fix")
	fmt.Println("==================================")

	// Change to project root
	if _, err := os.Stat("../../configs/prompts_library.json"); err == nil {
		os.Chdir("../..")
	}

	// Load the prompts library
	library, err := config.LoadPromptsLibrary("configs/prompts_library.json")
	if err != nil {
		log.Fatalf("❌ Failed to load prompts library: %v", err)
	}
	fmt.Println("✅ Loaded prompts library")

	// Get the maturity assessor prompt
	prompt, err := library.GetPrompt("maturity_assessor")
	if err != nil {
		log.Fatalf("❌ Failed to get maturity assessor prompt: %v", err)
	}
	fmt.Println("✅ Got maturity assessor prompt")

	// Test loading JSON schema
	schema, err := prompt.LoadJSONSchema()
	if err != nil {
		log.Fatalf("❌ Failed to load JSON schema: %v", err)
	}
	fmt.Println("✅ Loaded JSON schema")

	// Test the assertions that were failing
	fmt.Println("\n🧪 Testing assertions...")

	// Check schema contains "properties"
	if _, exists := schema["properties"]; exists {
		fmt.Println("✅ Schema contains 'properties' key")
	} else {
		fmt.Println("❌ Schema missing 'properties' key")
		return
	}

	// Check properties is a map
	properties, ok := schema["properties"].(map[string]interface{})
	if ok {
		fmt.Println("✅ Properties is a map")
	} else {
		fmt.Println("❌ Properties is not a map")
		return
	}

	// Check properties contains "overall_maturity_level"
	if _, exists := properties["overall_maturity_level"]; exists {
		fmt.Println("✅ Properties contains 'overall_maturity_level'")
	} else {
		fmt.Println("❌ Properties missing 'overall_maturity_level'")
		fmt.Printf("Available properties: ")
		for key := range properties {
			fmt.Printf("%s ", key)
		}
		fmt.Println()
		return
	}

	// Additional validation
	fmt.Println("\n📊 Schema validation:")
	fmt.Printf("  - Schema type: %v\n", schema["type"])
	fmt.Printf("  - Properties count: %d\n", len(properties))
	
	if required, ok := schema["required"].([]interface{}); ok {
		fmt.Printf("  - Required fields: %d\n", len(required))
	}

	// Test specific properties
	if oml, exists := properties["overall_maturity_level"].(map[string]interface{}); exists {
		fmt.Printf("  - overall_maturity_level type: %v\n", oml["type"])
		if enum, ok := oml["enum"].([]interface{}); ok {
			fmt.Printf("  - Enum values: %d options\n", len(enum))
		}
	}

	fmt.Println("\n🎉 All assertions pass! The test fix is correct.")
	fmt.Println("\nThe failing test should now pass with the updated assertion:")
	fmt.Println("  properties, ok := schema[\"properties\"].(map[string]interface{})")
	fmt.Println("  assert.True(t, ok, \"properties should be a map\")")
	fmt.Println("  assert.Contains(t, properties, \"overall_maturity_level\")")
}
