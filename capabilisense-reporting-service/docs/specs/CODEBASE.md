# Codebase Structure: CapabiliSense Reporting Service (Go)

This document outlines the current directory and file structure for the production-ready Go-based reporting service with AI insights and chart generation.

## Root Directory: `capabilisense-reporting-service/`

```
capabilisense-reporting-service/
├── cmd/                            # Application entry points
│   ├── combined_api/               # 🚀 PRIMARY: Unified API service
│   │   └── main.go                 # Combined Stage A + B + Charts + PDF API
│   ├── generate_ai_pdf/            # PDF generation utility
│   │   └── main.go                 # Standalone PDF generator
│   ├── reportgenerator/            # Legacy PDF service (port 8082)
│   │   └── main.go                 # Original PDF renderer
│   ├── stage1_data_api/            # Standalone Stage A API
│   │   └── main.go                 # Data extraction service
│   └── llm-integration-example/    # LLM testing utility
│       └── main.go                 # LLM integration examples
├── pkg/                            # Core packages
│   ├── config/                     # Configuration management
│   │   ├── config.go               # App configuration (env vars, database)
│   │   ├── prompts_library.go      # LLM prompts and provider management
│   │   ├── prompts_library_test.go # Configuration tests
│   │   └── testhelpers.go          # Test utilities
│   ├── dataextraction/             # 📊 Stage A - Data Processing
│   │   ├── api/                    # HTTP handlers for Stage A
│   │   │   └── handlers.go         # REST API endpoints
│   │   ├── models.go               # Core data models
│   │   ├── models_api_out.go       # API output structures
│   │   ├── models_db.go            # Database models
│   │   ├── extractor.go            # Data extraction interface
│   │   ├── processor.go            # Business logic
│   │   ├── repository.go           # Database repository interface
│   │   ├── service.go              # Core Stage A service
│   │   └── service_test.go         # Service tests
│   ├── aiinsights/                 # 🤖 Stage B - AI Insights
│   │   ├── providers/              # LLM provider implementations
│   │   ├── insightgenerator.go     # AI insight orchestration
│   │   ├── llm_interface.go        # LLM interface and providers
│   │   ├── llm_interface_test.go   # LLM interface tests
│   │   ├── llm_integration_test.go # Integration tests with real APIs
│   │   ├── llm_benchmark_test.go   # Performance benchmarks
│   │   ├── llm_logger.go           # LLM call logging
│   │   ├── llm_logger_test.go      # Logger tests
│   │   ├── llmclient.go            # LLM client implementation
│   │   ├── llmclient_test.go       # Client tests
│   │   ├── models.go               # AI insight models
│   │   ├── stage_b_api.go          # Stage B HTTP handlers
│   │   └── stage_b_service.go      # Stage B service implementation
│   ├── charts/                     # 📈 Chart Generation System
│   │   ├── adaptive_rays_chart.go          # Adaptive scaling rays
│   │   ├── adaptive_scaling.go             # Adaptive scaling logic
│   │   ├── adaptive_textured_rays_chart.go # 🎨 Ultimate chart solution
│   │   ├── chart_api.go                    # Chart REST API
│   │   ├── chart_generator.go              # Chart generation utilities
│   │   ├── chart_interface.go              # Unified chart interface
│   │   ├── chart_integration_test.go       # Chart integration tests
│   │   ├── enhanced_font_renderer.go       # Vector font rendering
│   │   ├── fancy_rays_chart.go             # Enhanced visual effects
│   │   ├── rays_chart.go                   # Basic rays charts
│   │   ├── spider_chart_simple.go          # Spider chart engine
│   │   ├── svg_rays_chart.go               # SVG-based charts
│   │   └── textured_rays_chart.go          # Textured ray patterns
│   ├── pdfgenerator/               # 📄 Stage C - PDF Generation
│   │   ├── renderer.go             # PDF renderer interface
│   │   ├── simple_renderer.go      # 🚀 PRIMARY: Direct fpdf with charts
│   │   ├── mdtopdf_renderer.go     # Secondary: Markdown-based
│   │   └── template_manager.go     # Template management
│   ├── debug/                      # Debug utilities
│   │   └── debug_utils.go          # Debug helpers
│   ├── utils/                      # Utility functions
│   └── models/                     # Shared data models
│       └── report_data.go          # Final report data structure
├── configs/                        # Configuration files
│   ├── prompts_library.json        # LLM prompts and provider config
│   ├── prompts/                    # Individual prompt files
│   │   ├── ai_spotlight.txt        # AI spotlight generation
│   │   ├── business_overview.txt   # Business summary prompts
│   │   ├── domain_insights.txt     # Domain analysis prompts
│   │   ├── focus_area.txt          # Focus area recommendations
│   │   ├── frontend_chat_initial.txt
│   │   ├── frontend_chat_system.txt
│   │   ├── hyde_generator.txt
│   │   ├── llm_extractor.txt
│   │   └── time_concerned_assessor.txt
│   ├── schemas/                    # JSON schemas for structured output
│   │   ├── ai_spotlight.json       # AI spotlight schema
│   │   ├── business_overview.json  # Business overview schema
│   │   ├── domain_insights.json    # Domain insights schema
│   │   ├── focus_area.json         # Focus area schema
│   │   └── maturity_assessment.json
│   └── fallbacks/                  # Fallback content when AI unavailable
│       ├── ai_spotlight_fallback.txt
│       ├── business_overview_fallback.txt
│       ├── domain_insights_fallback.txt
│       ├── focus_area_fallback.txt
│       ├── frontend_chat_fallback.txt
│       ├── hyde_generator_fallback.txt
│       ├── llm_extractor_fallback.txt
│       └── maturity_assessor_fallback.json
├── templates/                      # Report templates
│   ├── assessment_report.md        # Full report template
│   └── assessment_report_simplified.md # Simplified template
├── web/                           # Web interface
│   ├── README.md                  # Web interface documentation
│   └── test-pdf-generator.html    # Interactive test webpage
├── scripts/                       # Utility scripts
│   ├── run-llm-tests.sh          # LLM test runner
│   └── validate-test-files.sh    # File validation
├── logs/                          # Log files (created at runtime)
├── generated_charts/              # Generated chart files (created at runtime)
├── migrations/                    # Database migrations
│   └── 003_add_ai_insights_cache.sql
├── docs/                          # 📚 Documentation
│   ├── DOCUMENTATION_INDEX.md     # 📚 Master documentation index (start here)
│   ├── README.md                  # 📖 Documentation hub and overview
│   ├── specs/                     # 📋 Specifications & Requirements
│   │   ├── README.md              # Specifications directory overview
│   │   ├── CODEBASE.md            # Complete file organization and architecture
│   │   └── PRD.md                 # Product Requirements Document
│   ├── project/                   # 📝 Project Management
│   │   ├── README.md              # Project management directory overview
│   │   ├── ROADMAP.md             # ⭐ MASTER: Combined roadmap, status, priorities
│   │   ├── TASK_SUMMARY.md        # Development progress and completed features
│   │   ├── TODO.md                # Current priorities and implementation details
│   │   └── CHANGELOG.md           # Version history and feature evolution
│   ├── devnotes/                  # 🔧 Development Notes & Debugging
│   │   ├── README.md              # Development notes directory overview
│   │   ├── LLM_LIBRARY.md         # Multi-provider AI integration, optimization
│   │   ├── DEBUG_GUIDE.md         # LLM call logging, troubleshooting, performance
│   │   ├── TESTING.md             # Comprehensive test suite documentation
│   │   ├── TEST_SUITE_SUMMARY.md  # LLM library test implementation
│   │   ├── CODE_ASSISTANT.md      # Development assistant documentation
│   │   └── status.txt             # Development status notes
│   └── solutions/                 # 🔧 Technical Solution Documentation
│       ├── README.md              # Solutions directory overview
│       ├── TEXTURED_RAYS_SOLUTION.md      # Ultimate chart implementation
│       ├── ENHANCED_FONTS_SOLUTION.md     # Vector font rendering system
│       ├── ADAPTIVE_SCALING_SOLUTION.md   # Intelligent chart scaling
│       ├── FANCY_RAYS_STRATEGY.md         # Advanced visualization techniques
│       ├── CACHING.md                     # Performance optimization system
│       └── SIMPLE_PDF_RENDERER.md         # Primary PDF generation solution
├── assessment_backend.db          # SQLite database
├── .env                          # Environment variables
├── go.mod                        # Go module definition
├── go.sum                        # Go module checksums
└── README.md                     # Main project documentation
```

## Key Package Responsibilities:

### **Application Entry Points (`cmd/`)**
*   **`cmd/combined_api/`** (🚀 PRIMARY): Unified API service hosting all three stages (A+B+Charts+PDF) on port 8081
*   **`cmd/generate_ai_pdf/`**: Standalone PDF generation utility using mock data
*   **`cmd/reportgenerator/`**: Legacy PDF service on port 8082 (simple renderer)
*   **`cmd/stage1_data_api/`**: Standalone Stage A API service
*   **`cmd/llm-integration-example/`**: LLM testing and integration examples

### **Core Business Logic (`pkg/`)**
*   **`pkg/config/`**: Configuration management, environment variables, and LLM provider setup
*   **`pkg/dataextraction/`** (📊 Stage A): Database integration, framework parsing, domain score calculation, and AI context preparation
*   **`pkg/aiinsights/`** (🤖 Stage B): Multi-provider LLM integration, AI insight generation, structured output, and fallback handling
*   **`pkg/charts/`** (📈 Chart Generation): Sophisticated chart system with multiple styles:
    *   **Adaptive Textured Rays** (🎨 Ultimate): Textured rays with intelligent scaling and vector fonts
    *   **Spider Charts**: Variable domain/maturity level support
    *   **Multiple Styles**: Simple, fancy, SVG, and adaptive variants
*   **`pkg/pdfgenerator/`** (📄 Stage C): PDF generation with chart integration:
    *   **`simple_renderer.go`** (🚀 PRIMARY): Direct fpdf with adaptive textured rays charts
    *   **`mdtopdf_renderer.go`** (SECONDARY): Markdown-based rendering
*   **`pkg/models/`**: Shared data structures for report generation
*   **`pkg/debug/`**: Debug utilities and helpers
*   **`pkg/utils/`**: Common utility functions

### **Configuration & Assets**
*   **`configs/`**: LLM prompts, JSON schemas, and fallback content organized by type
*   **`templates/`**: Markdown templates for report generation
*   **`web/`**: Interactive test interface for PDF generation
*   **`scripts/`**: Utility scripts for testing and validation
*   **`docs/`**: Comprehensive documentation including this codebase overview

### **Runtime Directories** (Created automatically)
*   **`logs/`**: LLM call logs in JSONL format for debugging
*   **`generated_charts/`**: PNG chart files generated by the chart system

## Production Architecture:

### **Three-Stage Pipeline**
1. **Stage A** (Data Extraction): SQLite → Framework parsing → Domain scores → AI context
2. **Stage B** (AI Insights): Multi-provider LLM → Structured output → Professional insights
3. **Stage C** (PDF Generation): Charts + AI insights → Professional PDF reports

### **API Endpoints** (Combined API on port 8081)
- `GET /api/v1/report-data?project_id=<id>` - Stage A data extraction
- `POST /api/v1/generate-insights` - Stage B AI insight generation
- `POST /api/v1/generate-chart` - Chart generation with multiple styles
- `POST /api/v1/generate-complete-pdf?project_id=<id>` - End-to-end PDF generation
- `GET /test` - Interactive web interface for testing

### **Chart System Features**
- **Adaptive Scaling**: Intelligent zoom for low scores with context preservation
- **Vector Fonts**: Large, readable fonts (14pt-40pt) for professional appearance
- **Multiple Styles**: Simple rays, textured rays, adaptive textured rays, spider charts
- **Professional Quality**: Enterprise-ready visualizations suitable for business reports

## Module Information:

The service is a Go module with the following structure:
```bash
module capabilisense-reporting-service
go 1.21

# Key dependencies:
# - github.com/go-pdf/fpdf (PDF generation)
# - golang.org/x/image (chart rendering)
# - database/sql + modernc.org/sqlite (database)
# - Various LLM provider SDKs
```