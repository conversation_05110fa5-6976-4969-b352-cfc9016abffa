# LLM Library Testing Guide

## Overview

This document describes the comprehensive test suite for the LLM library, including unit tests, integration tests, benchmarks, and testing best practices.

## Test Structure

### Test Files

- `pkg/config/prompts_library_test.go` - Configuration loading and validation tests
- `pkg/aiinsights/llm_interface_test.go` - Core LLM interface unit tests
- `pkg/aiinsights/llm_integration_test.go` - Integration tests with real API calls
- `pkg/aiinsights/llmclient_test.go` - Client interface and factory tests
- `pkg/aiinsights/llm_benchmark_test.go` - Performance benchmarks

### Test Categories

#### 1. Unit Tests (`Test*`)
- Test core functionality without external dependencies
- Mock all API calls and file operations
- Fast execution (< 1 second per test)
- No API keys required

#### 2. Integration Tests (`TestIntegration*`)
- Test with real API providers
- Require valid API keys in `.env` file
- Longer execution time (up to 30 seconds per test)
- Test actual provider responses and fallback mechanisms

#### 3. Benchmark Tests (`Benchmark*`)
- Measure performance of core operations
- Test memory allocation patterns
- Concurrent access patterns
- Baseline performance metrics

## Setup

### 1. Environment Configuration

Create a `.env` file in the project root:

```bash
cp .env.example .env
# Edit .env with your actual API keys
```

Required API keys for full testing:
- `AZURE_OPENAI_KEY` - Azure OpenAI service
- `GCP_API_KEY` or `GOOGLE_API_KEY` - Google Gemini
- `ANTHROPIC_API_KEY` - Anthropic Claude
- `OPENROUTER_API_KEY` - OpenRouter (multiple models)
- `MISTRAL_API_KEY` - Mistral AI

### 2. Dependencies

Install test dependencies:
```bash
go mod tidy
```

## Running Tests

### Quick Test Run

```bash
# Run the comprehensive test suite
./scripts/run-llm-tests.sh

# Or run the basic test runner
go run cmd/test-runner/main.go
```

### Manual Test Execution

#### Unit Tests Only
```bash
# Configuration tests
go test -v ./pkg/config -run "^Test[^I]" -timeout 30s

# LLM interface unit tests
go test -v ./pkg/aiinsights -run "^Test[^I]" -timeout 60s
```

#### Integration Tests Only
```bash
# Requires API keys in .env
go test -v ./pkg/aiinsights -run "^TestIntegration" -timeout 300s
```

#### All Tests with Coverage
```bash
go test -v -cover ./pkg/config ./pkg/aiinsights -timeout 300s
```

#### Benchmarks
```bash
# Run performance benchmarks
go test -bench=. ./pkg/aiinsights -benchmem

# Specific benchmark
go test -bench=BenchmarkMockLLMClient ./pkg/aiinsights
```

## Test Coverage

### Current Coverage Areas

#### Configuration (`pkg/config`)
- ✅ Prompts library loading and parsing
- ✅ Model configuration validation
- ✅ Authentication mode handling
- ✅ System prompt loading from files
- ✅ JSON schema loading and validation
- ✅ Fallback content loading
- ✅ Safety settings configuration
- ✅ Environment variable handling

#### LLM Interface (`pkg/aiinsights`)
- ✅ Multi-provider payload building
- ✅ Response parsing for all providers
- ✅ Authentication handling (bearer, header, query param)
- ✅ Provider fallback mechanisms
- ✅ Error handling and logging
- ✅ Message building and formatting
- ✅ Tool/function call support
- ✅ Structured output with JSON schemas

#### Client Integration
- ✅ Factory pattern implementation
- ✅ Unified client interface
- ✅ Mock client for testing
- ✅ Configuration validation
- ✅ Insight generation workflows

### Coverage Targets

- **Unit Tests**: > 90% code coverage
- **Integration Tests**: All major provider flows
- **Error Handling**: All error paths tested
- **Performance**: Baseline benchmarks established

## Test Scenarios

### Unit Test Scenarios

1. **Configuration Loading**
   - Valid configuration files
   - Invalid/malformed JSON
   - Missing files
   - Environment variable resolution

2. **Provider Payload Building**
   - OpenAI/Azure format
   - Google Gemini format
   - Anthropic format
   - With and without tools
   - With and without JSON schemas

3. **Response Parsing**
   - Successful responses
   - Error responses
   - Malformed responses
   - Token usage extraction

4. **Authentication**
   - Bearer token auth
   - Custom header auth
   - Query parameter auth
   - Missing API keys

### Integration Test Scenarios

1. **Basic LLM Calls**
   - Simple chat requests
   - System prompt handling
   - Temperature settings

2. **Structured Output**
   - JSON schema validation
   - Provider-specific implementations
   - Schema loading from files

3. **Provider Fallback**
   - Invalid provider first
   - Network failures
   - API key issues
   - Fallback file serving

4. **Chat History**
   - Multi-turn conversations
   - Message formatting
   - Context preservation

5. **Performance Testing**
   - Response time baselines
   - Token usage tracking
   - Concurrent requests

## Test Data

### Mock Data
- Predefined responses for different prompt types
- Realistic token usage numbers
- Simulated processing times

### Test Configurations
- Minimal valid configurations
- Invalid configurations for error testing
- Provider-specific configurations

### Test Files
- Sample system prompts
- JSON schemas for structured output
- Fallback response files

## Debugging Tests

### Verbose Output
```bash
go test -v ./pkg/aiinsights -run TestSpecificFunction
```

### Test Specific Provider
```bash
# Set only one API key to test specific provider
export AZURE_OPENAI_KEY="your_key"
go test -v ./pkg/aiinsights -run TestIntegration
```

### Debug Integration Issues
```bash
# Enable detailed logging
export LOG_LEVEL=debug
go test -v ./pkg/aiinsights -run TestIntegrationCallLLM
```

## Continuous Integration

### GitHub Actions (Recommended)

```yaml
name: LLM Library Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: '1.24'
      - name: Run unit tests
        run: go test -v ./pkg/config ./pkg/aiinsights -run "^Test[^I]"
      - name: Run integration tests
        env:
          AZURE_OPENAI_KEY: ${{ secrets.AZURE_OPENAI_KEY }}
          ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
        run: go test -v ./pkg/aiinsights -run "^TestIntegration"
```

## Best Practices

### Writing Tests

1. **Use descriptive test names**
   ```go
   func TestCallLLMWithValidProvider(t *testing.T)
   func TestCallLLMWithInvalidAPIKey(t *testing.T)
   ```

2. **Test both success and failure paths**
   ```go
   // Test success
   response, err := llm.CallLLM(ctx, validRequest)
   assert.NoError(t, err)

   // Test failure
   _, err = llm.CallLLM(ctx, invalidRequest)
   assert.Error(t, err)
   ```

3. **Use table-driven tests for multiple scenarios**
   ```go
   testCases := []struct {
       name     string
       input    string
       expected string
   }{
       {"valid_input", "hello", "response"},
       {"empty_input", "", "error"},
   }
   ```

4. **Mock external dependencies in unit tests**
   ```go
   // Don't make real API calls in unit tests
   // Use mock responses instead
   ```

### Running Tests Efficiently

1. **Run unit tests frequently during development**
2. **Run integration tests before commits**
3. **Use benchmarks to catch performance regressions**
4. **Monitor test coverage regularly**

## Troubleshooting

### Common Issues

1. **Tests timeout**
   - Increase timeout: `-timeout 300s`
   - Check network connectivity
   - Verify API keys are valid

2. **Integration tests fail**
   - Check `.env` file exists and has valid keys
   - Verify API quotas/limits
   - Check provider service status

3. **Coverage reports missing**
   - Ensure all packages are included in test command
   - Check file permissions for coverage output

### Getting Help

1. Check test logs for detailed error messages
2. Run individual tests to isolate issues
3. Verify configuration files are valid JSON
4. Test with mock client first to isolate API issues
