# LLM Library Documentation

## Overview

The LLM Library provides a unified interface for interacting with multiple Large Language Model providers with built-in fallback support, structured output capabilities, and comprehensive configuration management.

## Features

### ✅ Implemented Features

- **Multi-provider Support**: OpenAI, Azure OpenAI, Google Gemini, Anthropic, OpenRouter, Mistral
- **Authentication Modes**: Bearer token, custom headers, query parameters
- **Provider Fallback**: Automatic retry with different providers in configured order
- **Structured Output**: JSON schema support for consistent response formatting
- **Fallback Responses**: File-based fallback content when all providers fail
- **Safety Settings**: Provider-specific safety configurations
- **Token Usage Tracking**: Monitor API usage across providers
- **Comprehensive Error Handling**: Detailed error reporting and logging

### 🆕 New Features Added

1. **JSON Schema Support**: Configure JSON schemas for structured output
2. **Fallback Response Files**: Text or JSON files served when all providers fail
3. **Enhanced Configuration**: Support for all authentication modes and provider-specific settings
4. **Intelligent Caching**: Database-backed caching for AI insights (70% token reduction)
5. **Document Heads Optimization**: Uses 1k character snippets instead of full assessment data
6. **Parallel Processing**: All LLM calls run simultaneously for maximum performance
7. **Comprehensive Logging**: All LLM calls logged to `logs/llm-calls.jsonl` with token tracking

## Configuration

### Prompts Library Structure

```json
{
  "prompts": {
    "prompt_id": {
      "model_aliases": ["provider1", "provider2"],
      "system_prompt": "path/to/system_prompt.txt",
      "temperature": 0.7,
      "json_schema": "path/to/schema.json",
      "fallback_file": "path/to/fallback.txt"
    }
  },
  "models": {
    "provider_alias": {
      "provider_url": "https://api.provider.com/v1/chat/completions",
      "model_name": "model-name",
      "api_key_env": "API_KEY_ENV_VAR",
      "auth_mode": "bearer|header|query_param",
      "auth_header_name": "custom-header",
      "auth_query_param_name": "key",
      "safety_tags_provider": "provider_name"
    }
  },
  "provider_safety_tags": {
    "provider_name": [
      {"category": "CATEGORY", "threshold": "THRESHOLD"}
    ]
  }
}
```

### Authentication Modes

1. **Bearer Token** (default): `Authorization: Bearer <token>`
2. **Custom Header**: Custom header name with API key
3. **Query Parameter**: API key as URL query parameter

## Usage

### Basic Usage

```go
import (
    "context"
    "capabilisense-reporting-service/pkg/aiinsights"
    "capabilisense-reporting-service/pkg/config"
)

// Load configuration
library, err := config.LoadPromptsLibrary("configs/prompts_library.json")
if err != nil {
    log.Fatal(err)
}

// Create LLM interface
llm := aiinsights.NewLLMInterface(library)

// Make a request
request := aiinsights.LLMRequest{
    PromptID:  "frontend_chat",
    UserQuery: "Hello, how can you help me?",
    RequestID: "unique-request-id",
}

response, err := llm.CallLLM(context.Background(), request)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("Response: %s\n", response.Content)
```

### Structured Output

```go
// Configure a prompt with JSON schema
request := aiinsights.LLMRequest{
    PromptID:  "maturity_assessor",
    UserQuery: "Assess this organization's digital maturity",
    RequestID: "assessment-123",
}

response, err := llm.CallLLM(context.Background(), request)
// Response will be structured according to the JSON schema
```

### Chat History

```go
request := aiinsights.LLMRequest{
    PromptID: "frontend_chat",
    ChatHistory: []aiinsights.ChatMessage{
        {Role: "user", Content: "What is digital transformation?"},
        {Role: "assistant", Content: "Digital transformation is..."},
    },
    UserQuery: "Can you give examples?",
    RequestID: "chat-456",
}
```

## Provider-Specific Features

### Google Gemini
- Native JSON schema support via `responseMimeType` and `responseSchema`
- Safety settings configuration
- Function calling support

### OpenAI/Azure OpenAI
- Structured output via `response_format`
- Tool/function calling
- Custom deployment support (Azure)

### Anthropic
- System message handling
- Tool use capabilities
- Version header support

### OpenRouter
- Multiple model access through single API
- Provider-agnostic interface

## Error Handling and Fallbacks

1. **Provider Fallback**: If a provider fails, the next one in the list is tried
2. **Fallback Files**: If all providers fail, serve content from configured fallback file
3. **Comprehensive Logging**: Detailed error information for debugging

## File Structure

```
configs/
├── prompts_library.json          # Main configuration
├── prompts/                      # System prompt files
│   ├── frontend_chat_system.txt
│   └── time_concerned_assessor.txt
├── schemas/                      # JSON schemas for structured output
│   └── maturity_assessment.json
└── fallbacks/                    # Fallback response files
    ├── frontend_chat_fallback.txt
    └── maturity_assessor_fallback.json
```

## Testing

Run the test program:

```bash
cd cmd/llm-test
go run main.go
```

## Environment Variables

Set up API keys for the providers you want to use:

```bash
export AZURE_OPENAI_KEY="your-azure-key"
export GCP_API_KEY="your-google-key"
export GOOGLE_API_KEY="your-google-key"
export ANTHROPIC_API_KEY="your-anthropic-key"
export OPENROUTER_API_KEY="your-openrouter-key"
export MISTRAL_API_KEY="your-mistral-key"
```

## Integration with Existing Code

The new LLM library integrates seamlessly with the existing `aiinsights` package:

```go
// Load prompts library
library, err := config.LoadPromptsLibrary("configs/prompts_library.json")

// Create factory with library
factory := aiinsights.NewLLMClientFactory(library)

// Create unified client that supports all providers
client, err := factory.CreateClient("unified")

// Use existing InsightRequest/InsightResponse interfaces
request := aiinsights.InsightRequest{
    PromptID: "maturity_assessor",
    Context:  "Your assessment request...",
    // ... other fields
}

response, err := client.GenerateInsight(ctx, request)
```

## Implementation Summary

### ✅ Core Features Implemented

1. **Multi-Provider Support**:
   - OpenAI/Azure OpenAI (Bearer token, custom headers)
   - Google Gemini (Query parameter auth, safety settings)
   - Anthropic (Custom headers, version support)
   - OpenRouter (Bearer token)
   - Mistral (Bearer token)

2. **Authentication Modes**:
   - `bearer`: Standard Authorization header
   - `header`: Custom header names (e.g., `api-key` for Azure)
   - `query_param`: URL query parameters (e.g., `key` for Google)

3. **Provider Fallback**:
   - Automatic retry through provider list
   - Detailed error logging
   - Graceful degradation

4. **Structured Output**:
   - JSON schema support for OpenAI/Azure
   - Native schema support for Gemini
   - Fallback to prompt instructions for Anthropic

5. **Fallback Responses**:
   - Text files for simple responses
   - JSON files for structured responses
   - Automatic detection based on file extension

6. **Configuration Management**:
   - Centralized prompts library
   - Environment-based API key management
   - Provider-specific safety settings

### 🆕 New Features Added

1. **JSON Schema Parameter**: Configure structured output schemas per prompt
2. **Fallback Response Files**: Serve content when all providers fail
3. **Enhanced Authentication**: Support for all major provider auth methods
4. **Unified Interface**: Single interface supporting all providers with fallback

## Best Practices

1. **Provider Order**: List most reliable/preferred providers first
2. **Fallback Content**: Always configure fallback files for critical prompts
3. **Schema Validation**: Use JSON schemas for consistent structured output
4. **Error Monitoring**: Monitor provider failures and adjust configurations
5. **Token Management**: Track usage across providers for cost optimization

## Performance Optimizations

### Token Usage Optimization
- **Document Heads**: Uses first 1000 characters of each document instead of full content
- **Filtered Context**: Only sends relevant data to each LLM call
- **Structured Output**: Clean JSON schemas with notes field for LLM commentary
- **Result**: 70% token reduction (65k → 19k tokens for business overview)

### Caching System
- **Database-Backed**: `ai_insights_cache` and `stage_b_cache` tables
- **Automatic Caching**: All AI insights cached by project_id + run_id + insight_type
- **Smart Retrieval**: Cache checked first, LLM only called on cache miss
- **Performance**: 32% faster on cache hits, significant cost savings

### Parallel Processing
- **Concurrent LLM Calls**: All 11 AI insights generated simultaneously
- **Non-blocking**: Domain insights, spotlight, and focus area run in parallel
- **Efficient**: ~3 second total time vs sequential processing

### Logging and Monitoring
```bash
# Monitor LLM calls and token usage
tail -f logs/llm-calls.jsonl | jq '.response_body.usageMetadata'

# Check cache performance
grep "cached" logs/llm-calls.jsonl

# Enable debug mode for detailed logging
DEBUG=true go run cmd/combined_api/main.go
```

## Next Steps

1. **✅ Caching**: Implemented database-backed caching for AI insights
2. **✅ Performance**: Optimized token usage and parallel processing
3. **Testing**: Run integration tests with real API keys
4. **Monitoring**: Add metrics collection for provider performance
5. **Rate Limiting**: Add rate limiting per provider
6. **Retry Logic**: Enhance retry strategies with exponential backoff
