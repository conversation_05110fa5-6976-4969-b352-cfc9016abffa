-- Migration to add AI insights caching tables
-- This allows reusing expensive AI-generated insights for the same project_id and run_id

-- Table to store Stage B AI insights results
CREATE TABLE IF NOT EXISTS ai_insights_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id TEXT NOT NULL,
    run_id TEXT NOT NULL,
    insight_type TEXT NOT NULL, -- 'organization_name', 'business_summary', 'domain_insights', 'ai_spotlight', 'focus_area'
    insight_data TEXT NOT NULL, -- JSON data of the generated insight
    tokens_used INTEGER NOT NULL DEFAULT 0,
    provider_used TEXT NOT NULL DEFAULT '',
    used_fallback BOOLEAN NOT NULL DEFAULT FALSE,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME, -- Optional expiration for cache invalidation
    
    -- Ensure uniqueness per project/run/type
    UNIQUE(project_id, run_id, insight_type)
);

-- Index for fast lookups
CREATE INDEX IF NOT EXISTS idx_ai_insights_cache_lookup 
ON ai_insights_cache(project_id, run_id, insight_type);

-- Index for cleanup of expired entries
CREATE INDEX IF NOT EXISTS idx_ai_insights_cache_expires 
ON ai_insights_cache(expires_at) WHERE expires_at IS NOT NULL;

-- Table to store complete Stage B outputs for reuse
CREATE TABLE IF NOT EXISTS stage_b_cache (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    project_id TEXT NOT NULL,
    run_id TEXT NOT NULL,
    stage_b_output TEXT NOT NULL, -- Complete JSON output from Stage B
    total_tokens_used INTEGER NOT NULL DEFAULT 0,
    providers_used TEXT NOT NULL DEFAULT '', -- JSON array of providers used
    fallbacks_used TEXT NOT NULL DEFAULT '', -- JSON array of fallbacks used
    processing_time_ms INTEGER NOT NULL DEFAULT 0,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME, -- Optional expiration
    
    -- Ensure uniqueness per project/run
    UNIQUE(project_id, run_id)
);

-- Index for fast Stage B cache lookups
CREATE INDEX IF NOT EXISTS idx_stage_b_cache_lookup 
ON stage_b_cache(project_id, run_id);

-- Index for cleanup of expired Stage B entries
CREATE INDEX IF NOT EXISTS idx_stage_b_cache_expires 
ON stage_b_cache(expires_at) WHERE expires_at IS NOT NULL;
