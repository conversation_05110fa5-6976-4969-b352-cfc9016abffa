# CapabiliSense PDF Generator Test Webpage

## 🎯 Overview
Interactive test webpage for generating and downloading CapabiliSense assessment reports as PDFs.

## 🚀 Quick Start

### 1. Start the API Server
```bash
cd capabilisense-reporting-service
go run cmd/combined_api/main.go
```

### 2. Open Test Webpage
Visit: **http://localhost:8081/test**

### 3. Generate PDF
1. Click "AI Project" quick test button (or enter custom project ID)
2. Select "Mock Data" generation type
3. Click "Generate & Download PDF Report"
4. PDF downloads automatically! 🎉

## 📋 Features

### Quick Test Options
- **AI Project**: Uses AI transformation assessment data
- **HR Project**: Uses HR capability assessment data  
- **Mock Project**: Uses generic mock data

### Generation Types
- **Mock Data**: Fast generation using existing mock files
- **Full Pipeline**: Complete Stage A → Stage B → Charts → PDF (future feature)

### Real-time Status
- ✅ API connection status on page load
- 🔄 Step-by-step generation progress
- ❌ Error handling with detailed messages

## 🛠️ Technical Details

### API Endpoints Used
- `GET /health` - Check API status
- `POST /api/v1/generate-mock-pdf?project_id=<id>` - Generate PDF

### Files Generated
- PDF saved as: `{project_id}_assessment_report.pdf`
- Example: `ai_assessment_report.pdf`

### Browser Compatibility
- Modern browsers with JavaScript enabled
- CORS enabled for localhost testing

## 🔧 Troubleshooting

### API Not Running
**Error**: "API not accessible"
**Solution**: Start the Combined API server:
```bash
go run cmd/combined_api/main.go
```

### PDF Generation Failed
**Error**: "PDF generation failed"
**Solutions**:
1. Check that mock data files exist:
   - `mock_ai_stage_a.json`
   - `stage_b_ai.json`
   - `generated_charts/ai_project_spider_chart.png`
2. Verify Go is in PATH: `which go`
3. Check server logs for detailed error messages

### File Not Found
**Error**: "PDF file not found"
**Solution**: The PDF generator creates files with date stamps. Check for files matching pattern:
```
AI_Transformation_Assessment_YYYY-MM-DD.pdf
```

## 📁 File Structure
```
web/
├── test-pdf-generator.html    # Main test webpage
└── README.md                  # This documentation

cmd/
├── combined_api/main.go       # API server with PDF endpoint
└── generate_ai_pdf/main.go    # PDF generator executable
```

## 🎨 UI Features
- Professional styling with CapabiliSense branding
- Responsive design for desktop and mobile
- Real-time status indicators
- Progress tracking during generation
- Error handling with user-friendly messages

## 🔮 Future Enhancements
- Full pipeline integration (Stage A → B → Charts → PDF)
- Custom project data upload
- PDF preview before download
- Batch generation for multiple projects
- Template customization options
