{"prompts": {"frontend_chat": {"model_aliases": ["azure", "gcp-gemini-2.0-flash-lite"], "system_prompt": "configs/prompts/frontend_chat_system.txt", "initial_message": "configs/prompts/frontend_chat_initial.txt", "temperature": 0.6, "fallback_file": "configs/fallbacks/frontend_chat_fallback.txt"}, "maturity_assessor": {"model_aliases": ["gcp-gemini-2.5-pro", "gcp-gemini-2.0-flash-lite", "azure"], "system_prompt": "configs/prompts/time_concerned_assessor.txt", "temperature": 0.0, "json_schema": "configs/schemas/maturity_assessment.json", "fallback_file": "configs/fallbacks/maturity_assessor_fallback.json"}, "hyde_generator": {"description": "Generates a hypothetical document snippet representing good evidence for a specific indicator.", "model_aliases": ["gcp-gemini-2.0-flash-lite", "azure"], "system_prompt": "configs/prompts/hyde_generator.txt", "temperature": 0.4, "fallback_file": "configs/fallbacks/hyde_generator_fallback.txt"}, "llm_extractor": {"description": "Fallback extractor using Gemini multimodal to get text from various file types.", "model_aliases": ["gcp-gemini-2.0-flash-lite", "gcp-gemini-2.5-pro", "azure"], "system_prompt": "configs/prompts/llm_extractor.txt", "temperature": 0.0, "fallback_file": "configs/fallbacks/llm_extractor_fallback.txt"}}, "provider_safety_tags": {"google": [{"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"}, {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"}, {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"}, {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"}], "azure": []}, "models": {"openrouter-quasar": {"provider_url": "https://openrouter.ai/api/v1/chat/completions", "model_name": "openrouter/optimus-alpha", "api_key_env": "OPENROUTER_API_KEY", "default": true, "safety_tags_provider": "quasar"}, "anthropic": {"provider_url": "https://api.anthropic.com/v1/messages", "model_name": "claude-3-opus", "api_key_env": "ANTHROPIC_API_KEY", "safety_tags_provider": "anthropic", "auth_mode": "header", "auth_header_name": "x-api-key"}, "azure": {"provider_url": "https://aoai-manual-swecentral-000.openai.azure.com/openai/deployments/gpt-4.1-2025-04-14/chat/completions?api-version=2025-01-01-preview", "model_name": "gpt-4.1-2025-04-14", "api_key_env": "AZURE_OPENAI_KEY", "safety_tags_provider": "azure", "auth_mode": "header", "auth_header_name": "api-key"}, "openrouter-gemini-2.5-pro-free": {"provider_url": "https://openrouter.ai/api/v1/chat/completions", "model_name": "google/gemini-2.5-pro-exp-03-25:free", "api_key_env": "OPENROUTER_API_KEY", "safety_tags_provider": "google"}, "gcp-gemini-2.0-flash-lite": {"provider_url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite:generateContent", "model_name": "gemini-2.0-flash-lite", "api_key_env": "GCP_API_KEY", "safety_tags_provider": "google", "auth_mode": "query_param", "auth_query_param_name": "key"}, "gcp-gemini-2.5-pro": {"provider_url": "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-pro-preview-05-06:generateContent", "model_name": "gemini-2.5-pro-preview-05-06", "api_key_env": "GOOGLE_API_KEY", "safety_tags_provider": "google", "auth_mode": "query_param", "auth_query_param_name": "key"}, "openrouter-gemini-2.0-flash": {"provider_url": "https://openrouter.ai/api/v1/chat/completions", "model_name": "google/gemini-2.0-flash-001", "api_key_env": "OPENROUTER_API_KEY", "safety_tags_provider": "google"}, "mistral-doc-extractor": {"service_type": "extraction", "provider_url": "https://api.mistral.ai/v1/ocr", "model_name": "mistral-ocr-latest", "api_key_env": "MISTRAL_API_KEY", "auth_mode": "bearer"}, "mistral-text-extractor": {"provider_url": "https://api.mistral.ai/v1/chat/completions", "model_name": "mistral-large-latest", "api_key_env": "MISTRAL_API_KEY"}}}