

**Task Description: Go Backend API for Stage A - Report Data Generation**

**Objective:**

Create a new Go-based backend API service. This service will expose an endpoint that accepts a `project_id` and `run_id` (or logic to determine the latest run). It will then fetch the relevant assessment data and framework definition from the existing database (or call existing Python backend APIs if direct DB access is not initially feasible). Using this data, it will perform algorithmic derivations and structure it into a comprehensive JSON output. This JSON will serve as the complete input for Stage B (AI Insight Generation).

**Project Context:**

This service is part of a larger "CapabiliSense PDF Reporting Service." It represents Stage A of a three-stage pipeline (A: Data Extraction, B: AI Insights, C: PDF Generation).

**Target Output JSON Structure (Conceptual - to be refined by Stage B's needs):**

The primary output of this service will be a JSON object. This JSON must contain all data points required by Stage B (AI Insights) to generate the textual content for the final PDF report.

```json
// Example Structure - Fields and depth to be confirmed based on Stage B prompt needs
{
  "report_metadata": {
    "project_id": "project-123",
    "run_id": "run-abc",
    "assessment_date": "2025-05-20T10:00:00Z", // ISO 8601 from AnalysisResults.start_time
    "evidence_as_of_date": "2025-04-19",     // From document metadata or assessment context
    "framework_id": "fw-uuid-xyz",
    "framework_name": "Example Assessment Framework"
    // OrganizationName will be extracted by Stage B, but provide context if available
  },
  "overall_maturity": {
    "score": 2.9, // Median of all leaf capability scores
    "scale_max": 5.0
  },
  "domain_scores_for_spider_chart": [ // Top-level domains/groupings
    { "domain_name": "Strategic Vision", "average_score": 3.5, "capability_count": 5 },
    { "domain_name": "Value & Outcomes", "average_score": 3.4, "capability_count": 6 },
    { "domain_name": "Technology & Data Foundation", "average_score": 1.2, "capability_count": 8 }
    // ... all top-level domains
  ],
  "key_strengths": [ // Top 3 highest scoring domains
    {
      "domain_name": "Value & Outcomes",
      "average_score": 3.4,
      "ai_insight_context": { // Data for Stage B to generate insight for this strength
        "contributing_capabilities": [
          { "name": "Benefit Realization", "score": 4.0, "description": "..." },
          { "name": "Outcome Tracking", "score": 3.8, "description": "..." }
        ],
        "positive_indicators_summary": [ // Optional: summary of high-scoring indicators
            {"name": "Metrics Defined", "score": 4, "reasoning": "Well-defined KPIs..."},
            {"name": "Tracking Process in Place", "score": 4, "reasoning": "Regular review meetings..."}
        ]
      }
    }
    // ... up to 2 more strengths
  ],
  "critical_areas_focus": [ // Bottom 3 lowest scoring domains
    {
      "domain_name": "Technology & Data Foundation",
      "average_score": 1.2,
      "ai_insight_context": { // Data for Stage B to generate insight for this focus area
        "contributing_capabilities": [ // Capabilities within this domain that drag it down
          { "name": "CMDB Accuracy", "score": 1.0, "description": "..." },
          { "name": "Data Governance Implementation", "score": 1.1, "description": "..." }
        ],
        "bottleneck_indicators_details": [ // From AssessmentData.get_level_comparison_data()
          {
            "capability_name": "CMDB Accuracy",
            "indicator_name": "CMDB Completeness",
            "current_score": 1,
            "current_reasoning": "Significant gaps in CMDB data, many CIs missing attributes.",
            "next_level_target": 2,
            "next_level_description": "CMDB data is largely complete for critical services.",
            "next_level_criteria": "Over 80% of critical CIs are registered with all mandatory attributes."
          }
          // ... other bottleneck indicators for capabilities in this domain
        ]
      }
    }
    // ... up to 2 more focus areas
  ],
  "context_for_spotlight_and_solutions_ai": { // Broader data for more holistic AI prompts
    "all_domain_scores_summary": [ // Could be same as domain_scores_for_spider_chart
        { "domain_name": "Strategic Vision", "average_score": 3.5 },
        // ...
    ],
    "all_leaf_capability_scores": [
        { "id": "cap-001", "name": "Benefit Realization", "score": 4.0, "domain": "Value & Outcomes", "description": "..." },
        { "id": "cap-002", "name": "CMDB Accuracy", "score": 1.0, "domain": "Technology & Data Foundation", "description": "..." }
        // ... all leaf capabilities
    ],
    "key_low_scoring_capabilities_with_reasoning": [ // Top N (e.g., 5-10) lowest scoring capabilities overall
        {
            "id": "cap-002", "name": "CMDB Accuracy", "score": 1.0, "domain": "Technology & Data Foundation",
            "indicators": [
                {"name": "CMDB Completeness", "score": 1, "reasoning": "Significant gaps..."},
                {"name": "CMDB Freshness", "score": 1, "reasoning": "Data not updated..."}
            ]
        }
        // ...
    ],
    "document_names_processed": ["doc1.pdf", "doc2.docx"],
    "framework_overview": { // High-level info about the framework structure
        "entity_name": "Corporate Digital Transformation Framework",
        "entity_description": "A framework to assess digital maturity.",
        "term_aliases": { "Grouping": "Phase", "Capability": "Skill Area", "Indicator": "Metric"},
        "top_level_group_names": ["Strategic Vision", "Value & Outcomes", ...]
    }
    // Potentially include summaries of document contents if feasible and helpful for AI,
    // or just names and let Stage B assume AI has access to document embeddings/content.
  }
}
```

**File Structure (Integrated with existing `capabilisense-reporting-service`):**

This service will primarily populate and extend the `pkg/dataextraction/` directory.

```
capabilisense-reporting-service/
├── cmd/
│   └── reportgenerator/
│       └── main.go                 # CURRENTLY: Will be updated/refactored to call the new API service
│   └── stage1_data_api/            # NEW: Main application for this Stage A API service
│       └── main.go
├── pkg/
│   ├── config/
│   │   ├── config.go               # Shared config loader (env vars, app settings)
│   │   └── (prompts_library.go - not directly used by Stage A API)
│   ├── dataextraction/             # STAGE A implementation
│   │   ├── api/                    # NEW: HTTP Handlers for this Stage A API service
│   │   │   └── handlers.go
│   │   ├── models_db.go            # NEW: Go structs for direct DB interaction (if applicable)
│   │   │                           #      or for parsing responses from existing Python API
│   │   ├── models_api_out.go       # NEW: Go structs for the JSON output of this Stage A API
│   │   ├── repository.go           # NEW: Interface for data fetching (DB or Python API)
│   │   │   └── db_repository.go    # NEW: Implementation for direct DB access
│   │   │   └── pyapi_repository.go # NEW: (Alternative) Impl for calling existing Python API
│   │   ├── service.go              # NEW: Core service logic for Stage A.
│   │   │                           #      - Uses repository to get data.
│   │   │                           #      - Performs calculations (median score, top/bottom domains).
│   │   │                           #      - Structures data into models_api_out.go structs.
│   │   ├── (models.go)             # OLD: (To be refactored/renamed) -> models_db.go / models_api_out.go
│   │   ├── (extractor.go)          # OLD: (To be refactored/renamed) -> repository.go
│   │   └── (processor.go)          # OLD: (To be refactored/renamed) -> service.go
│   ├── aiinsights/                 # Stage B (Untouched by this task)
│   │   └── ...
│   ├── pdfgenerator/               # Stage C (Untouched by this task)
│   │   └── ...
│   ├── utils/
│   │   └── ...
│   └── models/                     # Shared simple models (if any beyond specific packages)
│       └── (report_data.go)        # This might move or be composed from Stage A output + Stage B output
├── configs/
│   └── ...
├── templates/
│   └── ...
├── .env                            # Will need DB connection strings or Python API base URL
├── go.mod
├── go.sum
└── README.md
```

**Detailed Implementation Steps for the Coding Model:**

**1. Define Output JSON Structs (`pkg/dataextraction/models_api_out.go`):**
    *   Create Go structs that precisely match the desired output JSON structure shown above (e.g., `StageAOutput`, `ReportMetadata`, `OverallMaturityData`, `DomainScore`, `KeyStrength`, `CriticalArea`, `AIInsightContext`, `ContributingCapability`, `BottleneckIndicatorDetail`, `ContextForAI`, etc.).
    *   Use JSON struct tags (e.g., ``json:"report_metadata"``) for correct serialization.

**2. Define Data Source Structs (`pkg/dataextraction/models_db.go`):**
    *   Create Go structs to represent the raw data as fetched from the database OR as received from the existing Python backend's API if direct DB access is not used.
    *   These will mirror Pydantic models like `AnalysisResults`, `CapabilityAssessmentDetail`, `IndicatorAssessmentDetail`, and the structure of `FrameworkContent`.
    *   Include structs for `FrameworkDefinition` (with `Entity` and `TermAliases`), `EntityNode` (recursive for groupings/capabilities/indicators).

**3. Implement Data Repository (`pkg/dataextraction/repository.go` and implementations):**
    *   Define an interface `Repository` with methods like:
        *   `GetLatestFramework(projectID string) (*FrameworkDefinition, error)`
        *   `GetFrameworkByID(projectID string, frameworkID string) (*FrameworkContent, error)` (If needed, or `GetLatestFramework` is sufficient)
        *   `GetLatestAnalysisResults(projectID string) (*AnalysisResults, error)`
        *   `GetAnalysisResultsByRunID(projectID string, runID string) (*AnalysisResults, error)`
    *   **Implementation 1: `db_repository.go` (Direct Database Access - Preferred Long-Term)**
        *   Requires database schema knowledge and a Go SQL driver (e.g., `database/sql` + `pq` for PostgreSQL, `go-sqlite3` for SQLite).
        *   Implement the `Repository` interface methods by querying the database directly.
        *   Handle JSON unmarshaling from database columns if data is stored as JSON.
    *   **Implementation 2: `pyapi_repository.go` (Calls to Existing Python API - Interim Option)**
        *   Implement the `Repository` interface methods by making HTTP GET requests to your *existing Python backend's API endpoints* that serve this data.
        *   Use Go's `net/http` client or a library like `go-resty`.
        *   Unmarshal JSON responses into the `models_db.go` structs.

**4. Implement Core Service Logic (`pkg/dataextraction/service.go`):**
    *   Create a `Service` struct that takes a `Repository` instance.
    *   `NewService(repo Repository) *Service` constructor.
    *   Method: `GenerateReportData(projectID string, optionalRunID string) (*models_api_out.StageAOutput, error)`
        *   If `optionalRunID` is empty, call `repo.GetLatestAnalysisResults(projectID)`. Otherwise, call `repo.GetAnalysisResultsByRunID(projectID, optionalRunID)`. Handle errors.
        *   Call `repo.GetLatestFramework(projectID)` (or use framework ID from results). Handle errors.
        *   **Re-implement `AssessmentData` and `FrameworkData` logic in Go:**
            *   Parse the framework: Create a hierarchy map (similar to Python's `_build_hierarchy_map`). Store term aliases.
            *   Process assessment results against the framework hierarchy.
        *   **Calculate Overall Maturity:**
            *   Identify all *leaf* capabilities from the hierarchy.
            *   Get their scores from `AnalysisResults`.
            *   Calculate the median score.
        *   **Calculate Domain Scores (for Spider Chart):**
            *   Iterate through top-level domains (groupings) from the framework hierarchy.
            *   For each domain, find all its descendant leaf capabilities.
            *   Calculate the average score of these leaf capabilities.
            *   Store domain name, average score, and count of capabilities.
        *   **Identify Key Strengths & Critical Areas:**
            *   Sort domains by average score. Select top 3 and bottom 3.
        *   **Populate `ai_insight_context` for Strengths/Focus:**
            *   For each selected strength/focus domain:
                *   Identify its top 2-3 contributing (or lowest scoring for focus) capabilities.
                *   For focus areas: Extract bottleneck indicator details (current score, reasoning, next level target/desc/criteria). This involves logic similar to Python's `_calculate_level_comparison_data` but fetching from the Go structs. Full reasoning text is crucial.
        *   **Populate `context_for_spotlight_and_solutions_ai`:**
            *   Include all domain scores.
            *   List all leaf capabilities with their scores and parent domain.
            *   Identify top N (e.g., 5-10) overall lowest-scoring leaf capabilities and include their indicator scores and reasoning.
            *   Include document names and basic framework overview (name, description, aliases, top-level group names).
        *   Construct and return the `models_api_out.StageAOutput` struct.

**5. Implement API Handlers (`pkg/dataextraction/api/handlers.go`):**
    *   Create an HTTP handler function (e.g., `GenerateReportDataHandler(service *dataextraction.Service)`).
    *   It takes `project_id` and optional `run_id` from URL query parameters.
    *   Calls `service.GenerateReportData()`.
    *   If successful, marshals the `StageAOutput` struct to JSON and writes it to the HTTP response with status 200.
    *   If error, returns an appropriate HTTP error status and JSON error message.

**6. Setup Main Application (`cmd/stage1_data_api/main.go`):**
    *   Load application configuration (DB connection string or Python API base URL).
    *   Initialize the chosen `Repository` implementation.
    *   Initialize the `dataextraction.Service`.
    *   Create an HTTP router (e.g., using `net/http.ServeMux` or a library like `gorilla/mux` or `chi`).
    *   Register the `GenerateReportDataHandler` to an endpoint (e.g., `/api/v1/report-data`).
    *   Start the HTTP server.

**Configuration (`pkg/config/config.go` and `.env`):**
    *   The service will need configuration for:
        *   Database connection details (if `db_repository` is used).
        *   Base URL of the existing Python API (if `pyapi_repository` is used).
        *   Port for this Stage A API service to listen on.

**Testing:**
    *   Unit tests for `service.go` logic (calculations, structuring).
    *   Unit tests for `repository.go` implementations (mocking DB or Python API calls).
    *   Integration tests for the HTTP endpoint.

**Key Considerations for the Coding Model:**

*   **Error Handling:** Implement robust error handling at each step (DB/API calls, data processing, JSON marshaling).
*   **Logging:** Add detailed logging throughout the service.
*   **Concurrency:** Initially, assume requests are handled serially. Concurrency can be added later if needed.
*   **Database Schema/Python API Contract:** The implementation of the `Repository` will heavily depend on the exact structure of the database or the JSON responses from the Python API. Provide this information if possible.
*   **"Latest" Definition:** Clarify how "latest assessment" and "latest framework" are defined if not by simply fetching the most recent by timestamp/ID. (e.g., is there a "published" or "active" flag?). For now, assume latest by creation/upload time.
*   **Data for `ai_insight_context`:** Ensure the `bottleneck_indicators_details` includes enough raw reasoning text for Stage B LLMs to work effectively. Avoid premature summarization in Stage A for these critical inputs.

This detailed task description should provide a solid foundation for the coding model to implement the Go backend API for Stage A. The most complex part will be replicating the data processing logic from the Python `AssessmentData` class into Go within `pkg/dataextraction/service.go`.
