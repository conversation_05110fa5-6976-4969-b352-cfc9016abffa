{"type": "object", "properties": {"overall_maturity_level": {"type": "string", "enum": ["Initial", "Developing", "Defined", "Managed", "Optimizing"], "description": "Overall digital transformation maturity level"}, "domain_scores": {"type": "object", "properties": {"strategic_vision": {"type": "number", "minimum": 1, "maximum": 5, "description": "Score for strategic vision and leadership"}, "data_governance": {"type": "number", "minimum": 1, "maximum": 5, "description": "Score for data governance and management"}, "technology_infrastructure": {"type": "number", "minimum": 1, "maximum": 5, "description": "Score for technology infrastructure"}, "process_automation": {"type": "number", "minimum": 1, "maximum": 5, "description": "Score for process automation"}, "culture_change": {"type": "number", "minimum": 1, "maximum": 5, "description": "Score for organizational culture and change management"}}, "required": ["strategic_vision", "data_governance", "technology_infrastructure", "process_automation", "culture_change"]}, "key_strengths": {"type": "array", "items": {"type": "string"}, "description": "List of key organizational strengths"}, "improvement_areas": {"type": "array", "items": {"type": "string"}, "description": "List of areas needing improvement"}, "recommendations": {"type": "array", "items": {"type": "object", "properties": {"area": {"type": "string", "description": "Area of focus for the recommendation"}, "action": {"type": "string", "description": "Recommended action"}, "priority": {"type": "string", "enum": ["High", "Medium", "Low"], "description": "Priority level"}}, "required": ["area", "action", "priority"]}, "description": "List of specific recommendations"}, "confidence_score": {"type": "number", "minimum": 0, "maximum": 1, "description": "Confidence score for the assessment (0-1)"}}, "required": ["overall_maturity_level", "domain_scores", "key_strengths", "improvement_areas", "recommendations", "confidence_score"]}