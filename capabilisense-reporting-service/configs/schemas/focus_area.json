{"type": "object", "properties": {"focus_title": {"type": "string", "description": "Brief 3-6 word title for focus area in one-pager report"}, "focus_description": {"type": "string", "description": "Concise 2-3 sentence description for one-pager. Maximum 80 words. Focus on why this area is critical."}, "key_recommendations": {"type": "array", "items": {"type": "string"}, "minItems": 2, "maxItems": 3, "description": "List of 2-3 key recommendations for one-pager. Each recommendation should be 10-15 words maximum."}, "implementation_plan": {"type": "string", "description": "Brief 1-2 sentence implementation approach for one-pager. Maximum 50 words."}, "expected_outcomes": {"type": "array", "items": {"type": "string"}, "minItems": 2, "maxItems": 3, "description": "List of 2-3 expected outcomes for one-pager. Each outcome should be 8-12 words maximum."}, "notes": {"type": "string", "description": "Any additional analysis, reasoning, or meta-commentary about the focus area generation. Use this field for any explanatory text or processing notes."}}, "required": ["focus_title", "focus_description", "key_recommendations", "implementation_plan", "expected_outcomes", "notes"]}