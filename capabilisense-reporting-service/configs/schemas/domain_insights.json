{"type": "object", "properties": {"domain_name": {"type": "string", "description": "The exact name of the domain being analyzed"}, "current_state": {"type": "string", "description": "One sentence assessment of current domain state for one-pager report. Maximum 25 words."}, "key_strengths": {"type": "array", "items": {"type": "string"}, "minItems": 1, "maxItems": 2, "description": "List of 1-2 key strengths. Each strength should be 5-10 words maximum."}, "improvement_areas": {"type": "array", "items": {"type": "string"}, "minItems": 1, "maxItems": 2, "description": "List of 1-2 improvement areas. Each area should be 5-10 words maximum."}, "recommendations": {"type": "array", "items": {"type": "string"}, "minItems": 2, "maxItems": 3, "description": "List of 2-3 brief recommendations for one-pager. Each recommendation should be 10-15 words maximum."}, "next_steps": {"type": "array", "items": {"type": "string"}, "minItems": 1, "maxItems": 2, "description": "List of 1-2 immediate next steps. Each step should be 8-12 words maximum."}, "notes": {"type": "string", "description": "Any additional analysis, reasoning, or meta-commentary about the domain assessment. Use this field for any explanatory text or processing notes."}}, "required": ["domain_name", "current_state", "key_strengths", "improvement_areas", "recommendations", "next_steps", "notes"]}