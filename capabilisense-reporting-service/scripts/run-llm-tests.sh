#!/bin/bash

# LLM Library Test Runner
# This script runs the comprehensive test suite for the LLM library

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "go.mod" ] || [ ! -d "pkg/aiinsights" ]; then
    print_error "Please run this script from the capabilisense-reporting-service root directory"
    exit 1
fi

print_status "Starting LLM Library Test Suite"
echo "=================================="

# Check for .env file
if [ -f ".env" ]; then
    print_success "Found .env file for API keys"
    
    # Check which API keys are available
    echo ""
    print_status "Checking available API keys:"
    
    if grep -q "AZURE_OPENAI_KEY=" .env && [ -n "$(grep "AZURE_OPENAI_KEY=" .env | cut -d'=' -f2)" ]; then
        print_success "  ✓ Azure OpenAI API key found"
    else
        print_warning "  ✗ Azure OpenAI API key not found"
    fi
    
    if grep -q "GCP_API_KEY=" .env && [ -n "$(grep "GCP_API_KEY=" .env | cut -d'=' -f2)" ]; then
        print_success "  ✓ Google Cloud API key found"
    else
        print_warning "  ✗ Google Cloud API key not found"
    fi
    
    if grep -q "GOOGLE_API_KEY=" .env && [ -n "$(grep "GOOGLE_API_KEY=" .env | cut -d'=' -f2)" ]; then
        print_success "  ✓ Google API key found"
    else
        print_warning "  ✗ Google API key not found"
    fi
    
    if grep -q "ANTHROPIC_API_KEY=" .env && [ -n "$(grep "ANTHROPIC_API_KEY=" .env | cut -d'=' -f2)" ]; then
        print_success "  ✓ Anthropic API key found"
    else
        print_warning "  ✗ Anthropic API key not found"
    fi
    
    if grep -q "OPENROUTER_API_KEY=" .env && [ -n "$(grep "OPENROUTER_API_KEY=" .env | cut -d'=' -f2)" ]; then
        print_success "  ✓ OpenRouter API key found"
    else
        print_warning "  ✗ OpenRouter API key not found"
    fi
    
    if grep -q "MISTRAL_API_KEY=" .env && [ -n "$(grep "MISTRAL_API_KEY=" .env | cut -d'=' -f2)" ]; then
        print_success "  ✓ Mistral API key found"
    else
        print_warning "  ✗ Mistral API key not found"
    fi
else
    print_warning "No .env file found. Integration tests may be skipped."
    print_status "Create a .env file in the project root with your API keys:"
    echo "  AZURE_OPENAI_KEY=your_azure_key"
    echo "  GCP_API_KEY=your_google_key"
    echo "  GOOGLE_API_KEY=your_google_key"
    echo "  ANTHROPIC_API_KEY=your_anthropic_key"
    echo "  OPENROUTER_API_KEY=your_openrouter_key"
    echo "  MISTRAL_API_KEY=your_mistral_key"
fi

echo ""
print_status "Installing/updating dependencies..."
go mod tidy

echo ""
print_status "Running unit tests..."
echo "========================"

# Run unit tests (excluding integration tests)
if go test -v ./pkg/config -run "^Test[^I]" -timeout 30s; then
    print_success "Configuration tests passed"
else
    print_error "Configuration tests failed"
    exit 1
fi

if go test -v ./pkg/aiinsights -run "^Test[^I]" -timeout 60s; then
    print_success "LLM interface unit tests passed"
else
    print_error "LLM interface unit tests failed"
    exit 1
fi

echo ""
print_status "Running integration tests..."
echo "================================"

# Run integration tests with longer timeout
if go test -v ./pkg/aiinsights -run "^TestIntegration" -timeout 300s; then
    print_success "Integration tests completed"
else
    print_warning "Some integration tests may have failed (this is expected if API keys are not configured)"
fi

echo ""
print_status "Running comprehensive test coverage..."
echo "====================================="

# Run all tests with coverage
if go test -v -cover ./pkg/config ./pkg/aiinsights -timeout 300s; then
    print_success "All tests completed"
else
    print_warning "Some tests may have failed"
fi

echo ""
print_status "Generating test coverage report..."
go test -coverprofile=coverage.out ./pkg/config ./pkg/aiinsights
if [ -f "coverage.out" ]; then
    go tool cover -html=coverage.out -o coverage.html
    print_success "Coverage report generated: coverage.html"
    
    # Show coverage summary
    echo ""
    print_status "Coverage summary:"
    go tool cover -func=coverage.out | tail -1
fi

echo ""
print_status "Running example programs..."
echo "=========================="

# Test the basic LLM test program
print_status "Testing basic LLM interface..."
if timeout 60s go run cmd/llm-test/main.go 2>/dev/null; then
    print_success "Basic LLM test completed"
else
    print_warning "Basic LLM test may have failed (expected if no API keys configured)"
fi

# Test the integration example
print_status "Testing integration example..."
if timeout 60s go run cmd/llm-integration-example/main.go 2>/dev/null; then
    print_success "Integration example completed"
else
    print_warning "Integration example may have failed (expected if no API keys configured)"
fi

echo ""
print_success "LLM Library Test Suite Complete!"
echo "================================="
echo ""
print_status "Summary:"
echo "  ✓ Unit tests verify core functionality without API calls"
echo "  ✓ Integration tests verify real API interactions (when keys available)"
echo "  ✓ Fallback mechanisms are tested"
echo "  ✓ Configuration loading and validation works"
echo "  ✓ Multiple provider support is verified"
echo ""
print_status "Next steps:"
echo "  1. Add API keys to .env file for full integration testing"
echo "  2. Review coverage.html for test coverage details"
echo "  3. Run specific tests: go test -v ./pkg/aiinsights -run TestSpecificFunction"
echo "  4. Run integration tests only: go test -v ./pkg/aiinsights -run TestIntegration"
echo ""
print_success "Happy testing! 🚀"
