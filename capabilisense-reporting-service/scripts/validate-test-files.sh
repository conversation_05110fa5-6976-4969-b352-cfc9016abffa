#!/bin/bash

# Validate that all test files and dependencies are in place

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Validating LLM Library Test Files"
echo "=================================="

# Check if we're in the right directory
if [ ! -f "go.mod" ] || [ ! -d "pkg/aiinsights" ]; then
    print_error "Please run this script from the capabilisense-reporting-service root directory"
    exit 1
fi

# Check prompts library file
if [ -f "configs/prompts_library.json" ]; then
    print_success "✓ prompts_library.json found"
else
    print_error "✗ prompts_library.json not found"
    exit 1
fi

# Check required prompt files
prompt_files=(
    "configs/prompts/frontend_chat_system.txt"
    "configs/prompts/frontend_chat_initial.txt"
    "configs/prompts/time_concerned_assessor.txt"
    "configs/prompts/hyde_generator.txt"
    "configs/prompts/llm_extractor.txt"
)

print_status "Checking prompt files..."
for file in "${prompt_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "  ✓ $file"
    else
        print_error "  ✗ $file missing"
        exit 1
    fi
done

# Check schema files
schema_files=(
    "configs/schemas/maturity_assessment.json"
)

print_status "Checking schema files..."
for file in "${schema_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "  ✓ $file"
    else
        print_error "  ✗ $file missing"
        exit 1
    fi
done

# Check fallback files
fallback_files=(
    "configs/fallbacks/frontend_chat_fallback.txt"
    "configs/fallbacks/maturity_assessor_fallback.json"
    "configs/fallbacks/hyde_generator_fallback.txt"
    "configs/fallbacks/llm_extractor_fallback.txt"
)

print_status "Checking fallback files..."
for file in "${fallback_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "  ✓ $file"
    else
        print_error "  ✗ $file missing"
        exit 1
    fi
done

# Check test files
test_files=(
    "pkg/config/prompts_library_test.go"
    "pkg/config/testhelpers.go"
    "pkg/aiinsights/llm_interface_test.go"
    "pkg/aiinsights/llm_integration_test.go"
    "pkg/aiinsights/llmclient_test.go"
    "pkg/aiinsights/llm_benchmark_test.go"
)

print_status "Checking test files..."
for file in "${test_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "  ✓ $file"
    else
        print_error "  ✗ $file missing"
        exit 1
    fi
done

# Check example programs
example_files=(
    "cmd/llm-test/main.go"
    "cmd/llm-integration-example/main.go"
    "cmd/test-runner/main.go"
)

print_status "Checking example programs..."
for file in "${example_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "  ✓ $file"
    else
        print_warning "  ⚠ $file missing (optional)"
    fi
done

# Check documentation
doc_files=(
    "docs/LLM_LIBRARY.md"
    "docs/TESTING.md"
    ".env.example"
)

print_status "Checking documentation..."
for file in "${doc_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "  ✓ $file"
    else
        print_warning "  ⚠ $file missing (optional)"
    fi
done

# Validate JSON files
print_status "Validating JSON files..."

# Check prompts_library.json syntax
if command -v python3 &> /dev/null; then
    if python3 -m json.tool configs/prompts_library.json > /dev/null 2>&1; then
        print_success "  ✓ prompts_library.json is valid JSON"
    else
        print_error "  ✗ prompts_library.json has invalid JSON syntax"
        exit 1
    fi
else
    print_warning "  ⚠ python3 not found, skipping JSON validation"
fi

# Check schema file syntax
if command -v python3 &> /dev/null; then
    if python3 -m json.tool configs/schemas/maturity_assessment.json > /dev/null 2>&1; then
        print_success "  ✓ maturity_assessment.json is valid JSON"
    else
        print_error "  ✗ maturity_assessment.json has invalid JSON syntax"
        exit 1
    fi
fi

# Check fallback JSON file syntax
if command -v python3 &> /dev/null; then
    if python3 -m json.tool configs/fallbacks/maturity_assessor_fallback.json > /dev/null 2>&1; then
        print_success "  ✓ maturity_assessor_fallback.json is valid JSON"
    else
        print_error "  ✗ maturity_assessor_fallback.json has invalid JSON syntax"
        exit 1
    fi
fi

# Check .env file
if [ -f ".env" ]; then
    print_success "✓ .env file found"
    
    # Count API keys
    key_count=0
    if grep -q "AZURE_OPENAI_KEY=" .env && [ -n "$(grep "AZURE_OPENAI_KEY=" .env | cut -d'=' -f2)" ]; then
        ((key_count++))
    fi
    if grep -q "GCP_API_KEY=" .env && [ -n "$(grep "GCP_API_KEY=" .env | cut -d'=' -f2)" ]; then
        ((key_count++))
    fi
    if grep -q "GOOGLE_API_KEY=" .env && [ -n "$(grep "GOOGLE_API_KEY=" .env | cut -d'=' -f2)" ]; then
        ((key_count++))
    fi
    if grep -q "ANTHROPIC_API_KEY=" .env && [ -n "$(grep "ANTHROPIC_API_KEY=" .env | cut -d'=' -f2)" ]; then
        ((key_count++))
    fi
    if grep -q "OPENROUTER_API_KEY=" .env && [ -n "$(grep "OPENROUTER_API_KEY=" .env | cut -d'=' -f2)" ]; then
        ((key_count++))
    fi
    if grep -q "MISTRAL_API_KEY=" .env && [ -n "$(grep "MISTRAL_API_KEY=" .env | cut -d'=' -f2)" ]; then
        ((key_count++))
    fi
    
    if [ $key_count -gt 0 ]; then
        print_success "  ✓ Found $key_count API keys configured"
    else
        print_warning "  ⚠ No API keys found in .env file"
    fi
else
    print_warning "⚠ .env file not found (integration tests will be skipped)"
    print_status "  Create .env file from .env.example to enable integration testing"
fi

print_status "File size check..."
# Check that files are not empty
for file in "${prompt_files[@]}" "${fallback_files[@]}"; do
    if [ -s "$file" ]; then
        size=$(wc -c < "$file")
        print_success "  ✓ $file ($size bytes)"
    else
        print_error "  ✗ $file is empty"
        exit 1
    fi
done

echo ""
print_success "All required files are present and valid!"
echo "========================================"
echo ""
print_status "Test suite is ready to run:"
echo "  • Unit tests: go test ./pkg/config ./pkg/aiinsights -run '^Test[^I]'"
echo "  • Integration tests: go test ./pkg/aiinsights -run '^TestIntegration'"
echo "  • All tests: ./scripts/run-llm-tests.sh"
echo "  • Quick validation: go run cmd/test-runner/main.go"
echo ""
print_success "Validation complete! 🎉"
