package debug

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// DebugConfig holds debug configuration
type DebugConfig struct {
	Enabled     bool
	LogDir      string
	SaveArtifacts bool
}

// DefaultDebugConfig returns default debug configuration
func DefaultDebugConfig() *DebugConfig {
	return &DebugConfig{
		Enabled:       os.Getenv("DEBUG") == "true" || os.Getenv("DEBUG") == "1",
		LogDir:        "logs/debug",
		SaveArtifacts: true,
	}
}

// DebugLogger handles debug logging and artifact saving
type DebugLogger struct {
	config *DebugConfig
}

// NewDebugLogger creates a new debug logger
func NewDebugLogger() *DebugLogger {
	return &DebugLogger{
		config: DefaultDebugConfig(),
	}
}

// IsEnabled returns true if debug mode is enabled
func (d *DebugLogger) IsEnabled() bool {
	return d.config.Enabled
}

// Log logs a debug message if debug mode is enabled
func (d *DebugLogger) Log(format string, args ...interface{}) {
	if !d.config.Enabled {
		return
	}
	
	timestamp := time.Now().Format("2006-01-02 15:04:05.000")
	message := fmt.Sprintf(format, args...)
	log.Printf("[DEBUG %s] %s", timestamp, message)
}

// SaveJSON saves a JSON object to the debug logs directory
func (d *DebugLogger) SaveJSON(filename string, data interface{}) error {
	if !d.config.Enabled || !d.config.SaveArtifacts {
		return nil
	}

	// Ensure debug directory exists
	if err := os.MkdirAll(d.config.LogDir, 0755); err != nil {
		return fmt.Errorf("failed to create debug directory: %w", err)
	}

	// Add timestamp to filename
	timestamp := time.Now().Format("20060102_150405")
	if !strings.Contains(filename, timestamp) {
		ext := filepath.Ext(filename)
		name := strings.TrimSuffix(filename, ext)
		filename = fmt.Sprintf("%s_%s%s", name, timestamp, ext)
	}

	filePath := filepath.Join(d.config.LogDir, filename)

	// Marshal JSON with indentation for readability
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}

	// Write to file
	if err := os.WriteFile(filePath, jsonData, 0644); err != nil {
		return fmt.Errorf("failed to write debug file: %w", err)
	}

	d.Log("Saved debug JSON: %s (%d bytes)", filePath, len(jsonData))
	return nil
}

// SaveBytes saves binary data to the debug logs directory
func (d *DebugLogger) SaveBytes(filename string, data []byte) error {
	if !d.config.Enabled || !d.config.SaveArtifacts {
		return nil
	}

	// Ensure debug directory exists
	if err := os.MkdirAll(d.config.LogDir, 0755); err != nil {
		return fmt.Errorf("failed to create debug directory: %w", err)
	}

	// Add timestamp to filename
	timestamp := time.Now().Format("20060102_150405")
	if !strings.Contains(filename, timestamp) {
		ext := filepath.Ext(filename)
		name := strings.TrimSuffix(filename, ext)
		filename = fmt.Sprintf("%s_%s%s", name, timestamp, ext)
	}

	filePath := filepath.Join(d.config.LogDir, filename)

	// Write to file
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write debug file: %w", err)
	}

	d.Log("Saved debug file: %s (%d bytes)", filePath, len(data))
	return nil
}

// SaveText saves text data to the debug logs directory
func (d *DebugLogger) SaveText(filename string, text string) error {
	return d.SaveBytes(filename, []byte(text))
}

// LogStageStart logs the start of a processing stage
func (d *DebugLogger) LogStageStart(stage string, projectID string) {
	d.Log("🚀 Starting %s for project: %s", stage, projectID)
}

// LogStageEnd logs the end of a processing stage
func (d *DebugLogger) LogStageEnd(stage string, projectID string, duration time.Duration) {
	d.Log("✅ Completed %s for project: %s (took %v)", stage, projectID, duration)
}

// LogStageError logs an error in a processing stage
func (d *DebugLogger) LogStageError(stage string, projectID string, err error) {
	d.Log("❌ Error in %s for project: %s - %v", stage, projectID, err)
}

// LogLLMCall logs details about an LLM call
func (d *DebugLogger) LogLLMCall(provider string, model string, promptID string, tokensUsed int, duration time.Duration) {
	d.Log("🤖 LLM Call: %s/%s [%s] - %d tokens in %v", provider, model, promptID, tokensUsed, duration)
}

// LogHTTPRequest logs details about an HTTP request
func (d *DebugLogger) LogHTTPRequest(method string, url string, statusCode int, duration time.Duration) {
	// Mask API keys in URL for logging
	maskedURL := maskAPIKeysInURL(url)
	d.Log("🌐 HTTP %s %s -> %d (%v)", method, maskedURL, statusCode, duration)
}

// maskAPIKeysInURL masks API keys in URLs for debug logging
func maskAPIKeysInURL(rawURL string) string {
	// Simple masking for common API key patterns in URLs
	patterns := []string{
		`key=[^&]+`,
		`token=[^&]+`,
		`auth=[^&]+`,
		`sk-[a-zA-Z0-9]+`,
		`AIza[a-zA-Z0-9]+`,
	}
	
	maskedURL := rawURL
	for _, pattern := range patterns {
		maskedURL = strings.ReplaceAll(maskedURL, pattern, "[REDACTED]")
	}
	
	return maskedURL
}

// Global debug logger instance
var globalDebugLogger = NewDebugLogger()

// Log logs a debug message using the global logger
func Log(format string, args ...interface{}) {
	globalDebugLogger.Log(format, args...)
}

// SaveJSON saves JSON using the global logger
func SaveJSON(filename string, data interface{}) error {
	return globalDebugLogger.SaveJSON(filename, data)
}

// SaveBytes saves bytes using the global logger
func SaveBytes(filename string, data []byte) error {
	return globalDebugLogger.SaveBytes(filename, data)
}

// SaveText saves text using the global logger
func SaveText(filename string, text string) error {
	return globalDebugLogger.SaveText(filename, text)
}

// IsEnabled returns true if debug mode is enabled
func IsEnabled() bool {
	return globalDebugLogger.IsEnabled()
}

// LogStageStart logs stage start using the global logger
func LogStageStart(stage string, projectID string) {
	globalDebugLogger.LogStageStart(stage, projectID)
}

// LogStageEnd logs stage end using the global logger
func LogStageEnd(stage string, projectID string, duration time.Duration) {
	globalDebugLogger.LogStageEnd(stage, projectID, duration)
}

// LogStageError logs stage error using the global logger
func LogStageError(stage string, projectID string, err error) {
	globalDebugLogger.LogStageError(stage, projectID, err)
}

// LogLLMCall logs LLM call using the global logger
func LogLLMCall(provider string, model string, promptID string, tokensUsed int, duration time.Duration) {
	globalDebugLogger.LogLLMCall(provider, model, promptID, tokensUsed, duration)
}

// LogHTTPRequest logs HTTP request using the global logger
func LogHTTPRequest(method string, url string, statusCode int, duration time.Duration) {
	globalDebugLogger.LogHTTPRequest(method, url, statusCode, duration)
}
