package pdfgenerator

import (
	"capabilisense-reporting-service/pkg/models" // Adjust module path if different
)

// Renderer defines the interface for PDF generation.
// This allows for different rendering implementations (e.g., mdtopdf, headless Chrome).
type Renderer interface {
	// Render generates a PDF based on the provided ReportData and returns it as a byte slice.
	// It may also take a template identifier if implementations handle multiple templates,
	// but for now, we assume the implementation knows its template (like MDToPDFRenderer does).
	Render(data models.ReportData) ([]byte, error)
}