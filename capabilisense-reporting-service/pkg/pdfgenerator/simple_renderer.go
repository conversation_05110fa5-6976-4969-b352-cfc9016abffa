package pdfgenerator

import (
	"fmt"
	"log"
	"os"
	"time"

	"capabilisense-reporting-service/pkg/charts"
	"capabilisense-reporting-service/pkg/models"

	"github.com/go-pdf/fpdf"
)

// SimplePDFRenderer creates PDFs using direct FPDF calls, bypassing mdtopdf
type SimplePDFRenderer struct {
	TemplateName string
	Title        string
	Author       string
	WithFooter   bool
	Orientation  string
	PageSize     string
}

// NewSimplePDFRenderer creates a new simple PDF renderer
func NewSimplePDFRenderer(templateName, title, author string, withFooter bool, orientation, pageSize string) *SimplePDFRenderer {
	return &SimplePDFRenderer{
		TemplateName: templateName,
		Title:        title,
		Author:       author,
		WithFooter:   withFooter,
		Orientation:  orientation,
		PageSize:     pageSize,
	}
}

// Render generates a PDF using direct FPDF calls
func (r *SimplePDFRenderer) Render(data models.ReportData) ([]byte, error) {
	log.Println("Starting Simple PDF generation...")

	// Create temporary file
	tmpPdfFile, err := os.CreateTemp("", "simple-report-*.pdf")
	if err != nil {
		return nil, fmt.Errorf("failed to create temporary PDF file: %w", err)
	}
	tmpPdfPath := tmpPdfFile.Name()
	tmpPdfFile.Close()
	defer os.Remove(tmpPdfPath)

	// Initialize FPDF
	orientation := "P"
	if r.Orientation == "landscape" || r.Orientation == "L" {
		orientation = "L"
	}

	pdf := fpdf.New(orientation, "mm", r.PageSize, "")
	pdf.AddPage()

	// Set document properties
	if r.Title != "" {
		pdf.SetTitle(r.Title, true)
	}
	if r.Author != "" {
		pdf.SetAuthor(r.Author, true)
	}
	pdf.SetCreator("CapabiliSense Reporting Service", true)
	pdf.SetCreationDate(time.Now())

	// Set up footer if requested
	if r.WithFooter {
		pdf.SetFooterFunc(func() {
			pdf.SetY(-15)
			pdf.SetFont("Arial", "I", 8)
			footerContent := fmt.Sprintf("Page %d/{nb}", pdf.PageNo())
			pdf.CellFormat(0, 10, footerContent, "", 0, "C", false, 0, "")
		})
		pdf.AliasNbPages("")
	}

	// Generate content
	err = r.generateContent(pdf, data)
	if err != nil {
		return nil, fmt.Errorf("failed to generate PDF content: %w", err)
	}

	// Output to file
	err = pdf.OutputFileAndClose(tmpPdfPath)
	if err != nil {
		return nil, fmt.Errorf("failed to output PDF file: %w", err)
	}

	// Read the file
	pdfBytes, err := os.ReadFile(tmpPdfPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read PDF file: %w", err)
	}

	log.Printf("Simple PDF generated successfully (size: %d bytes)", len(pdfBytes))
	return pdfBytes, nil
}

// generateContent creates the PDF content
func (r *SimplePDFRenderer) generateContent(pdf *fpdf.Fpdf, data models.ReportData) error {
	// Title
	pdf.SetFont("Arial", "B", 20)
	pdf.SetTextColor(0, 0, 0)
	pdf.CellFormat(0, 15, fmt.Sprintf("%s Transformation Snapshot", data.OrganizationName), "", 1, "C", false, 0, "")
	pdf.Ln(10)

	// Date
	pdf.SetFont("Arial", "", 12)
	pdf.CellFormat(0, 8, fmt.Sprintf("Generated on: %s", time.Now().Format("January 2, 2006")), "", 1, "L", false, 0, "")
	pdf.Ln(5)

	// Generate and insert rays chart if we have data
	if len(data.SpiderChartData) > 0 {
		err := r.insertRaysChart(pdf, data)
		if err != nil {
			log.Printf("Warning: Failed to insert rays chart: %v", err)
			// Continue without chart
		}
	}

	// Section: Overview
	r.addSection(pdf, "Assessment Overview")
	pdf.SetFont("Arial", "", 11)
	pdf.MultiCell(0, 6, "This report provides a comprehensive analysis of your organization's digital transformation capabilities across key domains.", "", "", false)
	pdf.Ln(5)

	// Section: Key Metrics (if we have data)
	if len(data.SpiderChartData) > 0 {
		r.addSection(pdf, "Domain Scores")
		pdf.SetFont("Arial", "", 11)
		for _, domain := range data.SpiderChartData {
			pdf.CellFormat(0, 6, fmt.Sprintf("- %s: %.1f/5.0", domain.DomainName, domain.Score), "", 1, "L", false, 0, "")
		}
		pdf.Ln(5)
	}

	// Section: Key Strengths
	if len(data.KeyStrengths) > 0 {
		r.addSection(pdf, "Key Strengths")
		pdf.SetFont("Arial", "", 11)
		for _, strength := range data.KeyStrengths {
			pdf.CellFormat(0, 6, fmt.Sprintf("- %s (%.1f/5.0)", strength.DomainName, strength.Score), "", 1, "L", false, 0, "")
			if strength.AIInsightText != "" {
				pdf.SetFont("Arial", "I", 10)
				pdf.MultiCell(0, 5, "  "+strength.AIInsightText, "", "", false)
				pdf.SetFont("Arial", "", 11)
			}
		}
		pdf.Ln(5)
	}

	// Section: Areas for Focus
	if len(data.CriticalAreasFocus) > 0 {
		r.addSection(pdf, "Areas for Focus")
		pdf.SetFont("Arial", "", 11)
		for _, area := range data.CriticalAreasFocus {
			pdf.CellFormat(0, 6, fmt.Sprintf("- %s (%.1f/5.0)", area.DomainName, area.Score), "", 1, "L", false, 0, "")
			if area.AIInsightText != "" {
				pdf.SetFont("Arial", "I", 10)
				pdf.MultiCell(0, 5, "  "+area.AIInsightText, "", "", false)
				pdf.SetFont("Arial", "", 11)
			}
		}
		pdf.Ln(5)
	}

	// Section: AI Spotlight
	if data.AISpotlightTitle != "" {
		r.addSection(pdf, "AI Spotlight: "+data.AISpotlightTitle)
		pdf.SetFont("Arial", "", 11)
		pdf.MultiCell(0, 6, data.AISpotlightText, "", "", false)
		pdf.Ln(5)
	}

	// Section: ETS Solutions
	if len(data.ETSSolutions) > 0 {
		r.addSection(pdf, "Recommended Solutions")
		pdf.SetFont("Arial", "", 11)
		for _, solution := range data.ETSSolutions {
			pdf.SetFont("Arial", "B", 11)
			pdf.CellFormat(0, 6, solution.AreaTitle, "", 1, "L", false, 0, "")
			pdf.SetFont("Arial", "", 11)
			pdf.MultiCell(0, 5, solution.SolutionText, "", "", false)
			pdf.Ln(3)
		}
	}

	// Footer section
	pdf.Ln(10)
	pdf.SetFont("Arial", "I", 10)
	pdf.SetTextColor(128, 128, 128)
	pdf.MultiCell(0, 5, "This report was generated by CapabiliSense AI. For more information, please contact "+data.ContactEmail, "", "C", false)

	return nil
}

// addSection adds a section header
func (r *SimplePDFRenderer) addSection(pdf *fpdf.Fpdf, title string) {
	pdf.SetFont("Arial", "B", 14)
	pdf.SetTextColor(0, 0, 0)
	pdf.CellFormat(0, 10, title, "", 1, "L", false, 0, "")
	pdf.Ln(2)
}

// insertRaysChart generates and inserts a rays chart into the PDF
func (r *SimplePDFRenderer) insertRaysChart(pdf *fpdf.Fpdf, data models.ReportData) error {
	// Create chart directory
	chartDir := "charts"
	os.MkdirAll(chartDir, 0755)

	// Convert spider chart data to chart format
	var domains []charts.DomainData
	for _, domain := range data.SpiderChartData {
		domains = append(domains, charts.DomainData{
			Name:  domain.DomainName,
			Score: domain.Score,
		})
	}

	// Generate chart with ultimate adaptive textured rays (sophisticated + intelligent scaling)
	chartFilename := fmt.Sprintf("adaptive_textured_rays_chart_%d.png", time.Now().Unix())

	// Create unified chart generator for advanced chart types
	unifiedGenerator := charts.NewUnifiedChartGenerator(chartDir)

	// Use the ultimate adaptive textured rays configuration
	config := charts.CreateUnifiedModernConfig(charts.StyleAdaptiveTexturedRays)
	config = charts.SwitchToAdaptiveTexturedRays(config)

	chartPath, err := unifiedGenerator.GenerateChart(
		domains,
		5, // Max level
		fmt.Sprintf("%s Assessment", data.OrganizationName),
		chartFilename,
		config, // Ultimate adaptive textured rays with vector fonts
	)
	if err != nil {
		return fmt.Errorf("failed to generate rays chart: %w", err)
	}

	// Insert chart into PDF
	currentY := pdf.GetY()
	chartWidth := 80.0  // mm
	chartHeight := 80.0 // mm

	// Center the chart
	pageWidth, _ := pdf.GetPageSize()
	chartX := (pageWidth - chartWidth) / 2

	pdf.ImageOptions(chartPath, chartX, currentY, chartWidth, chartHeight, false, fpdf.ImageOptions{}, 0, "")

	// Move cursor below chart
	pdf.SetY(currentY + chartHeight + 10)

	// Clean up chart file
	defer os.Remove(chartPath)

	return nil
}
