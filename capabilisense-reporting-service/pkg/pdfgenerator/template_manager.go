package pdfgenerator

import (
	"bytes"
	"fmt"
	"html/template"
	"os"
	"path/filepath"
	"runtime"
	// "strings" // No longer needed here if sanitizeForTable is removed
)

import "capabilisense-reporting-service/pkg/models"

// GetTemplateFunctions defines custom functions accessible within templates.
func GetTemplateFunctions() template.FuncMap {
	return template.FuncMap{
		"printf": func(format string, a ...interface{}) string {
			return fmt.Sprintf(format, a...)
		},
		// "sanitizeForTable": sanitizeForMarkdownTable, // REMOVED
	}
}

// GenerateMarkdownFromTemplate populates a Markdown template with data.
func GenerateMarkdownFromTemplate(templateName string, data models.ReportData) (string, error) {
	_, b, _, _ := runtime.Caller(0)
	basepath := filepath.Dir(b)
	// Assuming your templates folder is at the root of the service module,
	// and this file is in pkg/pdfgenerator/
	templatePath := filepath.Join(basepath, "..", "..", "templates", templateName)

	tmplContent, err := os.ReadFile(templatePath)
	if err != nil {
		return "", fmt.Errorf("failed to read template file %s: %w", templatePath, err)
	}

	tmpl, err := template.New(templateName).Funcs(GetTemplateFunctions()).Parse(string(tmplContent))
	if err != nil {
		return "", fmt.Errorf("failed to parse template %s: %w", templateName, err)
	}

	var processedMarkdown bytes.Buffer
	if err := tmpl.Execute(&processedMarkdown, data); err != nil {
		return "", fmt.Errorf("failed to execute template %s: %w", templateName, err)
	}

	return processedMarkdown.String(), nil
}