package models

// ReportData holds all the information needed for the PDF report template.
type ReportData struct {
	OrganizationName        string
	AssessmentDate          string
	EvidenceAsOfDate        string
	OverallMaturityScore    float64
	OverallSummaryStatement string
	SpiderChartData         []DomainScoreData   // For textual representation for now
	KeyStrengths            []StrengthFocusData
	CriticalAreasFocus      []StrengthFocusData
	AISpotlightTitle        string
	AISpotlightText         string
	ETSSolutions            []SolutionData
	CurrentYear             int
	ContactEmail            string
}

type DomainScoreData struct {
	DomainName string
	Score      float64
}

type StrengthFocusData struct {
	DomainName    string
	Score         float64
	AIInsightText string
}

type SolutionData struct {
	AreaTitle    string
	SolutionText string
}