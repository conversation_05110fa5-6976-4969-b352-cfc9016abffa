package aiinsights

import (
	"context"
	"fmt"
	"log"
	"time"

	"capabilisense-reporting-service/pkg/config"
	"capabilisense-reporting-service/pkg/dataextraction"
)

// InsightGenerator orchestrates the generation of AI insights for reports
type InsightGenerator struct {
	clientFactory  *LLMClientFactory
	promptsLibrary *config.PromptsLibrary
	defaultTimeout time.Duration
}

// NewInsightGenerator creates a new insight generator
func NewInsightGenerator(promptsLibrary *config.PromptsLibrary) *InsightGenerator {
	return &InsightGenerator{
		clientFactory:  NewLLMClientFactory(promptsLibrary),
		promptsLibrary: promptsLibrary,
		defaultTimeout: 30 * time.Second,
	}
}

// GenerateAllInsights generates all AI insights for a processed data set
func (ig *InsightGenerator) GenerateAllInsights(ctx context.Context, processedData *dataextraction.ProcessedData) (*InsightGenerationResult, error) {
	if processedData == nil {
		return nil, fmt.Errorf("processed data cannot be nil")
	}

	startTime := time.Now()
	result := &InsightGenerationResult{
		GenerationMetadata: GenerationMetadata{
			GeneratedAt:    startTime,
			ProvidersUsed:  []string{},
			FallbacksUsed:  make(map[string]bool),
			QualityMetrics: make(map[string]float64),
			Errors:         []string{},
		},
	}

	// Generate organization insights
	orgInsight, err := ig.generateOrganizationInsight(ctx, processedData)
	if err != nil {
		log.Printf("Error generating organization insight: %v", err)
		result.GenerationMetadata.Errors = append(result.GenerationMetadata.Errors, fmt.Sprintf("Organization insight: %v", err))
		// Use fallback
		orgInsight = &OrganizationInsight{
			Name:  "Organization Name Not Available",
			Focus: "Focus area not determined",
		}
	}
	result.OrganizationInsight = *orgInsight

	// Generate overall summary
	summary, err := ig.generateOverallSummary(ctx, processedData)
	if err != nil {
		log.Printf("Error generating overall summary: %v", err)
		result.GenerationMetadata.Errors = append(result.GenerationMetadata.Errors, fmt.Sprintf("Overall summary: %v", err))
		// Use fallback
		summary = &OverallSummary{
			Statement:     "Overall assessment summary not available",
			KeyThemes:     []string{"Assessment completed"},
			MaturityLevel: "Not determined",
			Confidence:    0.0,
		}
	}
	result.OverallSummary = *summary

	// Generate domain insights
	domainInsights, err := ig.generateDomainInsights(ctx, processedData)
	if err != nil {
		log.Printf("Error generating domain insights: %v", err)
		result.GenerationMetadata.Errors = append(result.GenerationMetadata.Errors, fmt.Sprintf("Domain insights: %v", err))
		domainInsights = []DomainInsightAI{} // Empty slice as fallback
	}
	result.DomainInsights = domainInsights

	// Generate AI spotlight
	spotlight, err := ig.generateAISpotlight(ctx, processedData)
	if err != nil {
		log.Printf("Error generating AI spotlight: %v", err)
		result.GenerationMetadata.Errors = append(result.GenerationMetadata.Errors, fmt.Sprintf("AI spotlight: %v", err))
		// Use fallback
		spotlight = &AISpotlight{
			Title:      "Key Insight",
			Content:    "Detailed insights are not available at this time",
			Confidence: 0.0,
		}
	}
	result.AISpotlight = *spotlight

	// Generate ETS solutions
	solutions, err := ig.generateETSSolutions(ctx, processedData)
	if err != nil {
		log.Printf("Error generating ETS solutions: %v", err)
		result.GenerationMetadata.Errors = append(result.GenerationMetadata.Errors, fmt.Sprintf("ETS solutions: %v", err))
		solutions = []ETSSolution{} // Empty slice as fallback
	}
	result.ETSSolutions = solutions

	// Finalize metadata
	result.GenerationMetadata.TotalProcessingTime = time.Since(startTime)
	result.GenerationMetadata.QualityMetrics["success_rate"] = ig.calculateSuccessRate(result)

	return result, nil
}

// generateOrganizationInsight generates insights about the organization
func (ig *InsightGenerator) generateOrganizationInsight(ctx context.Context, processedData *dataextraction.ProcessedData) (*OrganizationInsight, error) {
	_, err := ig.promptsLibrary.GetPrompt("organization_name")
	if err != nil {
		return nil, fmt.Errorf("failed to get organization prompt: %w", err)
	}

	client, err := ig.clientFactory.CreateClient("mock") // Use mock for now
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}

	request := InsightRequest{
		PromptID:  "organization_name",
		RequestID: fmt.Sprintf("org_%d", time.Now().Unix()),
		InputData: map[string]interface{}{
			"domain_scores":  processedData.AnalysisResults.DomainScores,
			"top_strengths":  processedData.TopStrengths,
			"critical_areas": processedData.CriticalAreas,
			"overall_score":  processedData.OverallScore,
		},
		Timestamp: time.Now(),
	}

	response, err := client.GenerateInsight(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to generate organization insight: %w", err)
	}

	// Extract structured data
	name := "Unknown Organization"
	focus := "Not determined"

	if response.StructuredData != nil {
		if n, ok := response.StructuredData["name"].(string); ok {
			name = n
		}
		if f, ok := response.StructuredData["focus"].(string); ok {
			focus = f
		}
	}

	return &OrganizationInsight{
		Name:  name,
		Focus: focus,
	}, nil
}

// generateOverallSummary generates the overall assessment summary
func (ig *InsightGenerator) generateOverallSummary(ctx context.Context, processedData *dataextraction.ProcessedData) (*OverallSummary, error) {
	client, err := ig.clientFactory.CreateClient("mock")
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}

	request := InsightRequest{
		PromptID:  "overall_summary",
		RequestID: fmt.Sprintf("summary_%d", time.Now().Unix()),
		InputData: map[string]interface{}{
			"overall_score":   processedData.OverallScore,
			"domain_rankings": processedData.DomainRankings,
			"top_strengths":   processedData.TopStrengths,
			"critical_areas":  processedData.CriticalAreas,
			"maturity_level":  processedData.ComparisonMetrics["maturity_level"],
		},
		Timestamp: time.Now(),
	}

	response, err := client.GenerateInsight(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to generate overall summary: %w", err)
	}

	// Extract structured data
	statement := response.Content
	keyThemes := []string{}
	maturityLevel := "Not determined"
	confidence := response.Confidence

	if response.StructuredData != nil {
		if s, ok := response.StructuredData["statement"].(string); ok {
			statement = s
		}
		if themes, ok := response.StructuredData["key_themes"].([]string); ok {
			keyThemes = themes
		} else if themesInterface, ok := response.StructuredData["key_themes"].([]interface{}); ok {
			for _, theme := range themesInterface {
				if themeStr, ok := theme.(string); ok {
					keyThemes = append(keyThemes, themeStr)
				}
			}
		}
		if ml, ok := response.StructuredData["maturity_level"].(string); ok {
			maturityLevel = ml
		}
	}

	return &OverallSummary{
		Statement:     statement,
		KeyThemes:     keyThemes,
		MaturityLevel: maturityLevel,
		Confidence:    confidence,
	}, nil
}

// generateDomainInsights generates insights for specific domains
func (ig *InsightGenerator) generateDomainInsights(ctx context.Context, processedData *dataextraction.ProcessedData) ([]DomainInsightAI, error) {
	client, err := ig.clientFactory.CreateClient("mock")
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}

	var insights []DomainInsightAI

	// Generate insights for top strengths
	for _, strength := range processedData.TopStrengths {
		request := InsightRequest{
			PromptID:  "domain_insights",
			RequestID: fmt.Sprintf("strength_%s_%d", strength.DomainID, time.Now().Unix()),
			InputData: map[string]interface{}{
				"domain_id":    strength.DomainID,
				"domain_name":  strength.DomainName,
				"score":        strength.Score,
				"insight_type": "strength",
				"key_findings": strength.KeyFindings,
				"data_points":  strength.DataPoints,
			},
			Timestamp: time.Now(),
		}

		response, err := client.GenerateInsight(ctx, request)
		if err != nil {
			log.Printf("Error generating insight for strength %s: %v", strength.DomainID, err)
			continue
		}

		insight := DomainInsightAI{
			DomainID:    strength.DomainID,
			DomainName:  strength.DomainName,
			InsightType: "strength",
			Title:       fmt.Sprintf("Strong Performance in %s", strength.DomainName),
			Description: response.Content,
			KeyPoints:   strength.KeyFindings,
			Confidence:  response.Confidence,
		}

		if response.StructuredData != nil {
			if title, ok := response.StructuredData["title"].(string); ok {
				insight.Title = title
			}
			if desc, ok := response.StructuredData["description"].(string); ok {
				insight.Description = desc
			}
		}

		insights = append(insights, insight)
	}

	// Generate insights for critical areas
	for _, critical := range processedData.CriticalAreas {
		request := InsightRequest{
			PromptID:  "domain_insights",
			RequestID: fmt.Sprintf("critical_%s_%d", critical.DomainID, time.Now().Unix()),
			InputData: map[string]interface{}{
				"domain_id":    critical.DomainID,
				"domain_name":  critical.DomainName,
				"score":        critical.Score,
				"insight_type": "focus_area",
				"key_findings": critical.KeyFindings,
				"data_points":  critical.DataPoints,
			},
			Timestamp: time.Now(),
		}

		response, err := client.GenerateInsight(ctx, request)
		if err != nil {
			log.Printf("Error generating insight for critical area %s: %v", critical.DomainID, err)
			continue
		}

		insight := DomainInsightAI{
			DomainID:    critical.DomainID,
			DomainName:  critical.DomainName,
			InsightType: "focus_area",
			Title:       fmt.Sprintf("Improvement Opportunity in %s", critical.DomainName),
			Description: response.Content,
			KeyPoints:   critical.KeyFindings,
			Confidence:  response.Confidence,
		}

		if response.StructuredData != nil {
			if title, ok := response.StructuredData["title"].(string); ok {
				insight.Title = title
			}
			if desc, ok := response.StructuredData["description"].(string); ok {
				insight.Description = desc
			}
		}

		insights = append(insights, insight)
	}

	return insights, nil
}

// generateAISpotlight generates the AI spotlight section
func (ig *InsightGenerator) generateAISpotlight(ctx context.Context, processedData *dataextraction.ProcessedData) (*AISpotlight, error) {
	client, err := ig.clientFactory.CreateClient("mock")
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}

	request := InsightRequest{
		PromptID:  "ai_spotlight",
		RequestID: fmt.Sprintf("spotlight_%d", time.Now().Unix()),
		InputData: map[string]interface{}{
			"overall_score":      processedData.OverallScore,
			"domain_rankings":    processedData.DomainRankings,
			"critical_areas":     processedData.CriticalAreas,
			"comparison_metrics": processedData.ComparisonMetrics,
		},
		Timestamp: time.Now(),
	}

	response, err := client.GenerateInsight(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to generate AI spotlight: %w", err)
	}

	spotlight := &AISpotlight{
		Title:      "Key Insight",
		Content:    response.Content,
		Confidence: response.Confidence,
	}

	if response.StructuredData != nil {
		if title, ok := response.StructuredData["title"].(string); ok {
			spotlight.Title = title
		}
		if content, ok := response.StructuredData["content"].(string); ok {
			spotlight.Content = content
		}
		if category, ok := response.StructuredData["category"].(string); ok {
			spotlight.Category = category
		}
		if impact, ok := response.StructuredData["impact"].(string); ok {
			spotlight.Impact = impact
		}
	}

	return spotlight, nil
}

// generateETSSolutions generates ETS solution recommendations
func (ig *InsightGenerator) generateETSSolutions(ctx context.Context, processedData *dataextraction.ProcessedData) ([]ETSSolution, error) {
	client, err := ig.clientFactory.CreateClient("mock")
	if err != nil {
		return nil, fmt.Errorf("failed to create LLM client: %w", err)
	}

	var solutions []ETSSolution

	// Generate solutions based on critical areas
	for _, critical := range processedData.CriticalAreas {
		request := InsightRequest{
			PromptID:  "ets_solutions",
			RequestID: fmt.Sprintf("solution_%s_%d", critical.DomainID, time.Now().Unix()),
			InputData: map[string]interface{}{
				"domain_id":    critical.DomainID,
				"domain_name":  critical.DomainName,
				"score":        critical.Score,
				"key_findings": critical.KeyFindings,
				"data_points":  critical.DataPoints,
			},
			Timestamp: time.Now(),
		}

		response, err := client.GenerateInsight(ctx, request)
		if err != nil {
			log.Printf("Error generating ETS solution for %s: %v", critical.DomainID, err)
			continue
		}

		solution := ETSSolution{
			AreaTitle:   critical.DomainName,
			Description: response.Content,
			Priority:    "Medium",
			Confidence:  response.Confidence,
		}

		if response.StructuredData != nil {
			if title, ok := response.StructuredData["area_title"].(string); ok {
				solution.AreaTitle = title
			}
			if desc, ok := response.StructuredData["description"].(string); ok {
				solution.Description = desc
			}
			if priority, ok := response.StructuredData["priority"].(string); ok {
				solution.Priority = priority
			}
			if benefits, ok := response.StructuredData["benefits"].([]interface{}); ok {
				for _, benefit := range benefits {
					if benefitStr, ok := benefit.(string); ok {
						solution.Benefits = append(solution.Benefits, benefitStr)
					}
				}
			}
		}

		solutions = append(solutions, solution)
	}

	return solutions, nil
}

// calculateSuccessRate calculates the success rate of insight generation
func (ig *InsightGenerator) calculateSuccessRate(result *InsightGenerationResult) float64 {
	totalSections := 5.0 // org, summary, domains, spotlight, solutions
	successfulSections := 0.0

	if result.OrganizationInsight.Name != "Organization Name Not Available" {
		successfulSections++
	}
	if result.OverallSummary.Statement != "Overall assessment summary not available" {
		successfulSections++
	}
	if len(result.DomainInsights) > 0 {
		successfulSections++
	}
	if result.AISpotlight.Title != "Key Insight" || result.AISpotlight.Content != "Detailed insights are not available at this time" {
		successfulSections++
	}
	if len(result.ETSSolutions) > 0 {
		successfulSections++
	}

	return successfulSections / totalSections
}
