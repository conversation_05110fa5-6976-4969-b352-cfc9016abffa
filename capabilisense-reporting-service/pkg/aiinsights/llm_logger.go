package aiinsights

import (
	"encoding/json"
	"fmt"
	"net/url"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"sync"
	"time"

	"capabilisense-reporting-service/pkg/config"
)

// LLMLogEntry represents a log entry for LLM calls
type LLMLogEntry struct {
	Timestamp          string                 `json:"timestamp"`
	Level              string                 `json:"level"`
	EventType          string                 `json:"event_type"`
	ProviderAlias      string                 `json:"provider_alias"`
	ProviderURL        string                 `json:"provider_url"`
	ModelName          string                 `json:"model_name"`
	RunID              string                 `json:"run_id,omitempty"`
	IndicatorID        string                 `json:"indicator_id,omitempty"`
	RequestID          string                 `json:"request_id,omitempty"`
	PromptID           string                 `json:"prompt_id,omitempty"`
	RequestHeaders     map[string]string      `json:"request_headers,omitempty"`
	RequestPayload     map[string]interface{} `json:"request_payload,omitempty"`
	RequestTimestamp   string                 `json:"request_timestamp,omitempty"`
	ResponseStatusCode int                    `json:"response_status_code,omitempty"`
	ResponseBody       map[string]interface{} `json:"response_body,omitempty"`
	ErrorMessage       string                 `json:"error_message,omitempty"`
	ProcessingTime     string                 `json:"processing_time,omitempty"`
	TokensUsed         int                    `json:"tokens_used,omitempty"`
	UsedFallback       bool                   `json:"used_fallback,omitempty"`
}

// LLMLogger handles logging of LLM calls to JSONL format
type LLMLogger struct {
	logFile string
	mutex   sync.Mutex
}

// NewLLMLogger creates a new LLM logger
func NewLLMLogger() *LLMLogger {
	return &LLMLogger{
		logFile: "logs/llm-calls.jsonl",
	}
}

// ensureLogDir creates the logs directory if it doesn't exist
func (l *LLMLogger) ensureLogDir() error {
	logDir := filepath.Dir(l.logFile)
	return os.MkdirAll(logDir, 0755)
}

// LogRequest logs an LLM request
func (l *LLMLogger) LogRequest(
	providerAlias string,
	modelConfig *config.ModelConfig,
	requestID string,
	promptID string,
	payload map[string]interface{},
	headers map[string]string,
) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	entry := LLMLogEntry{
		Timestamp:      time.Now().UTC().Format(time.RFC3339Nano),
		Level:          "INFO",
		EventType:      "llm_request",
		ProviderAlias:  providerAlias,
		ProviderURL:    modelConfig.ProviderURL,
		ModelName:      modelConfig.ModelName,
		RequestID:      requestID,
		PromptID:       promptID,
		RequestHeaders: headers,
		RequestPayload: payload,
	}

	return l.writeLogEntry(entry)
}

// LogResponse logs an LLM response
func (l *LLMLogger) LogResponse(
	providerAlias string,
	modelConfig *config.ModelConfig,
	requestID string,
	promptID string,
	requestTimestamp time.Time,
	statusCode int,
	responseBody map[string]interface{},
	errorMessage string,
	processingTime time.Duration,
	tokensUsed int,
	usedFallback bool,
) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	entry := LLMLogEntry{
		Timestamp:          time.Now().UTC().Format(time.RFC3339Nano),
		Level:              "INFO",
		EventType:          "llm_response",
		ProviderAlias:      providerAlias,
		ProviderURL:        modelConfig.ProviderURL,
		ModelName:          modelConfig.ModelName,
		RequestID:          requestID,
		PromptID:           promptID,
		RequestTimestamp:   requestTimestamp.UTC().Format(time.RFC3339Nano),
		ResponseStatusCode: statusCode,
		ResponseBody:       responseBody,
		ProcessingTime:     processingTime.String(),
		TokensUsed:         tokensUsed,
		UsedFallback:       usedFallback,
	}

	if errorMessage != "" {
		entry.ErrorMessage = errorMessage
	}

	return l.writeLogEntry(entry)
}

// LogFallback logs when a fallback response is used
func (l *LLMLogger) LogFallback(
	requestID string,
	promptID string,
	lastError string,
	processingTime time.Duration,
) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	entry := LLMLogEntry{
		Timestamp:      time.Now().UTC().Format(time.RFC3339Nano),
		Level:          "INFO",
		EventType:      "llm_fallback",
		ProviderAlias:  "fallback",
		ProviderURL:    "file",
		ModelName:      "fallback",
		RequestID:      requestID,
		PromptID:       promptID,
		ErrorMessage:   lastError,
		ProcessingTime: processingTime.String(),
		UsedFallback:   true,
	}

	return l.writeLogEntry(entry)
}

// LogError logs an LLM error
func (l *LLMLogger) LogError(
	providerAlias string,
	modelConfig *config.ModelConfig,
	requestID string,
	promptID string,
	errorMessage string,
	processingTime time.Duration,
) error {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	entry := LLMLogEntry{
		Timestamp:      time.Now().UTC().Format(time.RFC3339Nano),
		Level:          "ERROR",
		EventType:      "llm_error",
		ProviderAlias:  providerAlias,
		RequestID:      requestID,
		PromptID:       promptID,
		ErrorMessage:   errorMessage,
		ProcessingTime: processingTime.String(),
	}

	if modelConfig != nil {
		entry.ProviderURL = modelConfig.ProviderURL
		entry.ModelName = modelConfig.ModelName
	}

	return l.writeLogEntry(entry)
}

// maskAPIKeysInURL masks API keys in URLs for logging
func (l *LLMLogger) maskAPIKeysInURL(rawURL string) string {
	// Parse the URL
	u, err := url.Parse(rawURL)
	if err != nil {
		return rawURL // Return original if parsing fails
	}

	// Mask specific query parameters that contain API keys
	query := u.Query()
	for key := range query {
		lowerKey := strings.ToLower(key)
		// Be specific about which query parameters to mask
		if lowerKey == "key" || lowerKey == "api_key" || lowerKey == "apikey" ||
			lowerKey == "access_token" || lowerKey == "auth_token" || lowerKey == "token" {
			query.Set(key, "[REDACTED]")
		}
	}
	u.RawQuery = query.Encode()

	return u.String()
}

// maskSensitiveData masks sensitive information in log entries
func (l *LLMLogger) maskSensitiveData(entry *LLMLogEntry) {
	// Mask API keys in URL
	entry.ProviderURL = l.maskAPIKeysInURL(entry.ProviderURL)

	// Mask sensitive headers (already done in LLM interface, but double-check)
	for key, value := range entry.RequestHeaders {
		lowerKey := strings.ToLower(key)
		if strings.Contains(lowerKey, "auth") || strings.Contains(lowerKey, "key") || strings.Contains(lowerKey, "token") {
			entry.RequestHeaders[key] = "[REDACTED]"
		} else if strings.Contains(value, "Bearer ") || strings.Contains(value, "sk-") || strings.Contains(value, "AIza") {
			// Mask common API key patterns
			entry.RequestHeaders[key] = "[REDACTED]"
		}
	}

	// Mask sensitive data in request payload
	if entry.RequestPayload != nil {
		l.maskSensitiveInMap(entry.RequestPayload)
	}

	// Mask sensitive data in response body
	if entry.ResponseBody != nil {
		l.maskSensitiveInMap(entry.ResponseBody)
	}
}

// maskSensitiveInMap recursively masks sensitive data in maps
func (l *LLMLogger) maskSensitiveInMap(data map[string]interface{}) {
	for key, value := range data {
		lowerKey := strings.ToLower(key)

		// Mask specific sensitive keys (be more targeted)
		if lowerKey == "api_key" || lowerKey == "apikey" || lowerKey == "access_token" ||
			lowerKey == "refresh_token" || lowerKey == "auth_token" || lowerKey == "bearer_token" ||
			strings.Contains(lowerKey, "password") || strings.Contains(lowerKey, "secret") {
			data[key] = "[REDACTED]"
			continue
		}

		// Recursively check nested maps
		if nestedMap, ok := value.(map[string]interface{}); ok {
			l.maskSensitiveInMap(nestedMap)
		}

		// Check string values for API key patterns
		if strValue, ok := value.(string); ok {
			// Common API key patterns
			apiKeyPatterns := []string{
				`sk-[a-zA-Z0-9]{20,}`,    // OpenAI style
				`AIza[a-zA-Z0-9]{35}`,    // Google API key
				`Bearer [a-zA-Z0-9\-_]+`, // Bearer tokens
			}

			for _, pattern := range apiKeyPatterns {
				if matched, _ := regexp.MatchString(pattern, strValue); matched {
					data[key] = "[REDACTED]"
					break
				}
			}
		}
	}
}

// writeLogEntry writes a log entry to the JSONL file
func (l *LLMLogger) writeLogEntry(entry LLMLogEntry) error {
	// Mask sensitive data before logging
	l.maskSensitiveData(&entry)

	// Ensure log directory exists
	if err := l.ensureLogDir(); err != nil {
		return fmt.Errorf("failed to create log directory: %w", err)
	}

	// Open file for appending
	file, err := os.OpenFile(l.logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("failed to open log file: %w", err)
	}
	defer file.Close()

	// Marshal to JSON
	jsonData, err := json.Marshal(entry)
	if err != nil {
		return fmt.Errorf("failed to marshal log entry: %w", err)
	}

	// Write JSON line
	if _, err := file.Write(jsonData); err != nil {
		return fmt.Errorf("failed to write log entry: %w", err)
	}

	// Write newline
	if _, err := file.Write([]byte("\n")); err != nil {
		return fmt.Errorf("failed to write newline: %w", err)
	}

	return nil
}

// SetLogFile allows changing the log file path (useful for testing)
func (l *LLMLogger) SetLogFile(path string) {
	l.mutex.Lock()
	defer l.mutex.Unlock()
	l.logFile = path
}
