package aiinsights

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"capabilisense-reporting-service/pkg/config"
)

// Integration tests that require real API keys
// These tests will be skipped if API keys are not available

func TestIntegrationCallLLMWithRealProviders(t *testing.T) {
	// Load test environment
	config.LoadTestEnv()
	
	// Get available providers
	availableProviders := config.GetAvailableProviders()
	if len(availableProviders) == 0 {
		t.Skip("No API keys available for integration testing")
	}

	library := loadTestLibrary(t)
	llm := NewLLMInterface(library)

	// Test basic chat with available providers
	for _, promptID := range []string{"frontend_chat"} {
		t.Run(fmt.Sprintf("prompt_%s", promptID), func(t *testing.T) {
			request := LLMRequest{
				PromptID:  promptID,
				UserQuery: "Hello! Can you briefly explain what digital transformation means?",
				RequestID: fmt.Sprintf("integration-test-%d", time.Now().Unix()),
			}

			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			defer cancel()

			response, err := llm.CallLLM(ctx, request)
			
			if err != nil {
				t.Logf("LLM call failed (this may be expected if no API keys are configured): %v", err)
				// Check if it used fallback
				if response != nil && response.UsedFallback {
					assert.True(t, response.UsedFallback)
					assert.NotEmpty(t, response.Content)
					t.Logf("Successfully used fallback response")
				}
				return
			}

			// If successful, validate response
			require.NotNil(t, response)
			assert.Equal(t, request.RequestID, response.RequestID)
			assert.Equal(t, request.PromptID, response.PromptID)
			assert.NotEmpty(t, response.Content)
			assert.NotEmpty(t, response.Provider)
			assert.NotEmpty(t, response.Model)
			assert.Greater(t, response.ProcessingTime, time.Duration(0))
			assert.False(t, response.UsedFallback)

			t.Logf("✓ Success with provider: %s, model: %s", response.Provider, response.Model)
			t.Logf("  Content length: %d characters", len(response.Content))
			t.Logf("  Tokens used: %d", response.TokensUsed)
			t.Logf("  Processing time: %v", response.ProcessingTime)
		})
	}
}

func TestIntegrationStructuredOutput(t *testing.T) {
	// Load test environment
	config.LoadTestEnv()
	
	// Get available providers
	availableProviders := config.GetAvailableProviders()
	if len(availableProviders) == 0 {
		t.Skip("No API keys available for integration testing")
	}

	library := loadTestLibrary(t)
	llm := NewLLMInterface(library)

	// Test structured output with maturity assessor
	request := LLMRequest{
		PromptID: "maturity_assessor",
		UserQuery: "Assess the digital transformation maturity of a mid-size technology company with strong leadership commitment, good technology infrastructure, but weak data governance and limited process automation capabilities.",
		RequestID: fmt.Sprintf("structured-test-%d", time.Now().Unix()),
	}

	ctx, cancel := context.WithTimeout(context.Background(), 45*time.Second)
	defer cancel()

	response, err := llm.CallLLM(ctx, request)
	
	if err != nil {
		t.Logf("Structured LLM call failed (this may be expected if no API keys are configured): %v", err)
		// Check if it used fallback
		if response != nil && response.UsedFallback {
			assert.True(t, response.UsedFallback)
			assert.NotEmpty(t, response.Content)
			t.Logf("Successfully used structured fallback response")
		}
		return
	}

	// If successful, validate response
	require.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.False(t, response.UsedFallback)

	t.Logf("✓ Structured output success with provider: %s", response.Provider)
	t.Logf("  Content length: %d characters", len(response.Content))
	t.Logf("  Structured data available: %t", response.StructuredData != nil)
	
	if response.StructuredData != nil {
		t.Logf("  Structured data keys: %v", getKeys(response.StructuredData))
	}
}

func TestIntegrationChatHistory(t *testing.T) {
	// Load test environment
	config.LoadTestEnv()
	
	// Get available providers
	availableProviders := config.GetAvailableProviders()
	if len(availableProviders) == 0 {
		t.Skip("No API keys available for integration testing")
	}

	library := loadTestLibrary(t)
	llm := NewLLMInterface(library)

	// Test with chat history
	request := LLMRequest{
		PromptID: "frontend_chat",
		ChatHistory: []ChatMessage{
			{
				Role:    "user",
				Content: "What are the key domains of digital transformation?",
			},
			{
				Role:    "assistant",
				Content: "The key domains of digital transformation typically include Strategic Vision & Leadership, Data Governance & Management, Technology Infrastructure, Process Automation, and Organizational Culture & Change Management.",
			},
		},
		UserQuery: "Can you give me specific examples of process automation in digital transformation?",
		RequestID: fmt.Sprintf("chat-history-test-%d", time.Now().Unix()),
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response, err := llm.CallLLM(ctx, request)
	
	if err != nil {
		t.Logf("Chat history LLM call failed: %v", err)
		return
	}

	// If successful, validate response
	require.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	assert.False(t, response.UsedFallback)

	t.Logf("✓ Chat history success with provider: %s", response.Provider)
	t.Logf("  Response mentions process automation: %t", 
		containsIgnoreCase(response.Content, "process") || containsIgnoreCase(response.Content, "automation"))
}

func TestIntegrationProviderFallback(t *testing.T) {
	// Load test environment
	config.LoadTestEnv()

	// Create a library with invalid provider first, then valid ones
	library := loadTestLibrary(t)
	
	// Modify the frontend_chat prompt to have an invalid provider first
	if prompt, exists := library.Prompts["frontend_chat"]; exists {
		// Add invalid provider at the beginning
		prompt.ModelAliases = append([]string{"invalid_provider"}, prompt.ModelAliases...)
		library.Prompts["frontend_chat"] = prompt
		
		// Add invalid provider config
		library.Models["invalid_provider"] = config.ModelConfig{
			ProviderURL: "https://invalid.example.com/api",
			ModelName:   "invalid-model",
			APIKeyEnv:   "INVALID_API_KEY",
		}
	}

	llm := NewLLMInterface(library)

	request := LLMRequest{
		PromptID:  "frontend_chat",
		UserQuery: "Hello, this is a fallback test.",
		RequestID: fmt.Sprintf("fallback-test-%d", time.Now().Unix()),
	}

	ctx, cancel := context.WithTimeout(context.Background(), 45*time.Second)
	defer cancel()

	response, err := llm.CallLLM(ctx, request)
	
	// Should either succeed with a valid provider or use fallback
	if err != nil {
		t.Logf("All providers failed, which is expected if no API keys are configured: %v", err)
		return
	}

	require.NotNil(t, response)
	assert.NotEmpty(t, response.Content)
	
	if response.UsedFallback {
		assert.Equal(t, "fallback", response.Provider)
		t.Logf("✓ Successfully used fallback after provider failures")
	} else {
		assert.NotEqual(t, "invalid_provider", response.Provider)
		t.Logf("✓ Successfully fell back to valid provider: %s", response.Provider)
	}
}

func TestIntegrationPerformanceBaseline(t *testing.T) {
	// Load test environment
	config.LoadTestEnv()
	
	// Get available providers
	availableProviders := config.GetAvailableProviders()
	if len(availableProviders) == 0 {
		t.Skip("No API keys available for performance testing")
	}

	library := loadTestLibrary(t)
	llm := NewLLMInterface(library)

	// Test performance with a simple request
	request := LLMRequest{
		PromptID:  "frontend_chat",
		UserQuery: "Hi",
		RequestID: fmt.Sprintf("perf-test-%d", time.Now().Unix()),
	}

	start := time.Now()
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response, err := llm.CallLLM(ctx, request)
	totalTime := time.Since(start)
	
	if err != nil {
		t.Logf("Performance test failed: %v", err)
		return
	}

	require.NotNil(t, response)
	
	t.Logf("✓ Performance baseline:")
	t.Logf("  Provider: %s", response.Provider)
	t.Logf("  Total time: %v", totalTime)
	t.Logf("  Processing time: %v", response.ProcessingTime)
	t.Logf("  Tokens used: %d", response.TokensUsed)
	
	// Basic performance assertions
	assert.Less(t, totalTime, 30*time.Second, "Request should complete within 30 seconds")
	assert.Greater(t, response.ProcessingTime, time.Duration(0), "Processing time should be positive")
}

// Helper functions
func getKeys(m map[string]interface{}) []string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	return keys
}

func containsIgnoreCase(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || 
		    len(s) > len(substr) && 
		    (s[:len(substr)] == substr || 
		     s[len(s)-len(substr):] == substr ||
		     findSubstring(s, substr)))
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
