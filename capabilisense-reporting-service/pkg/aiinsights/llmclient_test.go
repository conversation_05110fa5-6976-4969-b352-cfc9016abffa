package aiinsights

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"capabilisense-reporting-service/pkg/config"
)

func TestLLMClientFactory(t *testing.T) {
	library := loadTestLibrary(t)
	factory := NewLLMClientFactory(library)

	assert.NotNil(t, factory)
	assert.Equal(t, library, factory.promptsLibrary)
}

func TestCreateUnifiedClient(t *testing.T) {
	library := loadTestLibrary(t)
	factory := NewLLMClientFactory(library)

	client, err := factory.CreateClient("unified")
	assert.NoError(t, err)
	assert.NotNil(t, client)
	assert.Equal(t, "unified", client.GetProviderName())
}

func TestCreateMockClient(t *testing.T) {
	library := loadTestLibrary(t)
	factory := NewLLMClientFactory(library)

	client, err := factory.CreateClient("mock")
	assert.NoError(t, err)
	assert.NotNil(t, client)
	assert.Equal(t, "mock", client.GetProviderName())
}

func TestCreateInvalidClient(t *testing.T) {
	library := loadTestLibrary(t)
	factory := NewLLMClientFactory(library)

	_, err := factory.CreateClient("invalid")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "unsupported LLM provider")
}

func TestUnifiedClientValidateConfiguration(t *testing.T) {
	library := loadTestLibrary(t)
	client := NewUnifiedLLMClient(library)

	err := client.ValidateConfiguration()
	assert.NoError(t, err)
}

func TestUnifiedClientGetSupportedModels(t *testing.T) {
	library := loadTestLibrary(t)
	client := NewUnifiedLLMClient(library)

	models := client.GetSupportedModels()
	assert.NotEmpty(t, models)
	assert.Contains(t, models, "azure")
	assert.Contains(t, models, "gcp-gemini-2.0-flash-lite")
}

func TestMockClientGenerateInsight(t *testing.T) {
	client := NewMockLLMClient()

	request := InsightRequest{
		PromptID:  "organization_name",
		Context:   "Test organization",
		RequestID: "test-123",
		Timestamp: time.Now(),
	}

	response, err := client.GenerateInsight(context.Background(), request)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, "test-123", response.RequestID)
	assert.Equal(t, "organization_name", response.PromptID)
	assert.Equal(t, "MockCorp Inc.", response.Content)
	assert.Equal(t, "mock", response.Provider)
	assert.False(t, response.UsedFallback)
}

func TestMockClientDifferentPrompts(t *testing.T) {
	client := NewMockLLMClient()

	testCases := []struct {
		promptID        string
		expectedContent string
	}{
		{"organization_name", "MockCorp Inc."},
		{"overall_summary", "MockCorp shows good progress"},
		{"domain_insights", "Strategic Vision demonstrates strong performance"},
		{"ai_spotlight", "Data silos are currently impacting"},
		{"ets_solutions", "Implement a unified data governance framework"},
		{"unknown_prompt", "Mock insight generated for prompt: unknown_prompt"},
	}

	for _, tc := range testCases {
		t.Run(tc.promptID, func(t *testing.T) {
			request := InsightRequest{
				PromptID:  tc.promptID,
				RequestID: fmt.Sprintf("test-%s", tc.promptID),
				Timestamp: time.Now(),
			}

			response, err := client.GenerateInsight(context.Background(), request)
			assert.NoError(t, err)
			assert.Contains(t, response.Content, tc.expectedContent)
			assert.NotNil(t, response.StructuredData)
		})
	}
}

func TestUnifiedClientGenerateInsightWithMockData(t *testing.T) {
	// Create a test library with fallback to ensure we get a response
	library := &config.PromptsLibrary{
		Prompts: map[string]config.PromptConfig{
			"test_prompt": {
				ModelAliases: []string{"invalid_provider"},
				SystemPrompt: "You are a test assistant",
				Temperature:  0.5,
				FallbackFile: "../../configs/fallbacks/frontend_chat_fallback.txt",
			},
		},
		Models: map[string]config.ModelConfig{
			"invalid_provider": {
				ProviderURL: "http://invalid.example.com",
				ModelName:   "invalid-model",
				APIKeyEnv:   "INVALID_API_KEY",
			},
		},
	}

	client := NewUnifiedLLMClient(library)

	request := InsightRequest{
		PromptID:  "test_prompt",
		Context:   "Test context",
		RequestID: "test-unified-123",
		Timestamp: time.Now(),
		InputData: map[string]interface{}{
			"test_key": "test_value",
		},
	}

	response, err := client.GenerateInsight(context.Background(), request)
	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, "test-unified-123", response.RequestID)
	assert.Equal(t, "test_prompt", response.PromptID)
	assert.NotEmpty(t, response.Content)
	assert.True(t, response.UsedFallback)
	assert.Equal(t, "fallback", response.Provider)
}

func TestUnifiedClientIntegration(t *testing.T) {
	// Load test environment
	config.LoadTestEnv()
	
	// Get available providers
	availableProviders := config.GetAvailableProviders()
	if len(availableProviders) == 0 {
		t.Skip("No API keys available for unified client integration testing")
	}

	library := loadTestLibrary(t)
	factory := NewLLMClientFactory(library)
	client, err := factory.CreateClient("unified")
	require.NoError(t, err)

	request := InsightRequest{
		PromptID:  "frontend_chat",
		Context:   "Hello! Can you briefly explain digital transformation?",
		RequestID: fmt.Sprintf("unified-integration-%d", time.Now().Unix()),
		Timestamp: time.Now(),
		InputData: map[string]interface{}{
			"user_type": "test_user",
		},
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	response, err := client.GenerateInsight(ctx, request)
	
	if err != nil {
		t.Logf("Unified client integration test failed (expected if no API keys): %v", err)
		return
	}

	assert.NotNil(t, response)
	assert.Equal(t, request.RequestID, response.RequestID)
	assert.Equal(t, request.PromptID, response.PromptID)
	assert.NotEmpty(t, response.Content)
	assert.NotEmpty(t, response.Provider)
	assert.Greater(t, response.ProcessingTime, time.Duration(0))

	t.Logf("✓ Unified client success:")
	t.Logf("  Provider: %s", response.Provider)
	t.Logf("  Model: %s", response.Model)
	t.Logf("  Content length: %d", len(response.Content))
	t.Logf("  Used fallback: %t", response.UsedFallback)
}

func TestClientFactoryWithDifferentProviders(t *testing.T) {
	library := loadTestLibrary(t)
	factory := NewLLMClientFactory(library)

	providers := []string{"unified", "mock", "openai", "google"}
	
	for _, provider := range providers {
		t.Run(provider, func(t *testing.T) {
			client, err := factory.CreateClient(provider)
			
			if provider == "unified" || provider == "mock" {
				assert.NoError(t, err)
				assert.NotNil(t, client)
				assert.Equal(t, provider, client.GetProviderName())
				
				// Test that client can validate configuration
				err = client.ValidateConfiguration()
				assert.NoError(t, err)
				
				// Test that client returns supported models
				models := client.GetSupportedModels()
				assert.NotNil(t, models)
			} else {
				// openai and google are placeholder implementations
				assert.NoError(t, err)
				assert.NotNil(t, client)
			}
		})
	}
}

func TestMockClientPerformance(t *testing.T) {
	client := NewMockLLMClient()

	// Test that mock client responds quickly
	start := time.Now()
	
	request := InsightRequest{
		PromptID:  "organization_name",
		RequestID: "perf-test",
		Timestamp: time.Now(),
	}

	response, err := client.GenerateInsight(context.Background(), request)
	elapsed := time.Since(start)

	assert.NoError(t, err)
	assert.NotNil(t, response)
	assert.Less(t, elapsed, 500*time.Millisecond, "Mock client should respond quickly")
	assert.Greater(t, response.ProcessingTime, time.Duration(0))
	assert.Less(t, response.ProcessingTime, 200*time.Millisecond, "Mock processing time should be realistic")
}

func TestClientErrorHandling(t *testing.T) {
	// Test with nil library
	client := &UnifiedLLMClient{
		llmInterface: nil,
		providerName: "test",
	}

	err := client.ValidateConfiguration()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "LLM interface not initialized")

	// Test with nil library in interface
	client.llmInterface = &LLMInterface{
		library: nil,
	}

	err = client.ValidateConfiguration()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "prompts library not loaded")
}
