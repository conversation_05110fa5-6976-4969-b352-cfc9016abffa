package aiinsights

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"capabilisense-reporting-service/pkg/config"
)

func TestMain(m *testing.M) {
	// Load environment variables for testing
	config.LoadTestEnv()
	
	// Run tests
	m.Run()
}

func loadTestLibrary(t *testing.T) *config.PromptsLibrary {
	library, err := config.LoadPromptsLibrary("../../configs/prompts_library.json")
	require.NoError(t, err)
	return library
}

func TestNewLLMInterface(t *testing.T) {
	library := loadTestLibrary(t)
	
	llm := NewLLMInterface(library)
	assert.NotNil(t, llm)
	assert.NotNil(t, llm.library)
	assert.NotNil(t, llm.httpClient)
}

func TestBuildMessages(t *testing.T) {
	library := loadTestLibrary(t)
	llm := NewLLMInterface(library)

	// Test with system prompt and user query
	request := LLMRequest{
		UserQuery: "Hello, how are you?",
		ChatHistory: []ChatMessage{
			{Role: "user", Content: "Previous message"},
			{Role: "assistant", Content: "Previous response"},
		},
	}

	messages := llm.buildMessages("You are a helpful assistant.", request)
	
	assert.Len(t, messages, 4) // system + 2 history + user query
	assert.Equal(t, "system", messages[0].Role)
	assert.Equal(t, "You are a helpful assistant.", messages[0].Content)
	assert.Equal(t, "user", messages[1].Role)
	assert.Equal(t, "Previous message", messages[1].Content)
	assert.Equal(t, "assistant", messages[2].Role)
	assert.Equal(t, "Previous response", messages[2].Content)
	assert.Equal(t, "user", messages[3].Role)
	assert.Equal(t, "Hello, how are you?", messages[3].Content)
}

func TestBuildOpenAIPayload(t *testing.T) {
	library := loadTestLibrary(t)
	llm := NewLLMInterface(library)

	modelConfig := &config.ModelConfig{
		ModelName: "gpt-4",
	}

	messages := []ChatMessage{
		{Role: "user", Content: "Hello"},
	}

	payload, err := llm.buildOpenAIPayload(modelConfig, messages, 0.7, nil, nil)
	assert.NoError(t, err)
	assert.Equal(t, "gpt-4", payload["model"])
	assert.Equal(t, 0.7, payload["temperature"])
	assert.Equal(t, messages, payload["messages"])
}

func TestBuildGeminiPayload(t *testing.T) {
	library := loadTestLibrary(t)
	llm := NewLLMInterface(library)

	modelConfig := &config.ModelConfig{
		ModelName:          "gemini-pro",
		SafetyTagsProvider: "google",
	}

	messages := []ChatMessage{
		{Role: "user", Content: "Hello"},
		{Role: "assistant", Content: "Hi there"},
	}

	payload, err := llm.buildGeminiPayload(modelConfig, messages, 0.5, nil, nil)
	assert.NoError(t, err)
	
	contents, ok := payload["contents"].([]map[string]interface{})
	assert.True(t, ok)
	assert.Len(t, contents, 2)
	
	genConfig, ok := payload["generationConfig"].(map[string]interface{})
	assert.True(t, ok)
	assert.Equal(t, 0.5, genConfig["temperature"])
}

func TestBuildAnthropicPayload(t *testing.T) {
	library := loadTestLibrary(t)
	llm := NewLLMInterface(library)

	modelConfig := &config.ModelConfig{
		ModelName: "claude-3-opus",
	}

	messages := []ChatMessage{
		{Role: "system", Content: "You are helpful"},
		{Role: "user", Content: "Hello"},
	}

	payload, err := llm.buildAnthropicPayload(modelConfig, messages, 0.3, nil, nil)
	assert.NoError(t, err)
	
	assert.Equal(t, "claude-3-opus", payload["model"])
	assert.Equal(t, 0.3, payload["temperature"])
	assert.Equal(t, "You are helpful", payload["system"])
	assert.Equal(t, 4096, payload["max_tokens"])
	
	anthropicMessages, ok := payload["messages"].([]map[string]interface{})
	assert.True(t, ok)
	assert.Len(t, anthropicMessages, 1) // System message should be separated
}

func TestExtractOpenAIResponse(t *testing.T) {
	library := loadTestLibrary(t)
	llm := NewLLMInterface(library)

	respBody := map[string]interface{}{
		"choices": []interface{}{
			map[string]interface{}{
				"message": map[string]interface{}{
					"content": "Hello! How can I help you?",
				},
			},
		},
		"usage": map[string]interface{}{
			"total_tokens": float64(25),
		},
	}

	content, toolCalls, tokensUsed := llm.extractOpenAIResponse(respBody)
	assert.Equal(t, "Hello! How can I help you?", content)
	assert.Empty(t, toolCalls)
	assert.Equal(t, 25, tokensUsed)
}

func TestExtractGeminiResponse(t *testing.T) {
	library := loadTestLibrary(t)
	llm := NewLLMInterface(library)

	respBody := map[string]interface{}{
		"candidates": []interface{}{
			map[string]interface{}{
				"content": map[string]interface{}{
					"parts": []interface{}{
						map[string]interface{}{
							"text": "Hello from Gemini!",
						},
					},
				},
			},
		},
		"usageMetadata": map[string]interface{}{
			"totalTokenCount": float64(30),
		},
	}

	content, toolCalls, tokensUsed := llm.extractGeminiResponse(respBody)
	assert.Equal(t, "Hello from Gemini!", content)
	assert.Empty(t, toolCalls)
	assert.Equal(t, 30, tokensUsed)
}

func TestExtractAnthropicResponse(t *testing.T) {
	library := loadTestLibrary(t)
	llm := NewLLMInterface(library)

	respBody := map[string]interface{}{
		"content": []interface{}{
			map[string]interface{}{
				"type": "text",
				"text": "Hello from Claude!",
			},
		},
		"usage": map[string]interface{}{
			"input_tokens":  float64(10),
			"output_tokens": float64(15),
		},
	}

	content, toolCalls, tokensUsed := llm.extractAnthropicResponse(respBody)
	assert.Equal(t, "Hello from Claude!", content)
	assert.Empty(t, toolCalls)
	assert.Equal(t, 25, tokensUsed) // 10 + 15
}

// Integration tests that require API keys
func TestCallLLMWithMockProvider(t *testing.T) {
	// Create a test library with mock provider
	library := &config.PromptsLibrary{
		Prompts: map[string]config.PromptConfig{
			"test_prompt": {
				ModelAliases: []string{"mock_provider"},
				SystemPrompt: "You are a test assistant",
				Temperature:  0.5,
			},
		},
		Models: map[string]config.ModelConfig{
			"mock_provider": {
				ProviderURL: "http://mock.example.com",
				ModelName:   "mock-model",
				APIKeyEnv:   "MOCK_API_KEY",
			},
		},
	}

	// Set mock API key
	t.Setenv("MOCK_API_KEY", "mock-key-123")

	llm := NewLLMInterface(library)

	request := LLMRequest{
		PromptID:  "test_prompt",
		UserQuery: "Hello",
		RequestID: "test-123",
	}

	// This will fail because it's a mock URL, but we can test the error handling
	_, err := llm.CallLLM(context.Background(), request)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "all providers failed")
}

func TestCallLLMWithFallback(t *testing.T) {
	// Create a test library with fallback
	library := &config.PromptsLibrary{
		Prompts: map[string]config.PromptConfig{
			"test_prompt": {
				ModelAliases: []string{"invalid_provider"},
				SystemPrompt: "You are a test assistant",
				Temperature:  0.5,
				FallbackFile: "../../configs/fallbacks/frontend_chat_fallback.txt",
			},
		},
		Models: map[string]config.ModelConfig{
			"invalid_provider": {
				ProviderURL: "http://invalid.example.com",
				ModelName:   "invalid-model",
				APIKeyEnv:   "INVALID_API_KEY",
			},
		},
	}

	llm := NewLLMInterface(library)

	request := LLMRequest{
		PromptID:  "test_prompt",
		UserQuery: "Hello",
		RequestID: "test-123",
	}

	response, err := llm.CallLLM(context.Background(), request)
	assert.NoError(t, err)
	assert.True(t, response.UsedFallback)
	assert.Equal(t, "fallback", response.Provider)
	assert.Contains(t, response.Content, "unable to process")
}
