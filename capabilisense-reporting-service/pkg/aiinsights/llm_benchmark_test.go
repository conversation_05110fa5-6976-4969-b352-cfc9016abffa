package aiinsights

import (
	"context"
	"fmt"
	"testing"
	"time"

	"capabilisense-reporting-service/pkg/config"
)

// Benchmark tests for LLM library performance

func BenchmarkMockLLMClient(b *testing.B) {
	client := NewMockLLMClient()
	
	request := InsightRequest{
		PromptID:  "organization_name",
		Context:   "Test organization",
		RequestID: "bench-test",
		Timestamp: time.Now(),
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := client.GenerateInsight(context.Background(), request)
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkConfigurationLoading(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := config.LoadPromptsLibrary("../../configs/prompts_library.json")
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkLLMInterfaceCreation(b *testing.B) {
	library, err := config.LoadPromptsLibrary("../../configs/prompts_library.json")
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = NewLLMInterface(library)
	}
}

func BenchmarkMessageBuilding(b *testing.B) {
	library, err := config.LoadPromptsLibrary("../../configs/prompts_library.json")
	if err != nil {
		b.Fatal(err)
	}

	llm := NewLLMInterface(library)
	
	request := LLMRequest{
		UserQuery: "Hello, how are you?",
		ChatHistory: []ChatMessage{
			{Role: "user", Content: "Previous message"},
			{Role: "assistant", Content: "Previous response"},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = llm.buildMessages("You are a helpful assistant.", request)
	}
}

func BenchmarkPayloadBuilding(b *testing.B) {
	library, err := config.LoadPromptsLibrary("../../configs/prompts_library.json")
	if err != nil {
		b.Fatal(err)
	}

	llm := NewLLMInterface(library)
	
	modelConfig := &config.ModelConfig{
		ModelName: "gpt-4",
	}

	messages := []ChatMessage{
		{Role: "user", Content: "Hello"},
	}

	b.Run("OpenAI", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := llm.buildOpenAIPayload(modelConfig, messages, 0.7, nil, nil)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("Gemini", func(b *testing.B) {
		modelConfig.SafetyTagsProvider = "google"
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := llm.buildGeminiPayload(modelConfig, messages, 0.7, nil, nil)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("Anthropic", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := llm.buildAnthropicPayload(modelConfig, messages, 0.7, nil, nil)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

func BenchmarkResponseExtraction(b *testing.B) {
	library, err := config.LoadPromptsLibrary("../../configs/prompts_library.json")
	if err != nil {
		b.Fatal(err)
	}

	llm := NewLLMInterface(library)

	openAIResponse := map[string]interface{}{
		"choices": []interface{}{
			map[string]interface{}{
				"message": map[string]interface{}{
					"content": "Hello! How can I help you?",
				},
			},
		},
		"usage": map[string]interface{}{
			"total_tokens": float64(25),
		},
	}

	geminiResponse := map[string]interface{}{
		"candidates": []interface{}{
			map[string]interface{}{
				"content": map[string]interface{}{
					"parts": []interface{}{
						map[string]interface{}{
							"text": "Hello from Gemini!",
						},
					},
				},
			},
		},
		"usageMetadata": map[string]interface{}{
			"totalTokenCount": float64(30),
		},
	}

	anthropicResponse := map[string]interface{}{
		"content": []interface{}{
			map[string]interface{}{
				"type": "text",
				"text": "Hello from Claude!",
			},
		},
		"usage": map[string]interface{}{
			"input_tokens":  float64(10),
			"output_tokens": float64(15),
		},
	}

	b.Run("OpenAI", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, _ = llm.extractOpenAIResponse(openAIResponse)
		}
	})

	b.Run("Gemini", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, _ = llm.extractGeminiResponse(geminiResponse)
		}
	})

	b.Run("Anthropic", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, _, _ = llm.extractAnthropicResponse(anthropicResponse)
		}
	})
}

func BenchmarkFallbackLoading(b *testing.B) {
	library, err := config.LoadPromptsLibrary("../../configs/prompts_library.json")
	if err != nil {
		b.Fatal(err)
	}

	prompt, err := library.GetPrompt("frontend_chat")
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := prompt.GetFallbackContent("frontend_chat")
		if err != nil {
			b.Fatal(err)
		}
	}
}

func BenchmarkSchemaLoading(b *testing.B) {
	library, err := config.LoadPromptsLibrary("../../configs/prompts_library.json")
	if err != nil {
		b.Fatal(err)
	}

	prompt, err := library.GetPrompt("maturity_assessor")
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := prompt.LoadJSONSchema()
		if err != nil {
			b.Fatal(err)
		}
	}
}

// Benchmark concurrent access
func BenchmarkConcurrentMockCalls(b *testing.B) {
	client := NewMockLLMClient()
	
	b.RunParallel(func(pb *testing.PB) {
		i := 0
		for pb.Next() {
			request := InsightRequest{
				PromptID:  "organization_name",
				Context:   fmt.Sprintf("Test organization %d", i),
				RequestID: fmt.Sprintf("bench-test-%d", i),
				Timestamp: time.Now(),
			}
			
			_, err := client.GenerateInsight(context.Background(), request)
			if err != nil {
				b.Fatal(err)
			}
			i++
		}
	})
}

func BenchmarkConcurrentConfigAccess(b *testing.B) {
	library, err := config.LoadPromptsLibrary("../../configs/prompts_library.json")
	if err != nil {
		b.Fatal(err)
	}

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			_, err := library.GetPrompt("frontend_chat")
			if err != nil {
				b.Fatal(err)
			}
			
			_, err = library.GetModel("azure")
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// Memory allocation benchmarks
func BenchmarkMemoryAllocation(b *testing.B) {
	library, err := config.LoadPromptsLibrary("../../configs/prompts_library.json")
	if err != nil {
		b.Fatal(err)
	}

	b.Run("LLMInterface", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = NewLLMInterface(library)
		}
	})

	b.Run("MockClient", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = NewMockLLMClient()
		}
	})

	b.Run("UnifiedClient", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = NewUnifiedLLMClient(library)
		}
	})
}
