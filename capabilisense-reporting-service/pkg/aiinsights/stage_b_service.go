package aiinsights

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"capabilisense-reporting-service/pkg/config"
	"capabilisense-reporting-service/pkg/dataextraction"
)

// StageBService handles AI insight generation for Stage B
type StageBService struct {
	llmInterface *LLMInterface
	library      *config.PromptsLibrary
}

// NewStageBService creates a new Stage B service
func NewStageBService(library *config.PromptsLibrary) *StageBService {
	llmInterface := NewLLMInterface(library)
	return &StageBService{
		llmInterface: llmInterface,
		library:      library,
	}
}

// StageBOutput represents the complete AI-generated insights for one-pager report
type StageBOutput struct {
	ReportMetadata     dataextraction.ReportMetadata `json:"report_metadata"`
	OrganizationName   OrganizationNameInsight       `json:"organization_name"`
	BusinessSummary    BusinessSummaryInsight        `json:"business_summary"`
	StrengthDomains    []DomainInsight               `json:"strength_domains"` // Top 3 strength domains
	WeaknessDomains    []DomainInsight               `json:"weakness_domains"` // Top 3 weakness domains
	AISpotlight        AISpotlightInsight            `json:"ai_spotlight"`
	FocusArea          FocusAreaInsight              `json:"focus_area"` // AI-generated focus area
	GenerationMetadata GenerationMetadata            `json:"generation_metadata"`
}

// OrganizationNameInsight contains the AI-generated organization name
type OrganizationNameInsight struct {
	GeneratedName string `json:"generated_name"`
	Reasoning     string `json:"reasoning"`
}

// BusinessSummaryInsight contains the business summary for the one-pager
type BusinessSummaryInsight struct {
	ExecutiveSummary    string   `json:"executive_summary"`
	KeyFindings         []string `json:"key_findings"`
	StrategicPriorities []string `json:"strategic_priorities"`
}

// DomainInsight contains insights for a specific domain
type DomainInsight struct {
	DomainName       string   `json:"domain_name"`
	CurrentState     string   `json:"current_state"`
	KeyStrengths     []string `json:"key_strengths"`
	ImprovementAreas []string `json:"improvement_areas"`
	Recommendations  []string `json:"recommendations"`
	NextSteps        []string `json:"next_steps"`
}

// AISpotlightInsight contains the AI spotlight analysis
type AISpotlightInsight struct {
	SpotlightTitle  string   `json:"spotlight_title"`
	Analysis        string   `json:"analysis"`
	KeyInsights     []string `json:"key_insights"`
	BusinessImpact  string   `json:"business_impact"`
	ActionableSteps []string `json:"actionable_steps"`
}

// FocusAreaInsight contains AI-generated focus area recommendations
type FocusAreaInsight struct {
	FocusTitle         string   `json:"focus_title"`         // AI-generated title for the focus area
	FocusDescription   string   `json:"focus_description"`   // AI-generated description
	KeyRecommendations []string `json:"key_recommendations"` // AI-generated recommendations
	ImplementationPlan string   `json:"implementation_plan"` // AI-generated implementation approach
	ExpectedOutcomes   []string `json:"expected_outcomes"`   // AI-generated expected outcomes
}

// Note: ETSSolution and GenerationMetadata are already defined in models.go

// GenerateInsights generates all AI insights for Stage B
func (s *StageBService) GenerateInsights(ctx context.Context, stageAData *dataextraction.StageAOutput) (*StageBOutput, error) {
	startTime := time.Now()
	log.Printf("Starting Stage B AI insight generation for project %s", stageAData.ReportMetadata.ProjectID)

	var providersUsed []string
	var fallbacksUsed []string
	totalTokens := 0

	// Generate organization name
	orgName, tokens, provider, fallback, err := s.generateOrganizationName(ctx, stageAData)
	if err != nil {
		log.Printf("Warning: Failed to generate organization name: %v", err)
		orgName = OrganizationNameInsight{
			GeneratedName: "Organization",
			Reasoning:     "Unable to generate custom name due to AI service unavailability",
		}
	} else {
		totalTokens += tokens
		if provider != "" {
			providersUsed = append(providersUsed, provider)
		}
		if fallback {
			fallbacksUsed = append(fallbacksUsed, "organization_name")
		}
	}

	// Generate business summary
	businessSummary, tokens, provider, fallback, err := s.generateBusinessSummary(ctx, stageAData)
	if err != nil {
		log.Printf("Warning: Failed to generate business summary: %v", err)
		businessSummary = BusinessSummaryInsight{
			ExecutiveSummary:    "Assessment completed with mixed results across domains. Detailed analysis available in domain-specific sections.",
			KeyFindings:         []string{"Assessment data processed", "Multiple domains evaluated", "Recommendations generated"},
			StrategicPriorities: []string{"Focus on improvement areas", "Leverage existing strengths", "Implement targeted solutions"},
		}
	} else {
		totalTokens += tokens
		if provider != "" {
			providersUsed = append(providersUsed, provider)
		}
		if fallback {
			fallbacksUsed = append(fallbacksUsed, "business_summary")
		}
	}

	// Generate domain insights and separate into strengths/weaknesses
	allDomainInsights, tokens, providers, fallbacks := s.generateDomainInsights(ctx, stageAData)
	totalTokens += tokens
	providersUsed = append(providersUsed, providers...)
	fallbacksUsed = append(fallbacksUsed, fallbacks...)

	// Separate domains into strengths (top 3) and weaknesses (bottom 3)
	strengthDomains, weaknessDomains := s.separateStrengthsAndWeaknesses(allDomainInsights, stageAData.DomainScoresForSpiderChart)

	// Generate AI spotlight
	spotlight, tokens, provider, fallback, err := s.generateAISpotlight(ctx, stageAData)
	if err != nil {
		log.Printf("Warning: Failed to generate AI spotlight: %v", err)
		spotlight = AISpotlightInsight{
			SpotlightTitle:  "Assessment Insights",
			Analysis:        "Comprehensive assessment reveals opportunities for strategic improvement across multiple domains.",
			KeyInsights:     []string{"Data-driven assessment completed", "Multiple improvement opportunities identified", "Strategic recommendations available"},
			BusinessImpact:  "Implementation of recommendations expected to drive measurable improvements in organizational performance.",
			ActionableSteps: []string{"Review detailed domain insights", "Prioritize improvement initiatives", "Develop implementation timeline"},
		}
	} else {
		totalTokens += tokens
		if provider != "" {
			providersUsed = append(providersUsed, provider)
		}
		if fallback {
			fallbacksUsed = append(fallbacksUsed, "ai_spotlight")
		}
	}

	// Generate focus area (replaces ETS solutions)
	focusArea, tokens, provider, fallback, err := s.generateFocusArea(ctx, stageAData)
	if err != nil {
		log.Printf("Warning: Failed to generate focus area: %v", err)
		focusArea = FocusAreaInsight{
			FocusTitle:         "Strategic Improvement Focus",
			FocusDescription:   "Based on the assessment results, focus on strengthening key capabilities and addressing critical gaps to drive organizational performance.",
			KeyRecommendations: []string{"Review assessment findings", "Develop targeted improvement plans", "Implement strategic initiatives"},
			ImplementationPlan: "Phase 1: Review assessment results. Phase 2: Develop improvement plan. Phase 3: Execute strategic initiatives.",
			ExpectedOutcomes:   []string{"Improved organizational performance", "Enhanced strategic alignment", "Measurable progress tracking"},
		}
	} else {
		totalTokens += tokens
		if provider != "" {
			providersUsed = append(providersUsed, provider)
		}
		if fallback {
			fallbacksUsed = append(fallbacksUsed, "focus_area")
		}
	}

	processingTime := time.Since(startTime)
	log.Printf("Stage B AI insight generation completed in %v, tokens used: %d", processingTime, totalTokens)

	// Remove duplicates from providers used
	providersUsed = removeDuplicates(providersUsed)

	return &StageBOutput{
		ReportMetadata:   stageAData.ReportMetadata,
		OrganizationName: orgName,
		BusinessSummary:  businessSummary,
		StrengthDomains:  strengthDomains,
		WeaknessDomains:  weaknessDomains,
		AISpotlight:      spotlight,
		FocusArea:        focusArea,
		GenerationMetadata: GenerationMetadata{
			GeneratedAt:         time.Now(),
			TotalProcessingTime: processingTime,
			ProvidersUsed:       providersUsed,
			TotalTokensUsed:     totalTokens,
			FallbacksUsed:       convertFallbacksToMap(fallbacksUsed),
		},
	}, nil
}

// generateOrganizationName generates an organization name using AI
func (s *StageBService) generateOrganizationName(ctx context.Context, data *dataextraction.StageAOutput) (OrganizationNameInsight, int, string, bool, error) {
	// Prepare context data
	contextData := map[string]interface{}{
		"framework_name":     data.ReportMetadata.FrameworkName,
		"framework_overview": data.ContextForSpotlightAndAI.FrameworkOverview,
		"document_names":     data.ContextForSpotlightAndAI.DocumentNamesProcessed,
		"overall_maturity":   data.OverallMaturity,
		"domain_scores":      data.DomainScoresForSpiderChart,
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "organization_name",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("org-name-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return OrganizationNameInsight{}, 0, "", false, err
	}

	// Parse the response (assuming it's JSON)
	var insight OrganizationNameInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		// If JSON parsing fails, treat as plain text
		insight = OrganizationNameInsight{
			GeneratedName: "Organization", // Default fallback
			Reasoning:     response.Content,
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// generateBusinessSummary generates the business summary using AI
func (s *StageBService) generateBusinessSummary(ctx context.Context, data *dataextraction.StageAOutput) (BusinessSummaryInsight, int, string, bool, error) {
	contextData := map[string]interface{}{
		"overall_maturity":         data.OverallMaturity,
		"domain_scores":            data.DomainScoresForSpiderChart,
		"key_strengths":            data.KeyStrengths,
		"critical_areas":           data.CriticalAreasFocus,
		"framework_overview":       data.ContextForSpotlightAndAI.FrameworkOverview,
		"low_scoring_capabilities": data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning,
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "business_summary",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("business-summary-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return BusinessSummaryInsight{}, 0, "", false, err
	}

	var insight BusinessSummaryInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		insight = BusinessSummaryInsight{
			ExecutiveSummary:    response.Content,
			KeyFindings:         []string{"Analysis completed"},
			StrategicPriorities: []string{"Review detailed insights"},
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// generateDomainInsights generates insights for each domain
func (s *StageBService) generateDomainInsights(ctx context.Context, data *dataextraction.StageAOutput) ([]DomainInsight, int, []string, []string) {
	var insights []DomainInsight
	var totalTokens int
	var providersUsed []string
	var fallbacksUsed []string

	// Generate insights for each domain in domain scores
	for _, domain := range data.DomainScoresForSpiderChart {
		contextData := map[string]interface{}{
			"domain_name":              domain.DomainName,
			"domain_score":             domain.AverageScore,
			"capability_count":         domain.CapabilityCount,
			"all_domain_scores":        data.ContextForSpotlightAndAI.AllDomainScoresSummary,
			"leaf_capabilities":        data.ContextForSpotlightAndAI.AllLeafCapabilityScores,
			"low_scoring_capabilities": data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning,
		}

		contextJSON, _ := json.Marshal(contextData)

		request := LLMRequest{
			PromptID:  "domain_insights",
			UserQuery: string(contextJSON),
			RequestID: fmt.Sprintf("domain-%s-%d", domain.DomainName, time.Now().Unix()),
		}

		response, err := s.llmInterface.CallLLM(ctx, request)
		if err != nil {
			log.Printf("Warning: Failed to generate insights for domain %s: %v", domain.DomainName, err)
			// Create fallback insight
			insights = append(insights, DomainInsight{
				DomainName:       domain.DomainName,
				CurrentState:     fmt.Sprintf("Domain scored %.1f out of 5.0", domain.AverageScore),
				KeyStrengths:     []string{"Assessment completed"},
				ImprovementAreas: []string{"Detailed analysis pending"},
				Recommendations:  []string{"Review domain-specific data"},
				NextSteps:        []string{"Engage with domain experts"},
			})
			fallbacksUsed = append(fallbacksUsed, fmt.Sprintf("domain_%s", domain.DomainName))
			continue
		}

		var insight DomainInsight
		if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
			insight = DomainInsight{
				DomainName:       domain.DomainName,
				CurrentState:     response.Content,
				KeyStrengths:     []string{"Analysis provided"},
				ImprovementAreas: []string{"See detailed analysis"},
				Recommendations:  []string{"Review AI analysis"},
				NextSteps:        []string{"Implement recommendations"},
			}
		}

		insights = append(insights, insight)
		totalTokens += response.TokensUsed
		if response.Provider != "" {
			providersUsed = append(providersUsed, response.Provider)
		}
		if response.UsedFallback {
			fallbacksUsed = append(fallbacksUsed, fmt.Sprintf("domain_%s", domain.DomainName))
		}
	}

	return insights, totalTokens, providersUsed, fallbacksUsed
}

// generateAISpotlight generates the AI spotlight analysis
func (s *StageBService) generateAISpotlight(ctx context.Context, data *dataextraction.StageAOutput) (AISpotlightInsight, int, string, bool, error) {
	contextData := map[string]interface{}{
		"overall_maturity":         data.OverallMaturity,
		"domain_scores":            data.DomainScoresForSpiderChart,
		"key_strengths":            data.KeyStrengths,
		"critical_areas":           data.CriticalAreasFocus,
		"framework_overview":       data.ContextForSpotlightAndAI.FrameworkOverview,
		"all_leaf_capabilities":    data.ContextForSpotlightAndAI.AllLeafCapabilityScores,
		"low_scoring_capabilities": data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning,
		"documents_processed":      data.ContextForSpotlightAndAI.DocumentNamesProcessed,
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "ai_spotlight",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("spotlight-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return AISpotlightInsight{}, 0, "", false, err
	}

	var insight AISpotlightInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		insight = AISpotlightInsight{
			SpotlightTitle:  "AI Analysis Spotlight",
			Analysis:        response.Content,
			KeyInsights:     []string{"Comprehensive analysis completed"},
			BusinessImpact:  "Strategic insights generated for organizational improvement",
			ActionableSteps: []string{"Review analysis", "Develop action plan", "Monitor progress"},
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// generateFocusArea generates AI-driven focus area recommendations
func (s *StageBService) generateFocusArea(ctx context.Context, data *dataextraction.StageAOutput) (FocusAreaInsight, int, string, bool, error) {
	contextData := map[string]interface{}{
		"overall_maturity":         data.OverallMaturity,
		"critical_areas":           data.CriticalAreasFocus,
		"framework_overview":       data.ContextForSpotlightAndAI.FrameworkOverview,
		"low_scoring_capabilities": data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning,
		"documents_processed":      data.ContextForSpotlightAndAI.DocumentNamesProcessed,
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "focus_area",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("focus-area-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return FocusAreaInsight{}, 0, "", false, err
	}

	var insight FocusAreaInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		insight = FocusAreaInsight{
			FocusTitle:         "Strategic Focus Area",
			FocusDescription:   response.Content,
			KeyRecommendations: []string{"Review AI analysis", "Implement strategic initiatives", "Monitor progress"},
			ImplementationPlan: "Structured approach to implementing recommended solutions",
			ExpectedOutcomes:   []string{"Improved performance", "Strategic alignment", "Sustainable growth"},
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// separateStrengthsAndWeaknesses separates domain insights into strengths and weaknesses based on scores
func (s *StageBService) separateStrengthsAndWeaknesses(domainInsights []DomainInsight, domainScores []dataextraction.DomainScore) ([]DomainInsight, []DomainInsight) {
	// Create a map of domain names to scores for quick lookup
	scoreMap := make(map[string]float64)
	for _, score := range domainScores {
		scoreMap[score.DomainName] = score.AverageScore
	}

	// Sort domain insights by score (highest to lowest)
	sortedInsights := make([]DomainInsight, len(domainInsights))
	copy(sortedInsights, domainInsights)

	// Sort by score descending
	for i := 0; i < len(sortedInsights)-1; i++ {
		for j := i + 1; j < len(sortedInsights); j++ {
			scoreI := scoreMap[sortedInsights[i].DomainName]
			scoreJ := scoreMap[sortedInsights[j].DomainName]
			if scoreI < scoreJ {
				sortedInsights[i], sortedInsights[j] = sortedInsights[j], sortedInsights[i]
			}
		}
	}

	// Take top 3 as strengths, bottom 3 as weaknesses
	var strengthDomains, weaknessDomains []DomainInsight

	// Strengths: top 3 (or all if less than 3)
	strengthCount := 3
	if len(sortedInsights) < strengthCount {
		strengthCount = len(sortedInsights)
	}
	strengthDomains = sortedInsights[:strengthCount]

	// Weaknesses: bottom 3 (or all if less than 3)
	weaknessCount := 3
	if len(sortedInsights) < weaknessCount {
		weaknessCount = len(sortedInsights)
	}
	startIndex := len(sortedInsights) - weaknessCount
	if startIndex < 0 {
		startIndex = 0
	}
	weaknessDomains = sortedInsights[startIndex:]

	return strengthDomains, weaknessDomains
}

// removeDuplicates removes duplicate strings from a slice
func removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}

// convertFallbacksToMap converts a slice of fallback names to a map
func convertFallbacksToMap(fallbacks []string) map[string]bool {
	result := make(map[string]bool)
	for _, fallback := range fallbacks {
		result[fallback] = true
	}
	return result
}
