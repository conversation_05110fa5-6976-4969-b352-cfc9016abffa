package aiinsights

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"capabilisense-reporting-service/pkg/config"
	"capabilisense-reporting-service/pkg/dataextraction"
)

// StageBService handles AI insight generation for Stage B
type StageBService struct {
	llmInterface *LLMInterface
	library      *config.PromptsLibrary
	repo         dataextraction.Repository
}

// NewStageBService creates a new Stage B service
func NewStageBService(library *config.PromptsLibrary, repo dataextraction.Repository) *StageBService {
	llmInterface := NewLLMInterface(library)
	return &StageBService{
		llmInterface: llmInterface,
		library:      library,
		repo:         repo,
	}
}

// StageBOutput represents the complete AI-generated insights for one-pager report
type StageBOutput struct {
	ReportMetadata     dataextraction.ReportMetadata `json:"report_metadata"`
	OrganizationName   OrganizationNameInsight       `json:"organization_name"`
	BusinessSummary    BusinessSummaryInsight        `json:"business_summary"`
	StrengthDomains    []DomainInsight               `json:"strength_domains"` // Top 3 strength domains
	WeaknessDomains    []DomainInsight               `json:"weakness_domains"` // Top 3 weakness domains
	AISpotlight        AISpotlightInsight            `json:"ai_spotlight"`
	FocusArea          FocusAreaInsight              `json:"focus_area"` // AI-generated focus area
	GenerationMetadata GenerationMetadata            `json:"generation_metadata"`
}

// OrganizationNameInsight contains the AI-generated organization name
type OrganizationNameInsight struct {
	GeneratedName string `json:"generated_name"`
	Reasoning     string `json:"reasoning"`
}

// BusinessSummaryInsight contains the business summary for the one-pager
type BusinessSummaryInsight struct {
	ExecutiveSummary    string   `json:"executive_summary"`
	KeyFindings         []string `json:"key_findings"`
	StrategicPriorities []string `json:"strategic_priorities"`
}

// DomainInsight contains insights for a specific domain
type DomainInsight struct {
	DomainName       string   `json:"domain_name"`
	CurrentState     string   `json:"current_state"`
	KeyStrengths     []string `json:"key_strengths"`
	ImprovementAreas []string `json:"improvement_areas"`
	Recommendations  []string `json:"recommendations"`
	NextSteps        []string `json:"next_steps"`
}

// AISpotlightInsight contains the AI spotlight analysis
type AISpotlightInsight struct {
	SpotlightTitle  string   `json:"spotlight_title"`
	Analysis        string   `json:"analysis"`
	KeyInsights     []string `json:"key_insights"`
	BusinessImpact  string   `json:"business_impact"`
	ActionableSteps []string `json:"actionable_steps"`
}

// FocusAreaInsight contains AI-generated focus area recommendations
type FocusAreaInsight struct {
	FocusTitle         string   `json:"focus_title"`         // AI-generated title for the focus area
	FocusDescription   string   `json:"focus_description"`   // AI-generated description
	KeyRecommendations []string `json:"key_recommendations"` // AI-generated recommendations
	ImplementationPlan string   `json:"implementation_plan"` // AI-generated implementation approach
	ExpectedOutcomes   []string `json:"expected_outcomes"`   // AI-generated expected outcomes
}

// Note: ETSSolution and GenerationMetadata are already defined in models.go

// Result types for parallel processing
type businessOverviewResult struct {
	orgName         OrganizationNameInsight
	businessSummary BusinessSummaryInsight
	tokens          int
	provider        string
	fallback        bool
	err             error
}

type domainInsightsResult struct {
	insights  []DomainInsight
	tokens    int
	providers []string
	fallbacks []string
}

type spotlightResult struct {
	spotlight AISpotlightInsight
	tokens    int
	provider  string
	fallback  bool
	err       error
}

type focusAreaResult struct {
	focusArea FocusAreaInsight
	tokens    int
	provider  string
	fallback  bool
	err       error
}

// GenerateInsights generates all AI insights for Stage B with parallelization
func (s *StageBService) GenerateInsights(ctx context.Context, stageAData *dataextraction.StageAOutput) (*StageBOutput, error) {
	startTime := time.Now()
	projectID := stageAData.ReportMetadata.ProjectID
	log.Printf("Starting Stage B AI insight generation for project %s", projectID)

	// Debug logging and artifact saving
	debugMode := os.Getenv("DEBUG") == "true" || os.Getenv("DEBUG") == "1"
	if debugMode {
		fmt.Printf("[DEBUG] 🚀 Stage B: Starting AI insight generation for project: %s\n", projectID)
		fmt.Printf("[DEBUG] 📊 Input data: %d domains, overall maturity: %.2f\n",
			len(stageAData.DomainScoresForSpiderChart), stageAData.OverallMaturity.Score)

		// Save Stage A input data for debugging
		if err := s.saveDebugArtifact("stage_a_input.json", stageAData, projectID); err != nil {
			fmt.Printf("[DEBUG] ⚠️  Failed to save Stage A input: %v\n", err)
		}
	}

	// Create channels for parallel operations
	businessOverviewChan := make(chan businessOverviewResult, 1)
	domainInsightsChan := make(chan domainInsightsResult, 1)
	spotlightChan := make(chan spotlightResult, 1)
	focusAreaChan := make(chan focusAreaResult, 1)

	// Start parallel operations
	if debugMode {
		fmt.Printf("[DEBUG] 🔄 Starting parallel LLM calls...\n")
	}

	// 1. Business Overview (combined organization name + business summary)
	go func() {
		if debugMode {
			fmt.Printf("[DEBUG] 🏢 Starting business overview generation...\n")
		}
		result := s.generateBusinessOverview(ctx, stageAData, debugMode)
		businessOverviewChan <- result
	}()

	// 2. Domain Insights (can be parallelized internally)
	go func() {
		if debugMode {
			fmt.Printf("[DEBUG] 🎯 Starting domain insights generation for %d domains...\n", len(stageAData.DomainScoresForSpiderChart))
		}
		insights, tokens, providers, fallbacks := s.generateDomainInsightsParallel(ctx, stageAData, debugMode)
		domainInsightsChan <- domainInsightsResult{insights, tokens, providers, fallbacks}
	}()

	// 3. AI Spotlight
	go func() {
		if debugMode {
			fmt.Printf("[DEBUG] 💡 Starting AI spotlight generation...\n")
		}
		spotlight, tokens, provider, fallback, err := s.generateAISpotlight(ctx, stageAData)
		if debugMode {
			if err != nil {
				fmt.Printf("[DEBUG] ❌ AI spotlight generation failed: %v\n", err)
			} else {
				fmt.Printf("[DEBUG] ✅ AI spotlight generation completed (%d tokens)\n", tokens)
			}
		}
		spotlightChan <- spotlightResult{spotlight, tokens, provider, fallback, err}
	}()

	// 4. Focus Area
	go func() {
		if debugMode {
			fmt.Printf("[DEBUG] 🎯 Starting focus area generation...\n")
		}
		focusArea, tokens, provider, fallback, err := s.generateFocusArea(ctx, stageAData)
		if debugMode {
			if err != nil {
				fmt.Printf("[DEBUG] ❌ Focus area generation failed: %v\n", err)
			} else {
				fmt.Printf("[DEBUG] ✅ Focus area generation completed (%d tokens)\n", tokens)
			}
		}
		focusAreaChan <- focusAreaResult{focusArea, tokens, provider, fallback, err}
	}()

	// Collect results from parallel operations
	var providersUsed []string
	var fallbacksUsed []string
	totalTokens := 0

	// Wait for business overview
	businessOverview := <-businessOverviewChan
	if businessOverview.err != nil {
		log.Printf("Warning: Failed to generate business overview: %v", businessOverview.err)
		// Use fallbacks
		businessOverview.orgName = OrganizationNameInsight{
			GeneratedName: "Organization",
			Reasoning:     "Unable to generate custom name due to AI service unavailability",
		}
		businessOverview.businessSummary = BusinessSummaryInsight{
			ExecutiveSummary:    "Assessment completed with mixed results across domains. Detailed analysis available in domain-specific sections.",
			KeyFindings:         []string{"Assessment data processed", "Multiple domains evaluated", "Recommendations generated"},
			StrategicPriorities: []string{"Focus on improvement areas", "Leverage existing strengths", "Implement targeted solutions"},
		}
		fallbacksUsed = append(fallbacksUsed, "business_overview")
	} else {
		totalTokens += businessOverview.tokens
		if businessOverview.provider != "" {
			providersUsed = append(providersUsed, businessOverview.provider)
		}
		if businessOverview.fallback {
			fallbacksUsed = append(fallbacksUsed, "business_overview")
		}
		if debugMode {
			fmt.Printf("[DEBUG] ✅ Business overview completed (%d tokens)\n", businessOverview.tokens)
		}
	}

	// Wait for domain insights
	domainInsightsRes := <-domainInsightsChan
	totalTokens += domainInsightsRes.tokens
	providersUsed = append(providersUsed, domainInsightsRes.providers...)
	fallbacksUsed = append(fallbacksUsed, domainInsightsRes.fallbacks...)
	if debugMode {
		fmt.Printf("[DEBUG] ✅ Domain insights completed (%d tokens)\n", domainInsightsRes.tokens)
	}

	// Separate domains into strengths (top 3) and weaknesses (bottom 3)
	strengthDomains, weaknessDomains := s.separateStrengthsAndWeaknesses(domainInsightsRes.insights, stageAData.DomainScoresForSpiderChart)

	// Wait for AI spotlight
	spotlightRes := <-spotlightChan
	var spotlight AISpotlightInsight
	if spotlightRes.err != nil {
		log.Printf("Warning: Failed to generate AI spotlight: %v", spotlightRes.err)
		spotlight = AISpotlightInsight{
			SpotlightTitle:  "Assessment Insights",
			Analysis:        "Comprehensive assessment reveals opportunities for strategic improvement across multiple domains.",
			KeyInsights:     []string{"Data-driven assessment completed", "Multiple improvement opportunities identified", "Strategic recommendations available"},
			BusinessImpact:  "Implementation of recommendations expected to drive measurable improvements in organizational performance.",
			ActionableSteps: []string{"Review detailed domain insights", "Prioritize improvement initiatives", "Develop implementation timeline"},
		}
		fallbacksUsed = append(fallbacksUsed, "ai_spotlight")
	} else {
		spotlight = spotlightRes.spotlight
		totalTokens += spotlightRes.tokens
		if spotlightRes.provider != "" {
			providersUsed = append(providersUsed, spotlightRes.provider)
		}
		if spotlightRes.fallback {
			fallbacksUsed = append(fallbacksUsed, "ai_spotlight")
		}
	}

	// Wait for focus area
	focusAreaRes := <-focusAreaChan
	var focusArea FocusAreaInsight
	if focusAreaRes.err != nil {
		log.Printf("Warning: Failed to generate focus area: %v", focusAreaRes.err)
		focusArea = FocusAreaInsight{
			FocusTitle:         "Strategic Improvement Focus",
			FocusDescription:   "Based on the assessment results, focus on strengthening key capabilities and addressing critical gaps to drive organizational performance.",
			KeyRecommendations: []string{"Review assessment findings", "Develop targeted improvement plans", "Implement strategic initiatives"},
			ImplementationPlan: "Phase 1: Review assessment results. Phase 2: Develop improvement plan. Phase 3: Execute strategic initiatives.",
			ExpectedOutcomes:   []string{"Improved organizational performance", "Enhanced strategic alignment", "Measurable progress tracking"},
		}
		fallbacksUsed = append(fallbacksUsed, "focus_area")
	} else {
		focusArea = focusAreaRes.focusArea
		totalTokens += focusAreaRes.tokens
		if focusAreaRes.provider != "" {
			providersUsed = append(providersUsed, focusAreaRes.provider)
		}
		if focusAreaRes.fallback {
			fallbacksUsed = append(fallbacksUsed, "focus_area")
		}
	}

	processingTime := time.Since(startTime)
	log.Printf("Stage B AI insight generation completed in %v, tokens used: %d", processingTime, totalTokens)

	// Remove duplicates from providers used
	providersUsed = removeDuplicates(providersUsed)

	// Create the final output
	stageBOutput := &StageBOutput{
		ReportMetadata:   stageAData.ReportMetadata,
		OrganizationName: businessOverview.orgName,
		BusinessSummary:  businessOverview.businessSummary,
		StrengthDomains:  strengthDomains,
		WeaknessDomains:  weaknessDomains,
		AISpotlight:      spotlight,
		FocusArea:        focusArea,
		GenerationMetadata: GenerationMetadata{
			GeneratedAt:         time.Now(),
			TotalProcessingTime: processingTime,
			ProvidersUsed:       providersUsed,
			TotalTokensUsed:     totalTokens,
			FallbacksUsed:       convertFallbacksToMap(fallbacksUsed),
		},
	}

	// Debug logging and save final output
	if debugMode {
		fmt.Printf("[DEBUG] ✅ Stage B: Completed AI insight generation for project: %s\n", projectID)
		fmt.Printf("[DEBUG] 📊 Final output: %d strength domains, %d weakness domains\n",
			len(strengthDomains), len(weaknessDomains))
		fmt.Printf("[DEBUG] 🔢 Total tokens used: %d, Processing time: %v\n", totalTokens, processingTime)

		// Save Stage B output for debugging
		if err := s.saveDebugArtifact("stage_b_output.json", stageBOutput, projectID); err != nil {
			fmt.Printf("[DEBUG] ⚠️  Failed to save Stage B output: %v\n", err)
		}
	}

	return stageBOutput, nil
}

// generateBusinessOverview generates combined organization name and business summary
func (s *StageBService) generateBusinessOverview(ctx context.Context, stageAData *dataextraction.StageAOutput, debugMode bool) businessOverviewResult {
	startTime := time.Now()

	// Check cache first
	projectID := stageAData.ReportMetadata.ProjectID
	runID := stageAData.ReportMetadata.RunID

	if cachedInsight, err := s.repo.GetCachedInsight(projectID, runID, "business_overview"); err == nil && cachedInsight != nil {
		if debugMode {
			fmt.Printf("[DEBUG] 📋 Using cached business overview (saved %d tokens)\n", cachedInsight.TokensUsed)
		}

		// Parse cached JSON
		var result struct {
			OrganizationName struct {
				GeneratedName string `json:"generated_name"`
				Reasoning     string `json:"reasoning"`
			} `json:"organization_name"`
			BusinessSummary struct {
				ExecutiveSummary    string   `json:"executive_summary"`
				KeyFindings         []string `json:"key_findings"`
				StrategicPriorities []string `json:"strategic_priorities"`
			} `json:"business_summary"`
		}

		if err := json.Unmarshal([]byte(cachedInsight.InsightData), &result); err == nil {
			orgName := OrganizationNameInsight{
				GeneratedName: result.OrganizationName.GeneratedName,
				Reasoning:     result.OrganizationName.Reasoning,
			}

			businessSummary := BusinessSummaryInsight{
				ExecutiveSummary:    result.BusinessSummary.ExecutiveSummary,
				KeyFindings:         result.BusinessSummary.KeyFindings,
				StrategicPriorities: result.BusinessSummary.StrategicPriorities,
			}

			return businessOverviewResult{
				orgName:         orgName,
				businessSummary: businessSummary,
				tokens:          cachedInsight.TokensUsed,
				provider:        cachedInsight.ProviderUsed,
				fallback:        cachedInsight.UsedFallback,
				err:             nil,
			}
		} else if debugMode {
			fmt.Printf("[DEBUG] ⚠️ Failed to parse cached business overview: %v\n", err)
		}
	}

	// Get document heads for organizational context (much lighter than full assessment data)
	documentHeads, err := s.repo.GetDocumentHeads(projectID)
	if err != nil {
		if debugMode {
			fmt.Printf("[DEBUG] ⚠️ Failed to get document heads, using minimal context: %v\n", err)
		}
		documentHeads = []dataextraction.DocumentHead{} // Use empty slice as fallback
	}

	// Prepare minimal input data for the LLM (much smaller context)
	inputData := map[string]interface{}{
		"document_heads":   documentHeads,
		"domain_scores":    stageAData.DomainScoresForSpiderChart,
		"overall_maturity": stageAData.OverallMaturity,
		"framework_name":   stageAData.ReportMetadata.FrameworkName,
		"key_strengths":    stageAData.KeyStrengths,
		"critical_areas":   stageAData.CriticalAreasFocus,
	}

	contextJSON, _ := json.Marshal(inputData)

	request := LLMRequest{
		PromptID:  "business_overview",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("business-overview-%d", time.Now().Unix()),
	}

	// Generate using the combined business overview prompt
	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		if debugMode {
			fmt.Printf("[DEBUG] ❌ Business overview generation failed: %v\n", err)
		}
		return businessOverviewResult{err: err}
	}

	if debugMode {
		fmt.Printf("[DEBUG] ✅ Business overview LLM response received (%d tokens, %v)\n", response.TokensUsed, time.Since(startTime))
	}

	// Parse the response
	var result struct {
		OrganizationName struct {
			GeneratedName string `json:"generated_name"`
			Reasoning     string `json:"reasoning"`
		} `json:"organization_name"`
		BusinessSummary struct {
			ExecutiveSummary    string   `json:"executive_summary"`
			KeyFindings         []string `json:"key_findings"`
			StrategicPriorities []string `json:"strategic_priorities"`
		} `json:"business_summary"`
	}

	if err := json.Unmarshal([]byte(response.Content), &result); err != nil {
		if debugMode {
			fmt.Printf("[DEBUG] ❌ Failed to parse business overview response: %v\n", err)
			fmt.Printf("[DEBUG] Raw response: %s\n", response.Content)
		}
		return businessOverviewResult{err: fmt.Errorf("failed to parse business overview response: %v", err)}
	}

	orgName := OrganizationNameInsight{
		GeneratedName: result.OrganizationName.GeneratedName,
		Reasoning:     result.OrganizationName.Reasoning,
	}

	businessSummary := BusinessSummaryInsight{
		ExecutiveSummary:    result.BusinessSummary.ExecutiveSummary,
		KeyFindings:         result.BusinessSummary.KeyFindings,
		StrategicPriorities: result.BusinessSummary.StrategicPriorities,
	}

	if debugMode {
		fmt.Printf("[DEBUG] ✅ Business overview parsed successfully\n")
	}

	// Cache the result for future use
	cacheData := &dataextraction.CachedInsight{
		ProjectID:    projectID,
		RunID:        runID,
		InsightType:  "business_overview",
		InsightData:  response.Content, // Store the raw JSON response
		TokensUsed:   response.TokensUsed,
		ProviderUsed: response.Provider,
		UsedFallback: response.UsedFallback,
		CreatedAt:    time.Now(),
	}

	if err := s.repo.SaveCachedInsight(cacheData); err != nil && debugMode {
		fmt.Printf("[DEBUG] ⚠️ Failed to cache business overview: %v\n", err)
	} else if debugMode {
		fmt.Printf("[DEBUG] 💾 Cached business overview for future use\n")
	}

	return businessOverviewResult{
		orgName:         orgName,
		businessSummary: businessSummary,
		tokens:          response.TokensUsed,
		provider:        response.Provider,
		fallback:        response.UsedFallback,
		err:             nil,
	}
}

// generateOrganizationName generates an organization name using AI
func (s *StageBService) generateOrganizationName(ctx context.Context, data *dataextraction.StageAOutput) (OrganizationNameInsight, int, string, bool, error) {
	// Prepare filtered context data - only essential information for organization naming
	contextData := map[string]interface{}{
		"framework_name":     data.ReportMetadata.FrameworkName,
		"framework_overview": data.ContextForSpotlightAndAI.FrameworkOverview,
		"document_names":     data.ContextForSpotlightAndAI.DocumentNamesProcessed,
		"overall_maturity":   data.OverallMaturity,
		"domain_scores":      data.DomainScoresForSpiderChart, // Only top-level domain scores
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "organization_name",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("org-name-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return OrganizationNameInsight{}, 0, "", false, err
	}

	// Parse the response (assuming it's JSON)
	var insight OrganizationNameInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		// If JSON parsing fails, treat as plain text
		insight = OrganizationNameInsight{
			GeneratedName: "Organization", // Default fallback
			Reasoning:     response.Content,
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// generateBusinessSummary generates the business summary using AI
func (s *StageBService) generateBusinessSummary(ctx context.Context, data *dataextraction.StageAOutput) (BusinessSummaryInsight, int, string, bool, error) {
	contextData := map[string]interface{}{
		"overall_maturity":         data.OverallMaturity,
		"domain_scores":            data.DomainScoresForSpiderChart,
		"key_strengths":            data.KeyStrengths,
		"critical_areas":           data.CriticalAreasFocus,
		"framework_overview":       data.ContextForSpotlightAndAI.FrameworkOverview,
		"low_scoring_capabilities": data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning,
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "business_summary",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("business-summary-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return BusinessSummaryInsight{}, 0, "", false, err
	}

	var insight BusinessSummaryInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		insight = BusinessSummaryInsight{
			ExecutiveSummary:    response.Content,
			KeyFindings:         []string{"Analysis completed"},
			StrategicPriorities: []string{"Review detailed insights"},
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// generateDomainInsightsParallel generates insights for each domain in parallel
func (s *StageBService) generateDomainInsightsParallel(ctx context.Context, data *dataextraction.StageAOutput, debugMode bool) ([]DomainInsight, int, []string, []string) {
	domains := data.DomainScoresForSpiderChart

	// Create channels for parallel domain processing
	type domainResult struct {
		insight  DomainInsight
		tokens   int
		provider string
		fallback bool
		err      error
		index    int
	}

	resultChan := make(chan domainResult, len(domains))

	// Start parallel processing for each domain
	for i, domain := range domains {
		go func(index int, domainData dataextraction.DomainScore) {
			if debugMode {
				fmt.Printf("[DEBUG] 🎯 Starting domain insight generation for: %s\n", domainData.DomainName)
			}

			startTime := time.Now()

			// Filter data to only relevant capabilities for this specific domain
			contextData := map[string]interface{}{
				"domain_name":              domainData.DomainName,
				"domain_score":             domainData.AverageScore,
				"capability_count":         domainData.CapabilityCount,
				"all_domain_scores":        data.ContextForSpotlightAndAI.AllDomainScoresSummary,
				"low_scoring_capabilities": s.filterLowScoringCapabilitiesForDomain(data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning, domainData.DomainName, 3),
			}

			contextJSON, _ := json.Marshal(contextData)

			request := LLMRequest{
				PromptID:  "domain_insights",
				UserQuery: string(contextJSON),
				RequestID: fmt.Sprintf("domain-insights-%s-%d", domainData.DomainName, time.Now().Unix()),
			}

			response, err := s.llmInterface.CallLLM(ctx, request)
			if err != nil {
				if debugMode {
					fmt.Printf("[DEBUG] ❌ Domain insight generation failed for %s: %v\n", domainData.DomainName, err)
				}
				resultChan <- domainResult{
					insight:  DomainInsight{},
					tokens:   0,
					provider: "",
					fallback: false,
					err:      err,
					index:    index,
				}
				return
			}

			if debugMode {
				fmt.Printf("[DEBUG] ✅ Domain insight LLM response received for %s (%d tokens, %v)\n",
					domainData.DomainName, response.TokensUsed, time.Since(startTime))
			}

			var insight DomainInsight
			if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
				if debugMode {
					fmt.Printf("[DEBUG] ❌ Failed to parse domain insight response for %s: %v\n", domainData.DomainName, err)
				}
				// Use fallback
				insight = DomainInsight{
					DomainName:       domainData.DomainName,
					CurrentState:     "Assessment completed for this domain",
					KeyStrengths:     []string{"Domain evaluated"},
					ImprovementAreas: []string{"Review detailed assessment"},
					Recommendations:  []string{"Analyze domain performance"},
					NextSteps:        []string{"Review findings"},
				}
			}

			resultChan <- domainResult{
				insight:  insight,
				tokens:   response.TokensUsed,
				provider: response.Provider,
				fallback: response.UsedFallback,
				err:      nil,
				index:    index,
			}
		}(i, domain)
	}

	// Collect results
	insights := make([]DomainInsight, len(domains))
	var totalTokens int
	var providersUsed []string
	var fallbacksUsed []string

	for i := 0; i < len(domains); i++ {
		result := <-resultChan

		if result.err != nil {
			// Use fallback for failed domain
			insights[result.index] = DomainInsight{
				DomainName:       domains[result.index].DomainName,
				CurrentState:     "Assessment completed for this domain",
				KeyStrengths:     []string{"Domain evaluated"},
				ImprovementAreas: []string{"Review detailed assessment"},
				Recommendations:  []string{"Analyze domain performance"},
				NextSteps:        []string{"Review findings"},
			}
			fallbacksUsed = append(fallbacksUsed, "domain_insights")
		} else {
			insights[result.index] = result.insight
			totalTokens += result.tokens
			if result.provider != "" {
				providersUsed = append(providersUsed, result.provider)
			}
			if result.fallback {
				fallbacksUsed = append(fallbacksUsed, "domain_insights")
			}
		}

		if debugMode {
			fmt.Printf("[DEBUG] ✅ Domain insight completed for: %s\n", domains[result.index].DomainName)
		}
	}

	return insights, totalTokens, providersUsed, fallbacksUsed
}

// generateDomainInsights generates insights for each domain
func (s *StageBService) generateDomainInsights(ctx context.Context, data *dataextraction.StageAOutput) ([]DomainInsight, int, []string, []string) {
	var insights []DomainInsight
	var totalTokens int
	var providersUsed []string
	var fallbacksUsed []string

	// Generate insights for each domain in domain scores
	for _, domain := range data.DomainScoresForSpiderChart {
		// Filter data to only relevant capabilities for this specific domain
		contextData := map[string]interface{}{
			"domain_name":       domain.DomainName,
			"domain_score":      domain.AverageScore,
			"capability_count":  domain.CapabilityCount,
			"all_domain_scores": data.ContextForSpotlightAndAI.AllDomainScoresSummary,
			// REMOVED: "leaf_capabilities" - this was sending ALL capabilities (200k+ tokens)
			"low_scoring_capabilities": s.filterLowScoringCapabilitiesForDomain(data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning, domain.DomainName, 3),
		}

		contextJSON, _ := json.Marshal(contextData)

		request := LLMRequest{
			PromptID:  "domain_insights",
			UserQuery: string(contextJSON),
			RequestID: fmt.Sprintf("domain-%s-%d", domain.DomainName, time.Now().Unix()),
		}

		response, err := s.llmInterface.CallLLM(ctx, request)
		if err != nil {
			log.Printf("Warning: Failed to generate insights for domain %s: %v", domain.DomainName, err)
			// Create fallback insight
			insights = append(insights, DomainInsight{
				DomainName:       domain.DomainName,
				CurrentState:     fmt.Sprintf("Domain scored %.1f out of 5.0", domain.AverageScore),
				KeyStrengths:     []string{"Assessment completed"},
				ImprovementAreas: []string{"Detailed analysis pending"},
				Recommendations:  []string{"Review domain-specific data"},
				NextSteps:        []string{"Engage with domain experts"},
			})
			fallbacksUsed = append(fallbacksUsed, fmt.Sprintf("domain_%s", domain.DomainName))
			continue
		}

		var insight DomainInsight
		if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
			insight = DomainInsight{
				DomainName:       domain.DomainName,
				CurrentState:     response.Content,
				KeyStrengths:     []string{"Analysis provided"},
				ImprovementAreas: []string{"See detailed analysis"},
				Recommendations:  []string{"Review AI analysis"},
				NextSteps:        []string{"Implement recommendations"},
			}
		}

		insights = append(insights, insight)
		totalTokens += response.TokensUsed
		if response.Provider != "" {
			providersUsed = append(providersUsed, response.Provider)
		}
		if response.UsedFallback {
			fallbacksUsed = append(fallbacksUsed, fmt.Sprintf("domain_%s", domain.DomainName))
		}
	}

	return insights, totalTokens, providersUsed, fallbacksUsed
}

// generateAISpotlight generates the AI spotlight analysis
func (s *StageBService) generateAISpotlight(ctx context.Context, data *dataextraction.StageAOutput) (AISpotlightInsight, int, string, bool, error) {
	// Filter data to only most influential snippets to reduce token usage
	contextData := map[string]interface{}{
		"overall_maturity":   data.OverallMaturity,
		"domain_scores":      data.DomainScoresForSpiderChart,
		"key_strengths":      data.KeyStrengths,
		"critical_areas":     data.CriticalAreasFocus,
		"framework_overview": data.ContextForSpotlightAndAI.FrameworkOverview,
		// REMOVED: "all_leaf_capabilities" - this was causing 200k+ tokens
		"low_scoring_capabilities": s.filterTopLowScoringCapabilities(data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning, 5), // Only top 5 worst
		"documents_processed":      data.ContextForSpotlightAndAI.DocumentNamesProcessed,
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "ai_spotlight",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("spotlight-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return AISpotlightInsight{}, 0, "", false, err
	}

	var insight AISpotlightInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		insight = AISpotlightInsight{
			SpotlightTitle:  "AI Analysis Spotlight",
			Analysis:        response.Content,
			KeyInsights:     []string{"Comprehensive analysis completed"},
			BusinessImpact:  "Strategic insights generated for organizational improvement",
			ActionableSteps: []string{"Review analysis", "Develop action plan", "Monitor progress"},
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// generateFocusArea generates AI-driven focus area recommendations
func (s *StageBService) generateFocusArea(ctx context.Context, data *dataextraction.StageAOutput) (FocusAreaInsight, int, string, bool, error) {
	// Filter data to only most critical information for focus area recommendations
	contextData := map[string]interface{}{
		"overall_maturity":         data.OverallMaturity,
		"critical_areas":           data.CriticalAreasFocus,
		"framework_overview":       data.ContextForSpotlightAndAI.FrameworkOverview,
		"low_scoring_capabilities": s.filterTopLowScoringCapabilities(data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning, 3), // Only top 3 worst
		"documents_processed":      data.ContextForSpotlightAndAI.DocumentNamesProcessed,
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "focus_area",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("focus-area-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return FocusAreaInsight{}, 0, "", false, err
	}

	var insight FocusAreaInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		insight = FocusAreaInsight{
			FocusTitle:         "Strategic Focus Area",
			FocusDescription:   response.Content,
			KeyRecommendations: []string{"Review AI analysis", "Implement strategic initiatives", "Monitor progress"},
			ImplementationPlan: "Structured approach to implementing recommended solutions",
			ExpectedOutcomes:   []string{"Improved performance", "Strategic alignment", "Sustainable growth"},
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// separateStrengthsAndWeaknesses separates domain insights into strengths and weaknesses based on scores
func (s *StageBService) separateStrengthsAndWeaknesses(domainInsights []DomainInsight, domainScores []dataextraction.DomainScore) ([]DomainInsight, []DomainInsight) {
	// Create a map of domain names to scores for quick lookup
	scoreMap := make(map[string]float64)
	for _, score := range domainScores {
		scoreMap[score.DomainName] = score.AverageScore
	}

	// Sort domain insights by score (highest to lowest)
	sortedInsights := make([]DomainInsight, len(domainInsights))
	copy(sortedInsights, domainInsights)

	// Sort by score descending
	for i := 0; i < len(sortedInsights)-1; i++ {
		for j := i + 1; j < len(sortedInsights); j++ {
			scoreI := scoreMap[sortedInsights[i].DomainName]
			scoreJ := scoreMap[sortedInsights[j].DomainName]
			if scoreI < scoreJ {
				sortedInsights[i], sortedInsights[j] = sortedInsights[j], sortedInsights[i]
			}
		}
	}

	// Take top 3 as strengths, bottom 3 as weaknesses
	var strengthDomains, weaknessDomains []DomainInsight

	// Strengths: top 3 (or all if less than 3)
	strengthCount := 3
	if len(sortedInsights) < strengthCount {
		strengthCount = len(sortedInsights)
	}
	strengthDomains = sortedInsights[:strengthCount]

	// Weaknesses: bottom 3 (or all if less than 3)
	weaknessCount := 3
	if len(sortedInsights) < weaknessCount {
		weaknessCount = len(sortedInsights)
	}
	startIndex := len(sortedInsights) - weaknessCount
	if startIndex < 0 {
		startIndex = 0
	}
	weaknessDomains = sortedInsights[startIndex:]

	return strengthDomains, weaknessDomains
}

// filterTopLowScoringCapabilities filters to only the worst performing capabilities to reduce token usage
func (s *StageBService) filterTopLowScoringCapabilities(capabilities []dataextraction.LowScoringCapabilityDetail, limit int) []dataextraction.LowScoringCapabilityDetail {
	if len(capabilities) <= limit {
		return capabilities
	}

	// Sort by score ascending (worst first)
	sorted := make([]dataextraction.LowScoringCapabilityDetail, len(capabilities))
	copy(sorted, capabilities)

	for i := 0; i < len(sorted)-1; i++ {
		for j := i + 1; j < len(sorted); j++ {
			if sorted[i].Score > sorted[j].Score {
				sorted[i], sorted[j] = sorted[j], sorted[i]
			}
		}
	}

	return sorted[:limit]
}

// filterLowScoringCapabilitiesForDomain filters low-scoring capabilities to only those in the specified domain
func (s *StageBService) filterLowScoringCapabilitiesForDomain(capabilities []dataextraction.LowScoringCapabilityDetail, domainName string, limit int) []dataextraction.LowScoringCapabilityDetail {
	var domainCapabilities []dataextraction.LowScoringCapabilityDetail

	// Filter to only capabilities in the specified domain
	for _, cap := range capabilities {
		if cap.Domain == domainName {
			domainCapabilities = append(domainCapabilities, cap)
		}
	}

	// Apply limit
	if len(domainCapabilities) <= limit {
		return domainCapabilities
	}

	// Sort by score ascending (worst first) and take top N
	sorted := make([]dataextraction.LowScoringCapabilityDetail, len(domainCapabilities))
	copy(sorted, domainCapabilities)

	for i := 0; i < len(sorted)-1; i++ {
		for j := i + 1; j < len(sorted); j++ {
			if sorted[i].Score > sorted[j].Score {
				sorted[i], sorted[j] = sorted[j], sorted[i]
			}
		}
	}

	return sorted[:limit]
}

// saveDebugArtifact saves debug artifacts to logs/debug directory
func (s *StageBService) saveDebugArtifact(filename string, data interface{}, projectID string) error {
	if os.Getenv("DEBUG") != "true" && os.Getenv("DEBUG") != "1" {
		return nil // Skip if debug mode is not enabled
	}

	// Create debug directory
	debugDir := "logs/debug"
	if err := os.MkdirAll(debugDir, 0755); err != nil {
		return fmt.Errorf("failed to create debug directory: %w", err)
	}

	// Add timestamp and project ID to filename
	timestamp := time.Now().Format("20060102_150405")
	ext := filepath.Ext(filename)
	name := strings.TrimSuffix(filename, ext)
	debugFilename := fmt.Sprintf("%s_%s_%s%s", name, projectID, timestamp, ext)
	debugPath := filepath.Join(debugDir, debugFilename)

	// Marshal JSON with indentation for readability
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal JSON: %w", err)
	}

	// Write to file
	if err := os.WriteFile(debugPath, jsonData, 0644); err != nil {
		return fmt.Errorf("failed to write debug file: %w", err)
	}

	fmt.Printf("[DEBUG] 💾 Saved debug artifact: %s (%d bytes)\n", debugPath, len(jsonData))
	return nil
}

// removeDuplicates removes duplicate strings from a slice
func removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}

// convertFallbacksToMap converts a slice of fallback names to a map
func convertFallbacksToMap(fallbacks []string) map[string]bool {
	result := make(map[string]bool)
	for _, fallback := range fallbacks {
		result[fallback] = true
	}
	return result
}
