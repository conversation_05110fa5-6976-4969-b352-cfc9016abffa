package aiinsights

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"capabilisense-reporting-service/pkg/config"
	"capabilisense-reporting-service/pkg/dataextraction"
)

// StageBService handles AI insight generation for Stage B
type StageBService struct {
	llmInterface *LLMInterface
	library      *config.PromptsLibrary
}

// NewStageBService creates a new Stage B service
func NewStageBService(library *config.PromptsLibrary) *StageBService {
	llmInterface := NewLLMInterface(library)
	return &StageBService{
		llmInterface: llmInterface,
		library:      library,
	}
}

// StageBOutput represents the complete AI-generated insights
type StageBOutput struct {
	ReportMetadata     dataextraction.ReportMetadata `json:"report_metadata"`
	OrganizationName   OrganizationNameInsight       `json:"organization_name"`
	OverallSummary     OverallSummaryInsight         `json:"overall_summary"`
	DomainInsights     []DomainInsight               `json:"domain_insights"`
	AISpotlight        AISpotlightInsight            `json:"ai_spotlight"`
	ETSSolutions       ETSSolutionsInsight           `json:"ets_solutions"`
	GenerationMetadata GenerationMetadata            `json:"generation_metadata"`
}

// OrganizationNameInsight contains the AI-generated organization name
type OrganizationNameInsight struct {
	GeneratedName string `json:"generated_name"`
	Reasoning     string `json:"reasoning"`
}

// OverallSummaryInsight contains the executive summary
type OverallSummaryInsight struct {
	ExecutiveSummary    string   `json:"executive_summary"`
	KeyFindings         []string `json:"key_findings"`
	StrategicPriorities []string `json:"strategic_priorities"`
}

// DomainInsight contains insights for a specific domain
type DomainInsight struct {
	DomainName       string   `json:"domain_name"`
	CurrentState     string   `json:"current_state"`
	KeyStrengths     []string `json:"key_strengths"`
	ImprovementAreas []string `json:"improvement_areas"`
	Recommendations  []string `json:"recommendations"`
	NextSteps        []string `json:"next_steps"`
}

// AISpotlightInsight contains the AI spotlight analysis
type AISpotlightInsight struct {
	SpotlightTitle  string   `json:"spotlight_title"`
	Analysis        string   `json:"analysis"`
	KeyInsights     []string `json:"key_insights"`
	BusinessImpact  string   `json:"business_impact"`
	ActionableSteps []string `json:"actionable_steps"`
}

// ETSSolutionsInsight contains ETS-specific solutions
type ETSSolutionsInsight struct {
	RecommendedSolutions []ETSSolution `json:"recommended_solutions"`
	ImplementationPlan   string        `json:"implementation_plan"`
	ExpectedOutcomes     []string      `json:"expected_outcomes"`
}

// Note: ETSSolution and GenerationMetadata are already defined in models.go

// GenerateInsights generates all AI insights for Stage B
func (s *StageBService) GenerateInsights(ctx context.Context, stageAData *dataextraction.StageAOutput) (*StageBOutput, error) {
	startTime := time.Now()
	log.Printf("Starting Stage B AI insight generation for project %s", stageAData.ReportMetadata.ProjectID)

	var providersUsed []string
	var fallbacksUsed []string
	totalTokens := 0

	// Generate organization name
	orgName, tokens, provider, fallback, err := s.generateOrganizationName(ctx, stageAData)
	if err != nil {
		log.Printf("Warning: Failed to generate organization name: %v", err)
		orgName = OrganizationNameInsight{
			GeneratedName: "Organization",
			Reasoning:     "Unable to generate custom name due to AI service unavailability",
		}
	} else {
		totalTokens += tokens
		if provider != "" {
			providersUsed = append(providersUsed, provider)
		}
		if fallback {
			fallbacksUsed = append(fallbacksUsed, "organization_name")
		}
	}

	// Generate overall summary
	summary, tokens, provider, fallback, err := s.generateOverallSummary(ctx, stageAData)
	if err != nil {
		log.Printf("Warning: Failed to generate overall summary: %v", err)
		summary = OverallSummaryInsight{
			ExecutiveSummary:    "Assessment completed with mixed results across domains. Detailed analysis available in domain-specific sections.",
			KeyFindings:         []string{"Assessment data processed", "Multiple domains evaluated", "Recommendations generated"},
			StrategicPriorities: []string{"Focus on improvement areas", "Leverage existing strengths", "Implement targeted solutions"},
		}
	} else {
		totalTokens += tokens
		if provider != "" {
			providersUsed = append(providersUsed, provider)
		}
		if fallback {
			fallbacksUsed = append(fallbacksUsed, "overall_summary")
		}
	}

	// Generate domain insights
	domainInsights, tokens, providers, fallbacks := s.generateDomainInsights(ctx, stageAData)
	totalTokens += tokens
	providersUsed = append(providersUsed, providers...)
	fallbacksUsed = append(fallbacksUsed, fallbacks...)

	// Generate AI spotlight
	spotlight, tokens, provider, fallback, err := s.generateAISpotlight(ctx, stageAData)
	if err != nil {
		log.Printf("Warning: Failed to generate AI spotlight: %v", err)
		spotlight = AISpotlightInsight{
			SpotlightTitle:  "Assessment Insights",
			Analysis:        "Comprehensive assessment reveals opportunities for strategic improvement across multiple domains.",
			KeyInsights:     []string{"Data-driven assessment completed", "Multiple improvement opportunities identified", "Strategic recommendations available"},
			BusinessImpact:  "Implementation of recommendations expected to drive measurable improvements in organizational performance.",
			ActionableSteps: []string{"Review detailed domain insights", "Prioritize improvement initiatives", "Develop implementation timeline"},
		}
	} else {
		totalTokens += tokens
		if provider != "" {
			providersUsed = append(providersUsed, provider)
		}
		if fallback {
			fallbacksUsed = append(fallbacksUsed, "ai_spotlight")
		}
	}

	// Generate ETS solutions
	etsSolutions, tokens, provider, fallback, err := s.generateETSSolutions(ctx, stageAData)
	if err != nil {
		log.Printf("Warning: Failed to generate ETS solutions: %v", err)
		etsSolutions = ETSSolutionsInsight{
			RecommendedSolutions: []ETSSolution{
				{
					AreaTitle:      "Strategic Assessment Review",
					Description:    "Comprehensive review of assessment results with strategic recommendations",
					Benefits:       []string{"Clear improvement roadmap", "Data-driven insights", "Strategic alignment"},
					Implementation: "Engage with ETS consultants for detailed implementation planning",
					Priority:       "High",
					Confidence:     0.8,
				},
			},
			ImplementationPlan: "Phase 1: Review assessment results. Phase 2: Develop improvement plan. Phase 3: Execute strategic initiatives.",
			ExpectedOutcomes:   []string{"Improved organizational performance", "Enhanced strategic alignment", "Measurable progress tracking"},
		}
	} else {
		totalTokens += tokens
		if provider != "" {
			providersUsed = append(providersUsed, provider)
		}
		if fallback {
			fallbacksUsed = append(fallbacksUsed, "ets_solutions")
		}
	}

	processingTime := time.Since(startTime)
	log.Printf("Stage B AI insight generation completed in %v, tokens used: %d", processingTime, totalTokens)

	// Remove duplicates from providers used
	providersUsed = removeDuplicates(providersUsed)

	return &StageBOutput{
		ReportMetadata:   stageAData.ReportMetadata,
		OrganizationName: orgName,
		OverallSummary:   summary,
		DomainInsights:   domainInsights,
		AISpotlight:      spotlight,
		ETSSolutions:     etsSolutions,
		GenerationMetadata: GenerationMetadata{
			GeneratedAt:         time.Now(),
			TotalProcessingTime: processingTime,
			ProvidersUsed:       providersUsed,
			TotalTokensUsed:     totalTokens,
			FallbacksUsed:       convertFallbacksToMap(fallbacksUsed),
		},
	}, nil
}

// generateOrganizationName generates an organization name using AI
func (s *StageBService) generateOrganizationName(ctx context.Context, data *dataextraction.StageAOutput) (OrganizationNameInsight, int, string, bool, error) {
	// Prepare context data
	contextData := map[string]interface{}{
		"framework_name":     data.ReportMetadata.FrameworkName,
		"framework_overview": data.ContextForSpotlightAndAI.FrameworkOverview,
		"document_names":     data.ContextForSpotlightAndAI.DocumentNamesProcessed,
		"overall_maturity":   data.OverallMaturity,
		"domain_scores":      data.DomainScoresForSpiderChart,
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "organization_name",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("org-name-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return OrganizationNameInsight{}, 0, "", false, err
	}

	// Parse the response (assuming it's JSON)
	var insight OrganizationNameInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		// If JSON parsing fails, treat as plain text
		insight = OrganizationNameInsight{
			GeneratedName: "Organization", // Default fallback
			Reasoning:     response.Content,
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// generateOverallSummary generates the overall summary using AI
func (s *StageBService) generateOverallSummary(ctx context.Context, data *dataextraction.StageAOutput) (OverallSummaryInsight, int, string, bool, error) {
	contextData := map[string]interface{}{
		"overall_maturity":         data.OverallMaturity,
		"domain_scores":            data.DomainScoresForSpiderChart,
		"key_strengths":            data.KeyStrengths,
		"critical_areas":           data.CriticalAreasFocus,
		"framework_overview":       data.ContextForSpotlightAndAI.FrameworkOverview,
		"low_scoring_capabilities": data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning,
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "overall_summary",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("summary-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return OverallSummaryInsight{}, 0, "", false, err
	}

	var insight OverallSummaryInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		insight = OverallSummaryInsight{
			ExecutiveSummary:    response.Content,
			KeyFindings:         []string{"Analysis completed"},
			StrategicPriorities: []string{"Review detailed insights"},
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// generateDomainInsights generates insights for each domain
func (s *StageBService) generateDomainInsights(ctx context.Context, data *dataextraction.StageAOutput) ([]DomainInsight, int, []string, []string) {
	var insights []DomainInsight
	var totalTokens int
	var providersUsed []string
	var fallbacksUsed []string

	// Generate insights for each domain in domain scores
	for _, domain := range data.DomainScoresForSpiderChart {
		contextData := map[string]interface{}{
			"domain_name":              domain.DomainName,
			"domain_score":             domain.AverageScore,
			"capability_count":         domain.CapabilityCount,
			"all_domain_scores":        data.ContextForSpotlightAndAI.AllDomainScoresSummary,
			"leaf_capabilities":        data.ContextForSpotlightAndAI.AllLeafCapabilityScores,
			"low_scoring_capabilities": data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning,
		}

		contextJSON, _ := json.Marshal(contextData)

		request := LLMRequest{
			PromptID:  "domain_insights",
			UserQuery: string(contextJSON),
			RequestID: fmt.Sprintf("domain-%s-%d", domain.DomainName, time.Now().Unix()),
		}

		response, err := s.llmInterface.CallLLM(ctx, request)
		if err != nil {
			log.Printf("Warning: Failed to generate insights for domain %s: %v", domain.DomainName, err)
			// Create fallback insight
			insights = append(insights, DomainInsight{
				DomainName:       domain.DomainName,
				CurrentState:     fmt.Sprintf("Domain scored %.1f out of 5.0", domain.AverageScore),
				KeyStrengths:     []string{"Assessment completed"},
				ImprovementAreas: []string{"Detailed analysis pending"},
				Recommendations:  []string{"Review domain-specific data"},
				NextSteps:        []string{"Engage with domain experts"},
			})
			fallbacksUsed = append(fallbacksUsed, fmt.Sprintf("domain_%s", domain.DomainName))
			continue
		}

		var insight DomainInsight
		if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
			insight = DomainInsight{
				DomainName:       domain.DomainName,
				CurrentState:     response.Content,
				KeyStrengths:     []string{"Analysis provided"},
				ImprovementAreas: []string{"See detailed analysis"},
				Recommendations:  []string{"Review AI analysis"},
				NextSteps:        []string{"Implement recommendations"},
			}
		}

		insights = append(insights, insight)
		totalTokens += response.TokensUsed
		if response.Provider != "" {
			providersUsed = append(providersUsed, response.Provider)
		}
		if response.UsedFallback {
			fallbacksUsed = append(fallbacksUsed, fmt.Sprintf("domain_%s", domain.DomainName))
		}
	}

	return insights, totalTokens, providersUsed, fallbacksUsed
}

// generateAISpotlight generates the AI spotlight analysis
func (s *StageBService) generateAISpotlight(ctx context.Context, data *dataextraction.StageAOutput) (AISpotlightInsight, int, string, bool, error) {
	contextData := map[string]interface{}{
		"overall_maturity":         data.OverallMaturity,
		"domain_scores":            data.DomainScoresForSpiderChart,
		"key_strengths":            data.KeyStrengths,
		"critical_areas":           data.CriticalAreasFocus,
		"framework_overview":       data.ContextForSpotlightAndAI.FrameworkOverview,
		"all_leaf_capabilities":    data.ContextForSpotlightAndAI.AllLeafCapabilityScores,
		"low_scoring_capabilities": data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning,
		"documents_processed":      data.ContextForSpotlightAndAI.DocumentNamesProcessed,
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "ai_spotlight",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("spotlight-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return AISpotlightInsight{}, 0, "", false, err
	}

	var insight AISpotlightInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		insight = AISpotlightInsight{
			SpotlightTitle:  "AI Analysis Spotlight",
			Analysis:        response.Content,
			KeyInsights:     []string{"Comprehensive analysis completed"},
			BusinessImpact:  "Strategic insights generated for organizational improvement",
			ActionableSteps: []string{"Review analysis", "Develop action plan", "Monitor progress"},
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// generateETSSolutions generates ETS-specific solutions
func (s *StageBService) generateETSSolutions(ctx context.Context, data *dataextraction.StageAOutput) (ETSSolutionsInsight, int, string, bool, error) {
	contextData := map[string]interface{}{
		"overall_maturity":         data.OverallMaturity,
		"critical_areas":           data.CriticalAreasFocus,
		"framework_overview":       data.ContextForSpotlightAndAI.FrameworkOverview,
		"low_scoring_capabilities": data.ContextForSpotlightAndAI.KeyLowScoringCapabilitiesWithReasoning,
		"documents_processed":      data.ContextForSpotlightAndAI.DocumentNamesProcessed,
	}

	contextJSON, _ := json.Marshal(contextData)

	request := LLMRequest{
		PromptID:  "ets_solutions",
		UserQuery: string(contextJSON),
		RequestID: fmt.Sprintf("ets-%d", time.Now().Unix()),
	}

	response, err := s.llmInterface.CallLLM(ctx, request)
	if err != nil {
		return ETSSolutionsInsight{}, 0, "", false, err
	}

	var insight ETSSolutionsInsight
	if err := json.Unmarshal([]byte(response.Content), &insight); err != nil {
		insight = ETSSolutionsInsight{
			RecommendedSolutions: []ETSSolution{
				{
					AreaTitle:      "ETS Strategic Consulting",
					Description:    response.Content,
					Benefits:       []string{"Expert guidance", "Proven methodologies", "Measurable results"},
					Implementation: "Engage ETS consultants for detailed planning",
					Priority:       "High",
					Confidence:     0.8,
				},
			},
			ImplementationPlan: "Structured approach to implementing recommended solutions",
			ExpectedOutcomes:   []string{"Improved performance", "Strategic alignment", "Sustainable growth"},
		}
	}

	return insight, response.TokensUsed, response.Provider, response.UsedFallback, nil
}

// removeDuplicates removes duplicate strings from a slice
func removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}

// convertFallbacksToMap converts a slice of fallback names to a map
func convertFallbacksToMap(fallbacks []string) map[string]bool {
	result := make(map[string]bool)
	for _, fallback := range fallbacks {
		result[fallback] = true
	}
	return result
}
