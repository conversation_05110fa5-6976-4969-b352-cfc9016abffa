package aiinsights

import (
	"bufio"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"capabilisense-reporting-service/pkg/config"
)

func TestLLMLogger(t *testing.T) {
	// Create temporary log file
	tempDir := t.TempDir()
	logFile := filepath.Join(tempDir, "test-llm-calls.jsonl")

	logger := NewLLMLogger()
	logger.SetLogFile(logFile)

	// Test model config
	modelConfig := &config.ModelConfig{
		ProviderURL: "https://api.example.com/v1/chat",
		ModelName:   "test-model",
		APIKeyEnv:   "TEST_API_KEY",
	}

	// Test request logging
	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "[REDACTED]",
	}
	payload := map[string]interface{}{
		"model":       "test-model",
		"messages":    []string{"Hello"},
		"temperature": 0.7,
	}

	err := logger.LogRequest("test-provider", modelConfig, "req-123", "test-prompt", payload, headers)
	assert.NoError(t, err)

	// Test response logging
	responseBody := map[string]interface{}{
		"choices": []interface{}{
			map[string]interface{}{
				"message": map[string]interface{}{
					"content": "Hello! How can I help you?",
				},
			},
		},
		"usage": map[string]interface{}{
			"total_tokens": 25,
		},
	}

	err = logger.LogResponse("test-provider", modelConfig, "req-123", "test-prompt",
		time.Now().Add(-2*time.Second), 200, responseBody, "", 2*time.Second, 25, false)
	assert.NoError(t, err)

	// Test error logging
	err = logger.LogError("test-provider", modelConfig, "req-124", "test-prompt",
		"Connection timeout", 5*time.Second)
	assert.NoError(t, err)

	// Test fallback logging
	err = logger.LogFallback("req-125", "test-prompt", "All providers failed", 3*time.Second)
	assert.NoError(t, err)

	// Verify log file exists and has content
	assert.FileExists(t, logFile)

	// Read and verify log entries
	file, err := os.Open(logFile)
	require.NoError(t, err)
	defer file.Close()

	scanner := bufio.NewScanner(file)
	var entries []LLMLogEntry

	for scanner.Scan() {
		var entry LLMLogEntry
		err := json.Unmarshal(scanner.Bytes(), &entry)
		require.NoError(t, err)
		entries = append(entries, entry)
	}

	require.Len(t, entries, 4, "Should have 4 log entries")

	// Verify request entry
	requestEntry := entries[0]
	assert.Equal(t, "llm_request", requestEntry.EventType)
	assert.Equal(t, "test-provider", requestEntry.ProviderAlias)
	assert.Equal(t, "req-123", requestEntry.RequestID)
	assert.Equal(t, "test-prompt", requestEntry.PromptID)
	assert.Equal(t, headers, requestEntry.RequestHeaders)
	assert.Equal(t, payload, requestEntry.RequestPayload)

	// Verify response entry
	responseEntry := entries[1]
	assert.Equal(t, "llm_response", responseEntry.EventType)
	assert.Equal(t, "test-provider", responseEntry.ProviderAlias)
	assert.Equal(t, "req-123", responseEntry.RequestID)
	assert.Equal(t, 200, responseEntry.ResponseStatusCode)
	assert.Equal(t, 25, responseEntry.TokensUsed)
	assert.False(t, responseEntry.UsedFallback)

	// Verify error entry
	errorEntry := entries[2]
	assert.Equal(t, "llm_error", errorEntry.EventType)
	assert.Equal(t, "ERROR", errorEntry.Level)
	assert.Equal(t, "req-124", errorEntry.RequestID)
	assert.Equal(t, "Connection timeout", errorEntry.ErrorMessage)

	// Verify fallback entry
	fallbackEntry := entries[3]
	assert.Equal(t, "llm_fallback", fallbackEntry.EventType)
	assert.Equal(t, "fallback", fallbackEntry.ProviderAlias)
	assert.Equal(t, "req-125", fallbackEntry.RequestID)
	assert.True(t, fallbackEntry.UsedFallback)
	assert.Equal(t, "All providers failed", fallbackEntry.ErrorMessage)
}

func TestLLMLoggerConcurrency(t *testing.T) {
	// Test concurrent logging
	tempDir := t.TempDir()
	logFile := filepath.Join(tempDir, "concurrent-test.jsonl")

	logger := NewLLMLogger()
	logger.SetLogFile(logFile)

	modelConfig := &config.ModelConfig{
		ProviderURL: "https://api.example.com/v1/chat",
		ModelName:   "test-model",
	}

	// Launch multiple goroutines to test thread safety
	done := make(chan bool, 10)

	for i := 0; i < 10; i++ {
		go func(id int) {
			defer func() { done <- true }()

			requestID := fmt.Sprintf("req-%d", id)
			err := logger.LogRequest("test-provider", modelConfig, requestID, "test-prompt",
				map[string]interface{}{"test": id}, map[string]string{"Content-Type": "application/json"})
			assert.NoError(t, err)
		}(i)
	}

	// Wait for all goroutines to complete
	for i := 0; i < 10; i++ {
		<-done
	}

	// Verify all entries were logged
	file, err := os.Open(logFile)
	require.NoError(t, err)
	defer file.Close()

	scanner := bufio.NewScanner(file)
	entryCount := 0
	for scanner.Scan() {
		entryCount++
	}

	assert.Equal(t, 10, entryCount, "Should have 10 log entries")
}

func TestLLMLoggerDirectoryCreation(t *testing.T) {
	// Test that logger creates directory if it doesn't exist
	tempDir := t.TempDir()
	logFile := filepath.Join(tempDir, "nested", "dir", "test.jsonl")

	logger := NewLLMLogger()
	logger.SetLogFile(logFile)

	modelConfig := &config.ModelConfig{
		ProviderURL: "https://api.example.com/v1/chat",
		ModelName:   "test-model",
	}

	err := logger.LogRequest("test-provider", modelConfig, "req-123", "test-prompt",
		map[string]interface{}{}, map[string]string{})
	assert.NoError(t, err)

	// Verify directory was created
	assert.DirExists(t, filepath.Dir(logFile))
	assert.FileExists(t, logFile)
}
