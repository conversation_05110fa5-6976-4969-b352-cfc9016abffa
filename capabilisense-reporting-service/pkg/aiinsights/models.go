package aiinsights

import "time"

// InsightRequest represents a request for AI-generated insights
type InsightRequest struct {
	PromptID   string                 `json:"prompt_id"`
	InputData  map[string]interface{} `json:"input_data"`
	Parameters map[string]string      `json:"parameters,omitempty"`
	Context    string                 `json:"context,omitempty"`
	RequestID  string                 `json:"request_id"`
	Timestamp  time.Time              `json:"timestamp"`
}

// InsightResponse represents the response from an AI insight generation
type InsightResponse struct {
	RequestID      string                 `json:"request_id"`
	PromptID       string                 `json:"prompt_id"`
	Content        string                 `json:"content"`
	StructuredData map[string]interface{} `json:"structured_data,omitempty"`
	Confidence     float64                `json:"confidence,omitempty"`
	Provider       string                 `json:"provider"`
	Model          string                 `json:"model"`
	TokensUsed     int                    `json:"tokens_used,omitempty"`
	ProcessingTime time.Duration          `json:"processing_time"`
	Timestamp      time.Time              `json:"timestamp"`
	Error          string                 `json:"error,omitempty"`
	UsedFallback   bool                   `json:"used_fallback"`
}

// OrganizationInsight represents insights about the organization
type OrganizationInsight struct {
	Name        string `json:"name"`
	Focus       string `json:"focus"`
	Description string `json:"description,omitempty"`
}

// OverallSummary represents the overall assessment summary
type OverallSummary struct {
	Statement     string   `json:"statement"`
	KeyThemes     []string `json:"key_themes"`
	MaturityLevel string   `json:"maturity_level"`
	Confidence    float64  `json:"confidence"`
}

// DomainInsightAI represents AI-generated insights for a specific domain
type DomainInsightAI struct {
	DomainID        string   `json:"domain_id"`
	DomainName      string   `json:"domain_name"`
	InsightType     string   `json:"insight_type"` // "strength" or "focus_area"
	Title           string   `json:"title"`
	Description     string   `json:"description"`
	KeyPoints       []string `json:"key_points"`
	Recommendations []string `json:"recommendations,omitempty"`
	Confidence      float64  `json:"confidence"`
}

// AISpotlight represents the AI spotlight section
type AISpotlight struct {
	Title      string  `json:"title"`
	Content    string  `json:"content"`
	Category   string  `json:"category,omitempty"`
	Impact     string  `json:"impact,omitempty"`
	Confidence float64 `json:"confidence"`
}

// ETSSolution represents an ETS solution recommendation
type ETSSolution struct {
	AreaTitle      string   `json:"area_title"`
	Description    string   `json:"description"`
	Benefits       []string `json:"benefits,omitempty"`
	Implementation string   `json:"implementation,omitempty"`
	Priority       string   `json:"priority,omitempty"`
	Confidence     float64  `json:"confidence"`
}

// InsightGenerationResult represents the complete result of insight generation
type InsightGenerationResult struct {
	OrganizationInsight OrganizationInsight `json:"organization_insight"`
	OverallSummary      OverallSummary      `json:"overall_summary"`
	DomainInsights      []DomainInsightAI   `json:"domain_insights"`
	AISpotlight         AISpotlight         `json:"ai_spotlight"`
	ETSSolutions        []ETSSolution       `json:"ets_solutions"`
	GenerationMetadata  GenerationMetadata  `json:"generation_metadata"`
}

// GenerationMetadata contains metadata about the insight generation process
type GenerationMetadata struct {
	GeneratedAt         time.Time          `json:"generated_at"`
	TotalTokensUsed     int                `json:"total_tokens_used"`
	TotalProcessingTime time.Duration      `json:"total_processing_time"`
	ProvidersUsed       []string           `json:"providers_used"`
	FallbacksUsed       map[string]bool    `json:"fallbacks_used"`
	QualityMetrics      map[string]float64 `json:"quality_metrics"`
	Errors              []string           `json:"errors,omitempty"`
}
