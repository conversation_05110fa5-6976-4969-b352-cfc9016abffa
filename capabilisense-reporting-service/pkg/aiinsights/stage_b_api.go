package aiinsights

import (
	"context"
	"encoding/json"
	"log"
	"net/http"
	"time"

	"capabilisense-reporting-service/pkg/config"
	"capabilisense-reporting-service/pkg/dataextraction"
)

// StageBHandler handles Stage B API requests
type StageBHandler struct {
	stageBService *StageBService
}

// NewStageBHandler creates a new Stage B API handler
func NewStageBHandler(library *config.PromptsLibrary, repo dataextraction.Repository) *StageBHandler {
	return &StageBHandler{
		stageBService: NewStageBService(library, repo),
	}
}

// GenerateInsightsHandler handles the Stage B AI insight generation endpoint
func (h *StageBHandler) GenerateInsightsHandler(w http.ResponseWriter, r *http.Request) {
	// Set CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/json")

	// Handle preflight requests
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// Only allow POST requests
	if r.Method != "POST" {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed", "Only POST requests are supported")
		return
	}

	// Parse request body (Stage A output)
	var stageAData dataextraction.StageAOutput
	if err := json.NewDecoder(r.Body).Decode(&stageAData); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body", "Failed to parse Stage A data: "+err.Error())
		return
	}

	// Validate required fields
	if stageAData.ReportMetadata.ProjectID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Missing required field", "project_id is required in report_metadata")
		return
	}

	// Log the request
	log.Printf("Stage B API request: project_id=%s, run_id=%s", stageAData.ReportMetadata.ProjectID, stageAData.ReportMetadata.RunID)

	// Generate insights
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	startTime := time.Now()
	insights, err := h.stageBService.GenerateInsights(ctx, &stageAData)
	processingTime := time.Since(startTime)

	if err != nil {
		log.Printf("Stage B API error: %v (processing time: %v)", err, processingTime)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to generate insights", err.Error())
		return
	}

	// Log success
	log.Printf("Stage B API success: project_id=%s, processing_time=%v, tokens_used=%d",
		stageAData.ReportMetadata.ProjectID, processingTime, insights.GenerationMetadata.TotalTokensUsed)

	// Write successful response
	h.writeSuccessResponse(w, insights)
}

// CombinedReportHandler handles the combined Stage A + Stage B pipeline
func (h *StageBHandler) CombinedReportHandler(w http.ResponseWriter, r *http.Request) {
	// Set CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/json")

	// Handle preflight requests
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// Only allow GET requests
	if r.Method != "GET" {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed", "Only GET requests are supported")
		return
	}

	// This endpoint would integrate with Stage A API
	// For now, return a placeholder response
	response := map[string]interface{}{
		"message": "Combined Stage A + Stage B pipeline endpoint",
		"status":  "not_implemented",
		"note":    "Use Stage A API (/api/v1/report-data) followed by Stage B API (/api/v1/generate-insights) for now",
		"endpoints": map[string]string{
			"stage_a": "/api/v1/report-data?project_id=<project_id>&run_id=<optional_run_id>",
			"stage_b": "/api/v1/generate-insights (POST with Stage A output)",
		},
	}

	h.writeSuccessResponse(w, response)
}

// HealthCheckHandler handles health check requests for Stage B
func (h *StageBHandler) HealthCheckHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "GET" {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed", "Only GET requests are supported")
		return
	}

	response := map[string]interface{}{
		"status":    "healthy",
		"service":   "capabilisense-stage-b-api",
		"timestamp": time.Now(),
		"version":   "1.0.0",
		"ai_ready":  true,
	}

	h.writeSuccessResponse(w, response)
}

// writeErrorResponse writes a standardized error response
func (h *StageBHandler) writeErrorResponse(w http.ResponseWriter, statusCode int, error string, message string) {
	w.WriteHeader(statusCode)

	response := map[string]interface{}{
		"error":     error,
		"message":   message,
		"code":      statusCode,
		"timestamp": time.Now(),
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Failed to encode error response: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

// writeSuccessResponse writes a standardized success response
func (h *StageBHandler) writeSuccessResponse(w http.ResponseWriter, data interface{}) {
	w.WriteHeader(http.StatusOK)

	response := map[string]interface{}{
		"data":      data,
		"timestamp": time.Now(),
		"status":    "success",
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Failed to encode success response: %v", err)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Encoding error", "Failed to encode response")
	}
}

// SetupStageBRoutes sets up the HTTP routes for Stage B API
func SetupStageBRoutes(library *config.PromptsLibrary, repo dataextraction.Repository) *http.ServeMux {
	handler := NewStageBHandler(library, repo)
	mux := http.NewServeMux()

	// Stage B API routes
	mux.HandleFunc("/api/v1/generate-insights", handler.GenerateInsightsHandler)
	mux.HandleFunc("/api/v1/combined-report", handler.CombinedReportHandler)

	// Health check
	mux.HandleFunc("/health-b", handler.HealthCheckHandler)

	// Info route
	mux.HandleFunc("/stage-b", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		response := map[string]interface{}{
			"service":     "CapabiliSense Stage B API",
			"version":     "1.0.0",
			"description": "AI Insight Generation Service",
			"endpoints": map[string]interface{}{
				"generate_insights": map[string]interface{}{
					"url":         "/api/v1/generate-insights",
					"method":      "POST",
					"description": "Generate AI insights from Stage A data",
					"input":       "Stage A output JSON",
					"output":      "AI-generated insights and recommendations",
				},
				"combined_report": map[string]interface{}{
					"url":         "/api/v1/combined-report",
					"method":      "GET",
					"description": "Combined Stage A + Stage B pipeline (future)",
					"status":      "not_implemented",
				},
				"health_check": map[string]interface{}{
					"url":         "/health-b",
					"method":      "GET",
					"description": "Stage B service health check",
				},
			},
			"ai_capabilities": []string{
				"Organization name generation",
				"Executive summary creation",
				"Domain-specific insights",
				"AI spotlight analysis",
				"ETS solution recommendations",
			},
		}

		json.NewEncoder(w).Encode(response)
	})

	return mux
}
