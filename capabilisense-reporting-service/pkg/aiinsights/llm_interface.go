package aiinsights

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"capabilisense-reporting-service/pkg/config"
)

// LLMRequest represents a request to an LLM provider
type LLMRequest struct {
	PromptID    string                 `json:"prompt_id"`
	UserQuery   string                 `json:"user_query,omitempty"`
	ChatHistory []ChatMessage          `json:"chat_history,omitempty"`
	Tools       []Tool                 `json:"tools,omitempty"`
	Context     map[string]interface{} `json:"context,omitempty"`
	RequestID   string                 `json:"request_id"`
	Temperature *float64               `json:"temperature,omitempty"`
}

// ChatMessage represents a message in chat history
type ChatMessage struct {
	Role       string     `json:"role"` // "system", "user", "assistant", "tool"
	Content    string     `json:"content"`
	ToolCalls  []ToolCall `json:"tool_calls,omitempty"`
	ToolCallID string     `json:"tool_call_id,omitempty"`
	Name       string     `json:"name,omitempty"`
}

// ToolCall represents a tool call made by the assistant
type Tool<PERSON>all struct {
	ID       string   `json:"id"`
	Type     string   `json:"type"`
	Function Function `json:"function"`
}

// Function represents a function call
type Function struct {
	Name      string `json:"name"`
	Arguments string `json:"arguments"`
}

// Tool represents a tool definition
type Tool struct {
	Type     string                 `json:"type"`
	Function map[string]interface{} `json:"function"`
}

// LLMResponse represents a response from an LLM provider
type LLMResponse struct {
	RequestID      string                 `json:"request_id"`
	PromptID       string                 `json:"prompt_id"`
	Content        string                 `json:"content"`
	ToolCalls      []ToolCall             `json:"tool_calls,omitempty"`
	StructuredData map[string]interface{} `json:"structured_data,omitempty"`
	Provider       string                 `json:"provider"`
	Model          string                 `json:"model"`
	TokensUsed     int                    `json:"tokens_used,omitempty"`
	ProcessingTime time.Duration          `json:"processing_time"`
	Timestamp      time.Time              `json:"timestamp"`
	Error          string                 `json:"error,omitempty"`
	UsedFallback   bool                   `json:"used_fallback"`
}

// LLMInterface provides the main interface for LLM interactions
type LLMInterface struct {
	library    *config.PromptsLibrary
	httpClient *http.Client
	logger     *LLMLogger
}

// NewLLMInterface creates a new LLM interface
func NewLLMInterface(library *config.PromptsLibrary) *LLMInterface {
	return &LLMInterface{
		library: library,
		httpClient: &http.Client{
			Timeout: 120 * time.Second,
		},
		logger: NewLLMLogger(),
	}
}

// CallLLM makes a request to LLM providers with fallback support
func (llm *LLMInterface) CallLLM(ctx context.Context, request LLMRequest) (*LLMResponse, error) {
	startTime := time.Now()

	// Get prompt configuration
	promptConfig, err := llm.library.GetPrompt(request.PromptID)
	if err != nil {
		return nil, fmt.Errorf("failed to get prompt config: %w", err)
	}

	// Try each model alias in order
	var lastError error
	for i, alias := range promptConfig.ModelAliases {
		modelConfig, err := llm.library.GetModel(alias)
		if err != nil {
			lastError = fmt.Errorf("model %s not found: %w", alias, err)
			continue
		}

		response, err := llm.callProvider(ctx, request, promptConfig, modelConfig, alias)
		if err != nil {
			lastError = err
			// Log the error and try next provider
			fmt.Printf("Provider %s failed (attempt %d/%d): %v\n", alias, i+1, len(promptConfig.ModelAliases), err)
			continue
		}

		// Success - return the response
		response.ProcessingTime = time.Since(startTime)
		response.Timestamp = time.Now()
		return response, nil
	}

	// All providers failed - try fallback
	if promptConfig.FallbackFile != "" {
		fallbackContent, err := promptConfig.GetFallbackContent(request.PromptID)
		if err == nil {
			// Log fallback usage
			processingTime := time.Since(startTime)
			llm.logger.LogFallback(request.RequestID, request.PromptID, lastError.Error(), processingTime)

			return &LLMResponse{
				RequestID:      request.RequestID,
				PromptID:       request.PromptID,
				Content:        fallbackContent,
				Provider:       "fallback",
				Model:          "file",
				ProcessingTime: processingTime,
				Timestamp:      time.Now(),
				UsedFallback:   true,
			}, nil
		}
	}

	return nil, fmt.Errorf("all providers failed, last error: %w", lastError)
}

// callProvider makes a request to a specific provider
func (llm *LLMInterface) callProvider(ctx context.Context, request LLMRequest, promptConfig *config.PromptConfig, modelConfig *config.ModelConfig, alias string) (*LLMResponse, error) {
	// Get API key from environment
	apiKey := os.Getenv(modelConfig.APIKeyEnv)
	if apiKey == "" {
		return nil, fmt.Errorf("API key not found in environment variable %s", modelConfig.APIKeyEnv)
	}

	// Load system prompt
	systemPrompt, err := promptConfig.LoadSystemPrompt()
	if err != nil {
		return nil, fmt.Errorf("failed to load system prompt: %w", err)
	}

	// Load JSON schema if configured
	jsonSchema, err := promptConfig.LoadJSONSchema()
	if err != nil {
		return nil, fmt.Errorf("failed to load JSON schema: %w", err)
	}

	// Build messages
	messages := llm.buildMessages(systemPrompt, request)

	// Get temperature (use from request or prompt config)
	temperature := promptConfig.Temperature
	if request.Temperature != nil {
		temperature = *request.Temperature
	}

	// Build request payload based on provider
	payload, err := llm.buildProviderPayload(modelConfig, messages, temperature, request.Tools, jsonSchema)
	if err != nil {
		return nil, fmt.Errorf("failed to build payload: %w", err)
	}

	// Build HTTP request
	httpReq, err := llm.buildHTTPRequest(ctx, modelConfig, payload, apiKey)
	if err != nil {
		return nil, fmt.Errorf("failed to build HTTP request: %w", err)
	}

	// Extract headers for logging
	headers := make(map[string]string)
	for key, values := range httpReq.Header {
		if len(values) > 0 {
			// Don't log sensitive headers
			if key == "Authorization" || strings.Contains(strings.ToLower(key), "key") {
				headers[key] = "[REDACTED]"
			} else {
				headers[key] = values[0]
			}
		}
	}

	// Log request
	requestTime := time.Now()
	llm.logger.LogRequest(alias, modelConfig, request.RequestID, request.PromptID, payload, headers)

	// Make HTTP request
	resp, err := llm.httpClient.Do(httpReq)
	if err != nil {
		// Log error
		llm.logger.LogError(alias, modelConfig, request.RequestID, request.PromptID,
			fmt.Sprintf("HTTP request failed: %v", err), time.Since(requestTime))
		return nil, fmt.Errorf("HTTP request failed: %w", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		llm.logger.LogError(alias, modelConfig, request.RequestID, request.PromptID,
			fmt.Sprintf("Failed to read response body: %v", err), time.Since(requestTime))
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		// Log error response
		llm.logger.LogResponse(alias, modelConfig, request.RequestID, request.PromptID,
			requestTime, resp.StatusCode, nil, string(body), time.Since(requestTime), 0, false)
		return nil, fmt.Errorf("HTTP %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var respBody map[string]interface{}
	if err := json.Unmarshal(body, &respBody); err != nil {
		llm.logger.LogError(alias, modelConfig, request.RequestID, request.PromptID,
			fmt.Sprintf("Failed to decode response: %v", err), time.Since(requestTime))
		return nil, fmt.Errorf("failed to decode response: %w", err)
	}

	// Extract response content based on provider
	content, toolCalls, tokensUsed := llm.extractResponse(modelConfig, respBody)

	// Log successful response
	llm.logger.LogResponse(alias, modelConfig, request.RequestID, request.PromptID,
		requestTime, resp.StatusCode, respBody, "", time.Since(requestTime), tokensUsed, false)

	return &LLMResponse{
		RequestID:  request.RequestID,
		PromptID:   request.PromptID,
		Content:    content,
		ToolCalls:  toolCalls,
		Provider:   alias,
		Model:      modelConfig.ModelName,
		TokensUsed: tokensUsed,
	}, nil
}

// buildMessages constructs the messages array for the request
func (llm *LLMInterface) buildMessages(systemPrompt string, request LLMRequest) []ChatMessage {
	var messages []ChatMessage

	// Add system prompt if present
	if systemPrompt != "" {
		messages = append(messages, ChatMessage{
			Role:    "system",
			Content: systemPrompt,
		})
	}

	// Add chat history
	messages = append(messages, request.ChatHistory...)

	// Add user query if present
	if request.UserQuery != "" {
		messages = append(messages, ChatMessage{
			Role:    "user",
			Content: request.UserQuery,
		})
	}

	return messages
}

// buildProviderPayload builds the request payload for different providers
func (llm *LLMInterface) buildProviderPayload(modelConfig *config.ModelConfig, messages []ChatMessage, temperature float64, tools []Tool, jsonSchema map[string]interface{}) (map[string]interface{}, error) {
	providerURL := strings.ToLower(modelConfig.ProviderURL)

	// Google Gemini
	if strings.Contains(providerURL, "generativelanguage.googleapis.com") {
		return llm.buildGeminiPayload(modelConfig, messages, temperature, tools, jsonSchema)
	}

	// Anthropic
	if strings.Contains(providerURL, "anthropic.com") {
		return llm.buildAnthropicPayload(modelConfig, messages, temperature, tools, jsonSchema)
	}

	// OpenAI/Azure/OpenRouter/Mistral (OpenAI-compatible)
	return llm.buildOpenAIPayload(modelConfig, messages, temperature, tools, jsonSchema)
}

// buildOpenAIPayload builds payload for OpenAI-compatible APIs
func (llm *LLMInterface) buildOpenAIPayload(modelConfig *config.ModelConfig, messages []ChatMessage, temperature float64, tools []Tool, jsonSchema map[string]interface{}) (map[string]interface{}, error) {
	payload := map[string]interface{}{
		"model":       modelConfig.ModelName,
		"messages":    messages,
		"temperature": temperature,
	}

	if len(tools) > 0 {
		payload["tools"] = tools
	}

	// Add structured output for OpenAI if schema is provided
	if jsonSchema != nil {
		payload["response_format"] = map[string]interface{}{
			"type":        "json_schema",
			"json_schema": jsonSchema,
		}
	}

	return payload, nil
}

// buildGeminiPayload builds payload for Google Gemini API
func (llm *LLMInterface) buildGeminiPayload(modelConfig *config.ModelConfig, messages []ChatMessage, temperature float64, tools []Tool, jsonSchema map[string]interface{}) (map[string]interface{}, error) {
	// Convert messages to Gemini format
	var contents []map[string]interface{}

	for _, msg := range messages {
		if msg.Role == "system" {
			// Skip system messages for now - Gemini handles them differently
			continue
		}

		role := "user"
		if msg.Role == "assistant" {
			role = "model"
		}

		content := map[string]interface{}{
			"role": role,
			"parts": []map[string]interface{}{
				{"text": msg.Content},
			},
		}

		contents = append(contents, content)
	}

	payload := map[string]interface{}{
		"contents": contents,
		"generationConfig": map[string]interface{}{
			"temperature": temperature,
		},
	}

	// Add tools if present
	if len(tools) > 0 {
		var functionDeclarations []map[string]interface{}
		for _, tool := range tools {
			functionDeclarations = append(functionDeclarations, tool.Function)
		}
		payload["tools"] = []map[string]interface{}{
			{"functionDeclarations": functionDeclarations},
		}
	}

	// Add safety settings
	if safetyTags, exists := llm.library.ProviderSafetyTags[modelConfig.SafetyTagsProvider]; exists {
		var safetySettings []map[string]interface{}
		for _, tag := range safetyTags {
			safetySettings = append(safetySettings, map[string]interface{}{
				"category":  tag.Category,
				"threshold": tag.Threshold,
			})
		}
		payload["safetySettings"] = safetySettings
	}

	// Add structured output for Gemini if schema is provided
	if jsonSchema != nil {
		if genConfig, ok := payload["generationConfig"].(map[string]interface{}); ok {
			genConfig["responseMimeType"] = "application/json"
			genConfig["responseSchema"] = jsonSchema
		}
	}

	return payload, nil
}

// buildAnthropicPayload builds payload for Anthropic API
func (llm *LLMInterface) buildAnthropicPayload(modelConfig *config.ModelConfig, messages []ChatMessage, temperature float64, tools []Tool, _ map[string]interface{}) (map[string]interface{}, error) {
	// Separate system message from other messages
	var systemPrompt string
	var anthropicMessages []map[string]interface{}

	for _, msg := range messages {
		if msg.Role == "system" {
			systemPrompt = msg.Content
			continue
		}

		anthropicMessages = append(anthropicMessages, map[string]interface{}{
			"role":    msg.Role,
			"content": msg.Content,
		})
	}

	payload := map[string]interface{}{
		"model":       modelConfig.ModelName,
		"messages":    anthropicMessages,
		"temperature": temperature,
		"max_tokens":  4096, // Anthropic requires max_tokens
	}

	if systemPrompt != "" {
		payload["system"] = systemPrompt
	}

	if len(tools) > 0 {
		payload["tools"] = tools
	}

	// For structured output with Anthropic, we would typically use system prompt instructions
	// since Anthropic doesn't have native JSON schema support like OpenAI

	return payload, nil
}

// buildHTTPRequest builds the HTTP request for the provider
func (llm *LLMInterface) buildHTTPRequest(ctx context.Context, modelConfig *config.ModelConfig, payload map[string]interface{}, apiKey string) (*http.Request, error) {
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal payload: %w", err)
	}

	requestURL := modelConfig.ProviderURL

	// Handle query parameter authentication
	if modelConfig.AuthMode == "query_param" {
		u, err := url.Parse(requestURL)
		if err != nil {
			return nil, fmt.Errorf("failed to parse URL: %w", err)
		}

		q := u.Query()
		paramName := modelConfig.AuthQueryParamName
		if paramName == "" {
			paramName = "key" // Default for Google
		}
		q.Set(paramName, apiKey)
		u.RawQuery = q.Encode()
		requestURL = u.String()
	}

	req, err := http.NewRequestWithContext(ctx, "POST", requestURL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Content-Type", "application/json")

	// Handle different authentication modes
	switch modelConfig.AuthMode {
	case "bearer", "":
		req.Header.Set("Authorization", "Bearer "+apiKey)
	case "header":
		headerName := modelConfig.AuthHeaderName
		if headerName == "" {
			return nil, fmt.Errorf("auth_header_name is required for header auth mode")
		}
		req.Header.Set(headerName, apiKey)
	case "query_param":
		// Already handled above
	default:
		return nil, fmt.Errorf("unsupported auth mode: %s", modelConfig.AuthMode)
	}

	// Add provider-specific headers
	providerURL := strings.ToLower(modelConfig.ProviderURL)
	if strings.Contains(providerURL, "anthropic.com") {
		version := modelConfig.AnthropicVersion
		if version == "" {
			version = "2023-06-01"
		}
		req.Header.Set("anthropic-version", version)
	}

	return req, nil
}

// extractResponse extracts content and metadata from provider response
func (llm *LLMInterface) extractResponse(modelConfig *config.ModelConfig, respBody map[string]interface{}) (string, []ToolCall, int) {
	providerURL := strings.ToLower(modelConfig.ProviderURL)

	// Google Gemini
	if strings.Contains(providerURL, "generativelanguage.googleapis.com") {
		return llm.extractGeminiResponse(respBody)
	}

	// Anthropic
	if strings.Contains(providerURL, "anthropic.com") {
		return llm.extractAnthropicResponse(respBody)
	}

	// OpenAI/Azure/OpenRouter/Mistral (OpenAI-compatible)
	return llm.extractOpenAIResponse(respBody)
}

// extractOpenAIResponse extracts response from OpenAI-compatible APIs
func (llm *LLMInterface) extractOpenAIResponse(respBody map[string]interface{}) (string, []ToolCall, int) {
	var content string
	var toolCalls []ToolCall
	var tokensUsed int

	// Extract token usage
	if usage, ok := respBody["usage"].(map[string]interface{}); ok {
		if total, ok := usage["total_tokens"].(float64); ok {
			tokensUsed = int(total)
		}
	}

	// Extract choices
	if choices, ok := respBody["choices"].([]interface{}); ok && len(choices) > 0 {
		if choice, ok := choices[0].(map[string]interface{}); ok {
			if message, ok := choice["message"].(map[string]interface{}); ok {
				// Extract content
				if c, ok := message["content"].(string); ok {
					content = c
				}

				// Extract tool calls
				if tc, ok := message["tool_calls"].([]interface{}); ok {
					for _, call := range tc {
						if callMap, ok := call.(map[string]interface{}); ok {
							toolCall := ToolCall{
								Type: "function",
							}
							if id, ok := callMap["id"].(string); ok {
								toolCall.ID = id
							}
							if fn, ok := callMap["function"].(map[string]interface{}); ok {
								if name, ok := fn["name"].(string); ok {
									toolCall.Function.Name = name
								}
								if args, ok := fn["arguments"].(string); ok {
									toolCall.Function.Arguments = args
								}
							}
							toolCalls = append(toolCalls, toolCall)
						}
					}
				}
			}
		}
	}

	return content, toolCalls, tokensUsed
}

// extractGeminiResponse extracts response from Google Gemini API
func (llm *LLMInterface) extractGeminiResponse(respBody map[string]interface{}) (string, []ToolCall, int) {
	var content string
	var toolCalls []ToolCall
	var tokensUsed int

	// Extract token usage
	if usage, ok := respBody["usageMetadata"].(map[string]interface{}); ok {
		if total, ok := usage["totalTokenCount"].(float64); ok {
			tokensUsed = int(total)
		}
	}

	// Extract candidates
	if candidates, ok := respBody["candidates"].([]interface{}); ok && len(candidates) > 0 {
		if candidate, ok := candidates[0].(map[string]interface{}); ok {
			if contentBlock, ok := candidate["content"].(map[string]interface{}); ok {
				if parts, ok := contentBlock["parts"].([]interface{}); ok {
					for _, part := range parts {
						if partMap, ok := part.(map[string]interface{}); ok {
							// Extract text content
							if text, ok := partMap["text"].(string); ok {
								content += text
							}

							// Extract function calls
							if functionCall, ok := partMap["functionCall"].(map[string]interface{}); ok {
								toolCall := ToolCall{
									Type: "function",
								}
								if name, ok := functionCall["name"].(string); ok {
									toolCall.Function.Name = name
									// Generate ID for Gemini (it doesn't provide one)
									toolCall.ID = fmt.Sprintf("gemini_call_%s_%d", name, len(toolCalls))
								}
								if args, ok := functionCall["args"].(map[string]interface{}); ok {
									argsBytes, _ := json.Marshal(args)
									toolCall.Function.Arguments = string(argsBytes)
								}
								toolCalls = append(toolCalls, toolCall)
							}
						}
					}
				}
			}
		}
	}

	return content, toolCalls, tokensUsed
}

// extractAnthropicResponse extracts response from Anthropic API
func (llm *LLMInterface) extractAnthropicResponse(respBody map[string]interface{}) (string, []ToolCall, int) {
	var content string
	var toolCalls []ToolCall
	var tokensUsed int

	// Extract token usage
	if usage, ok := respBody["usage"].(map[string]interface{}); ok {
		if inputTokens, ok := usage["input_tokens"].(float64); ok {
			tokensUsed += int(inputTokens)
		}
		if outputTokens, ok := usage["output_tokens"].(float64); ok {
			tokensUsed += int(outputTokens)
		}
	}

	// Extract content
	if contentArray, ok := respBody["content"].([]interface{}); ok {
		for _, contentItem := range contentArray {
			if contentMap, ok := contentItem.(map[string]interface{}); ok {
				// Extract text content
				if contentType, ok := contentMap["type"].(string); ok && contentType == "text" {
					if text, ok := contentMap["text"].(string); ok {
						content += text
					}
				}

				// Extract tool use
				if contentType, ok := contentMap["type"].(string); ok && contentType == "tool_use" {
					toolCall := ToolCall{
						Type: "function",
					}
					if id, ok := contentMap["id"].(string); ok {
						toolCall.ID = id
					}
					if name, ok := contentMap["name"].(string); ok {
						toolCall.Function.Name = name
					}
					if input, ok := contentMap["input"].(map[string]interface{}); ok {
						inputBytes, _ := json.Marshal(input)
						toolCall.Function.Arguments = string(inputBytes)
					}
					toolCalls = append(toolCalls, toolCall)
				}
			}
		}
	}

	return content, toolCalls, tokensUsed
}
