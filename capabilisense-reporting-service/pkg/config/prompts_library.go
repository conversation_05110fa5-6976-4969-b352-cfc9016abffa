package config

import (
	"encoding/json"
	"fmt"
	"os"
)

// PromptConfig represents a single prompt configuration
type PromptConfig struct {
	ID          string            `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Provider    string            `json:"provider"`
	Model       string            `json:"model"`
	Temperature float64           `json:"temperature"`
	MaxTokens   int               `json:"max_tokens"`
	Prompt      string            `json:"prompt"`
	Fallback    *FallbackConfig   `json:"fallback,omitempty"`
	Parameters  map[string]string `json:"parameters,omitempty"`
}

// FallbackConfig represents fallback configuration for when LLM fails
type FallbackConfig struct {
	Type    string `json:"type"` // "static", "file", "template"
	Content string `json:"content"`
	Path    string `json:"path,omitempty"`
}

// PromptsLibrary holds all prompt configurations
type PromptsLibrary struct {
	Version string                  `json:"version"`
	Prompts map[string]PromptConfig `json:"prompts"`
}

// LoadPromptsLibrary loads the prompts library from a JSON file
func LoadPromptsLibrary(filepath string) (*PromptsLibrary, error) {
	data, err := os.ReadFile(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to read prompts library file %s: %w", filepath, err)
	}

	var library PromptsLibrary
	if err := json.Unmarshal(data, &library); err != nil {
		return nil, fmt.Errorf("failed to parse prompts library file %s: %w", filepath, err)
	}

	return &library, nil
}

// GetPrompt retrieves a prompt configuration by ID
func (pl *PromptsLibrary) GetPrompt(id string) (*PromptConfig, error) {
	prompt, exists := pl.Prompts[id]
	if !exists {
		return nil, fmt.Errorf("prompt with ID '%s' not found", id)
	}
	return &prompt, nil
}

// GetFallbackContent retrieves fallback content for a prompt
func (pc *PromptConfig) GetFallbackContent() (string, error) {
	if pc.Fallback == nil {
		return "", fmt.Errorf("no fallback configured for prompt %s", pc.ID)
	}

	switch pc.Fallback.Type {
	case "static":
		return pc.Fallback.Content, nil
	case "file":
		if pc.Fallback.Path == "" {
			return "", fmt.Errorf("fallback file path not specified for prompt %s", pc.ID)
		}
		data, err := os.ReadFile(pc.Fallback.Path)
		if err != nil {
			return "", fmt.Errorf("failed to read fallback file %s: %w", pc.Fallback.Path, err)
		}
		return string(data), nil
	default:
		return "", fmt.Errorf("unsupported fallback type '%s' for prompt %s", pc.Fallback.Type, pc.ID)
	}
}
