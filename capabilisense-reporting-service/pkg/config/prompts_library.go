package config

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
)

// PromptConfig represents a single prompt configuration
type PromptConfig struct {
	ModelAliases   []string `json:"model_aliases"`
	SystemPrompt   string   `json:"system_prompt"`
	InitialMessage string   `json:"initial_message,omitempty"`
	Temperature    float64  `json:"temperature"`
	Description    string   `json:"description,omitempty"`
	JSONSchema     string   `json:"json_schema,omitempty"`   // Path to JSON schema file for structured output
	FallbackFile   string   `json:"fallback_file,omitempty"` // Path to fallback response file
}

// ModelConfig represents configuration for a specific model/provider
type ModelConfig struct {
	ProviderURL        string `json:"provider_url"`
	ModelName          string `json:"model_name"`
	APIKeyEnv          string `json:"api_key_env"`
	Default            bool   `json:"default,omitempty"`
	SafetyTagsProvider string `json:"safety_tags_provider,omitempty"`
	AuthMode           string `json:"auth_mode,omitempty"`             // "bearer", "header", "query_param"
	AuthHeaderName     string `json:"auth_header_name,omitempty"`      // For header auth mode
	AuthQueryParamName string `json:"auth_query_param_name,omitempty"` // For query param auth mode
	ServiceType        string `json:"service_type,omitempty"`          // e.g., "extraction"
	AnthropicVersion   string `json:"anthropic_version,omitempty"`     // For Anthropic API
}

// SafetyTag represents safety configuration for providers
type SafetyTag struct {
	Category  string `json:"category"`
	Threshold string `json:"threshold"`
}

// PromptsLibrary holds all prompt configurations and model definitions
type PromptsLibrary struct {
	Prompts            map[string]PromptConfig `json:"prompts"`
	Models             map[string]ModelConfig  `json:"models"`
	ProviderSafetyTags map[string][]SafetyTag  `json:"provider_safety_tags"`
}

// LoadPromptsLibrary loads the prompts library from a JSON file
func LoadPromptsLibrary(filepath string) (*PromptsLibrary, error) {
	resolvedPath := findConfigFile(filepath)
	data, err := os.ReadFile(resolvedPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read prompts library file %s: %w", filepath, err)
	}

	var library PromptsLibrary
	if err := json.Unmarshal(data, &library); err != nil {
		return nil, fmt.Errorf("failed to parse prompts library file %s: %w", filepath, err)
	}

	return &library, nil
}

// GetPrompt retrieves a prompt configuration by ID
func (pl *PromptsLibrary) GetPrompt(id string) (*PromptConfig, error) {
	prompt, exists := pl.Prompts[id]
	if !exists {
		return nil, fmt.Errorf("prompt with ID '%s' not found", id)
	}
	return &prompt, nil
}

// GetModel retrieves a model configuration by alias
func (pl *PromptsLibrary) GetModel(alias string) (*ModelConfig, error) {
	model, exists := pl.Models[alias]
	if !exists {
		return nil, fmt.Errorf("model with alias '%s' not found", alias)
	}
	return &model, nil
}

// GetFallbackContent retrieves fallback content for a prompt
func (pc *PromptConfig) GetFallbackContent(promptID string) (string, error) {
	if pc.FallbackFile == "" {
		return "", fmt.Errorf("no fallback file configured for prompt %s", promptID)
	}

	filePath := findConfigFile(pc.FallbackFile)
	data, err := os.ReadFile(filePath)
	if err != nil {
		return "", fmt.Errorf("failed to read fallback file %s: %w", pc.FallbackFile, err)
	}
	return string(data), nil
}

// LoadSystemPrompt loads the system prompt from file if it's a file path
func (pc *PromptConfig) LoadSystemPrompt() (string, error) {
	if pc.SystemPrompt == "" {
		return "", nil
	}

	// Check if it's a file path (contains .txt extension)
	if len(pc.SystemPrompt) > 4 && pc.SystemPrompt[len(pc.SystemPrompt)-4:] == ".txt" {
		filePath := findConfigFile(pc.SystemPrompt)
		data, err := os.ReadFile(filePath)
		if err != nil {
			return "", fmt.Errorf("failed to read system prompt file %s: %w", pc.SystemPrompt, err)
		}
		return string(data), nil
	}

	// Return as-is if it's not a file path
	return pc.SystemPrompt, nil
}

// LoadInitialMessage loads the initial message from file if it's a file path
func (pc *PromptConfig) LoadInitialMessage() (string, error) {
	if pc.InitialMessage == "" {
		return "", nil
	}

	// Check if it's a file path (contains .txt extension)
	if len(pc.InitialMessage) > 4 && pc.InitialMessage[len(pc.InitialMessage)-4:] == ".txt" {
		filePath := findConfigFile(pc.InitialMessage)
		data, err := os.ReadFile(filePath)
		if err != nil {
			return "", fmt.Errorf("failed to read initial message file %s: %w", pc.InitialMessage, err)
		}
		return string(data), nil
	}

	// Return as-is if it's not a file path
	return pc.InitialMessage, nil
}

// LoadJSONSchema loads the JSON schema from file if configured
func (pc *PromptConfig) LoadJSONSchema() (map[string]interface{}, error) {
	if pc.JSONSchema == "" {
		return nil, nil
	}

	filePath := findConfigFile(pc.JSONSchema)
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read JSON schema file %s: %w", pc.JSONSchema, err)
	}

	var schema map[string]interface{}
	if err := json.Unmarshal(data, &schema); err != nil {
		return nil, fmt.Errorf("failed to parse JSON schema file %s: %w", pc.JSONSchema, err)
	}

	return schema, nil
}

// findConfigFile finds a configuration file by looking in the current directory and parent directories
func findConfigFile(relativePath string) string {
	// First try the path as-is
	if _, err := os.Stat(relativePath); err == nil {
		return relativePath
	}

	// Try looking in parent directories (up to 5 levels)
	for i := 0; i < 5; i++ {
		prefix := ""
		for j := 0; j < i; j++ {
			prefix = filepath.Join("..", prefix)
		}

		fullPath := filepath.Join(prefix, relativePath)
		if _, err := os.Stat(fullPath); err == nil {
			return fullPath
		}
	}

	// Return original path if not found (will cause appropriate error)
	return relativePath
}
