package config

import (
	"encoding/json"
	"fmt"
	"os"
)

// Config holds the application configuration
type Config struct {
	// Server configuration
	Port string `json:"port"`

	// File paths
	PromptsLibraryPath string `json:"prompts_library_path"`
	TemplatesPath      string `json:"templates_path"`
	OutputPath         string `json:"output_path"`
	DatabasePath       string `json:"database_path"`

	// PDF generation settings
	DefaultTheme       string `json:"default_theme"`
	DefaultOrientation string `json:"default_orientation"`
	DefaultPageSize    string `json:"default_page_size"`

	// Logging
	LogLevel string `json:"log_level"`
	LogFile  string `json:"log_file"`
}

// LoadConfig loads configuration from environment variables and defaults
func LoadConfig() (*Config, error) {
	config := &Config{
		Port:               getEnvOrDefault("PORT", "8081"),
		PromptsLibraryPath: getEnvOrDefault("PROMPTS_LIBRARY_PATH", "configs/prompts_library.json"),
		TemplatesPath:      getEnvOrDefault("TEMPLATES_PATH", "templates"),
		OutputPath:         getEnvOrDefault("OUTPUT_PATH", "output"),
		DatabasePath:       getEnvOrDefault("DATABASE_PATH", "assessment_backend.db"),
		DefaultTheme:       getEnvOrDefault("DEFAULT_THEME", "light"),
		DefaultOrientation: getEnvOrDefault("DEFAULT_ORIENTATION", "portrait"),
		DefaultPageSize:    getEnvOrDefault("DEFAULT_PAGE_SIZE", "A4"),
		LogLevel:           getEnvOrDefault("LOG_LEVEL", "info"),
		LogFile:            getEnvOrDefault("LOG_FILE", ""),
	}

	return config, nil
}

// LoadConfigFromFile loads configuration from a JSON file
func LoadConfigFromFile(filepath string) (*Config, error) {
	data, err := os.ReadFile(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file %s: %w", filepath, err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse config file %s: %w", filepath, err)
	}

	return &config, nil
}

// getEnvOrDefault returns the environment variable value or a default value
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
