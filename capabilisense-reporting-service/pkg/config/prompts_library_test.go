package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMain(m *testing.M) {
	// Load environment variables for testing
	LoadTestEnv()

	// Run tests
	code := m.Run()
	os.Exit(code)
}

func TestLoadPromptsLibrary(t *testing.T) {
	// Test loading the actual prompts library file
	// The findConfigFile function should resolve the path correctly
	library, err := LoadPromptsLibrary("configs/prompts_library.json")
	require.NoError(t, err)
	require.NotNil(t, library)

	// Test that required prompts exist
	assert.Contains(t, library.Prompts, "frontend_chat")
	assert.Contains(t, library.Prompts, "maturity_assessor")

	// Test that required models exist
	assert.Contains(t, library.Models, "azure")
	assert.Contains(t, library.Models, "gcp-gemini-2.0-flash-lite")

	// Test provider safety tags
	assert.Contains(t, library.ProviderSafetyTags, "google")
	assert.Contains(t, library.ProviderSafetyTags, "azure")
}

func TestLoadPromptsLibraryInvalidFile(t *testing.T) {
	// Test loading non-existent file
	_, err := LoadPromptsLibrary("nonexistent.json")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to read prompts library file")
}

func TestGetPrompt(t *testing.T) {
	library, err := LoadPromptsLibrary("configs/prompts_library.json")
	require.NoError(t, err)

	// Test getting existing prompt
	prompt, err := library.GetPrompt("frontend_chat")
	assert.NoError(t, err)
	assert.NotNil(t, prompt)
	assert.NotEmpty(t, prompt.ModelAliases)
	assert.Equal(t, 0.6, prompt.Temperature)

	// Test getting non-existent prompt
	_, err = library.GetPrompt("nonexistent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestGetModel(t *testing.T) {
	library, err := LoadPromptsLibrary("configs/prompts_library.json")
	require.NoError(t, err)

	// Test getting existing model
	model, err := library.GetModel("azure")
	assert.NoError(t, err)
	assert.NotNil(t, model)
	assert.NotEmpty(t, model.ProviderURL)
	assert.NotEmpty(t, model.ModelName)
	assert.NotEmpty(t, model.APIKeyEnv)

	// Test getting non-existent model
	_, err = library.GetModel("nonexistent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "not found")
}

func TestPromptConfigLoadSystemPrompt(t *testing.T) {
	library, err := LoadPromptsLibrary("configs/prompts_library.json")
	require.NoError(t, err)

	prompt, err := library.GetPrompt("frontend_chat")
	require.NoError(t, err)

	// Test loading system prompt from file
	systemPrompt, err := prompt.LoadSystemPrompt()
	assert.NoError(t, err)
	assert.NotEmpty(t, systemPrompt)
	assert.Contains(t, systemPrompt, "digital transformation")
}

func TestPromptConfigLoadJSONSchema(t *testing.T) {
	library, err := LoadPromptsLibrary("configs/prompts_library.json")
	require.NoError(t, err)

	prompt, err := library.GetPrompt("maturity_assessor")
	require.NoError(t, err)

	// Test loading JSON schema
	schema, err := prompt.LoadJSONSchema()
	assert.NoError(t, err)
	assert.NotNil(t, schema)
	assert.Contains(t, schema, "properties")

	// Check that the schema has the expected structure
	properties, ok := schema["properties"].(map[string]interface{})
	assert.True(t, ok, "properties should be a map")
	assert.Contains(t, properties, "overall_maturity_level")
}

func TestPromptConfigGetFallbackContent(t *testing.T) {
	library, err := LoadPromptsLibrary("configs/prompts_library.json")
	require.NoError(t, err)

	prompt, err := library.GetPrompt("frontend_chat")
	require.NoError(t, err)

	// Test loading fallback content
	fallback, err := prompt.GetFallbackContent("frontend_chat")
	assert.NoError(t, err)
	assert.NotEmpty(t, fallback)
	assert.Contains(t, fallback, "unable to process")
}

func TestPromptConfigNoFallback(t *testing.T) {
	// Create a prompt config without fallback
	prompt := &PromptConfig{
		ModelAliases: []string{"azure"},
		Temperature:  0.5,
	}

	// Test that it returns error when no fallback is configured
	_, err := prompt.GetFallbackContent("test")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "no fallback file configured")
}

func TestModelConfigAuthModes(t *testing.T) {
	library, err := LoadPromptsLibrary("configs/prompts_library.json")
	require.NoError(t, err)

	// Test Azure (header auth)
	azure, err := library.GetModel("azure")
	require.NoError(t, err)
	assert.Equal(t, "header", azure.AuthMode)
	assert.Equal(t, "api-key", azure.AuthHeaderName)

	// Test Google (query param auth)
	google, err := library.GetModel("gcp-gemini-2.0-flash-lite")
	require.NoError(t, err)
	assert.Equal(t, "query_param", google.AuthMode)
	assert.Equal(t, "key", google.AuthQueryParamName)

	// Test Anthropic (header auth)
	anthropic, err := library.GetModel("anthropic")
	require.NoError(t, err)
	assert.Equal(t, "header", anthropic.AuthMode)
	assert.Equal(t, "x-api-key", anthropic.AuthHeaderName)
}

func TestSafetyTags(t *testing.T) {
	library, err := LoadPromptsLibrary("configs/prompts_library.json")
	require.NoError(t, err)

	// Test Google safety tags
	googleTags, exists := library.ProviderSafetyTags["google"]
	assert.True(t, exists)
	assert.NotEmpty(t, googleTags)

	// Check that required safety categories are present
	categories := make(map[string]bool)
	for _, tag := range googleTags {
		categories[tag.Category] = true
	}
	assert.True(t, categories["HARM_CATEGORY_HARASSMENT"])
	assert.True(t, categories["HARM_CATEGORY_HATE_SPEECH"])

	// Test Azure safety tags (should be empty)
	azureTags, exists := library.ProviderSafetyTags["azure"]
	assert.True(t, exists)
	assert.Empty(t, azureTags)
}

func TestGetTestAPIKeys(t *testing.T) {
	keys := GetTestAPIKeys()

	// Test that function returns a map (even if empty)
	assert.NotNil(t, keys)

	// If any keys are available, they should be non-empty strings
	for name, value := range keys {
		assert.NotEmpty(t, name)
		assert.NotEmpty(t, value)
	}
}

func TestGetAvailableProviders(t *testing.T) {
	providers := GetAvailableProviders()

	// Test that function returns a slice (even if empty)
	assert.NotNil(t, providers)

	// Log available providers for debugging
	t.Logf("Available providers: %v", providers)

	// If any providers are available, they should be valid strings
	for _, provider := range providers {
		assert.NotEmpty(t, provider)
	}
}
