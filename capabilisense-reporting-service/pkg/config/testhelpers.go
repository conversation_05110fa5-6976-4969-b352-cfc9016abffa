package config

import (
	"os"
	"path/filepath"

	"github.com/joho/godotenv"
)

// LoadTestEnv loads environment variables from .env file for testing
func LoadTestEnv() error {
	// Look for .env file in project root
	envPath := ".env"
	
	// If not found, try going up directories to find project root
	for i := 0; i < 5; i++ {
		if _, err := os.Stat(envPath); err == nil {
			break
		}
		envPath = filepath.Join("..", envPath)
	}
	
	// Load .env file if it exists
	if _, err := os.Stat(envPath); err == nil {
		return godotenv.Load(envPath)
	}
	
	// Not an error if .env doesn't exist - environment variables might be set directly
	return nil
}

// GetTestAPIKeys returns a map of available API keys for testing
func GetTestAPIKeys() map[string]string {
	keys := map[string]string{
		"AZURE_OPENAI_KEY":   os.Getenv("AZURE_OPENAI_KEY"),
		"GCP_API_KEY":        os.Getenv("GCP_API_KEY"),
		"GOOGLE_API_KEY":     os.Getenv("GOOGLE_API_KEY"),
		"ANTHROPIC_API_KEY":  os.Getenv("ANTHROPIC_API_KEY"),
		"OPENROUTER_API_KEY": os.Getenv("OPENROUTER_API_KEY"),
		"MISTRAL_API_KEY":    os.Getenv("MISTRAL_API_KEY"),
	}
	
	// Filter out empty keys
	availableKeys := make(map[string]string)
	for name, value := range keys {
		if value != "" {
			availableKeys[name] = value
		}
	}
	
	return availableKeys
}

// HasAPIKey checks if a specific API key is available
func HasAPIKey(keyName string) bool {
	return os.Getenv(keyName) != ""
}

// GetAvailableProviders returns a list of providers that have API keys configured
func GetAvailableProviders() []string {
	var providers []string
	
	if HasAPIKey("AZURE_OPENAI_KEY") {
		providers = append(providers, "azure")
	}
	if HasAPIKey("GCP_API_KEY") || HasAPIKey("GOOGLE_API_KEY") {
		providers = append(providers, "gcp-gemini-2.0-flash-lite", "gcp-gemini-2.5-pro")
	}
	if HasAPIKey("ANTHROPIC_API_KEY") {
		providers = append(providers, "anthropic")
	}
	if HasAPIKey("OPENROUTER_API_KEY") {
		providers = append(providers, "openrouter-quasar", "openrouter-gemini-2.5-pro-free", "openrouter-gemini-2.0-flash")
	}
	if HasAPIKey("MISTRAL_API_KEY") {
		providers = append(providers, "mistral-doc-extractor", "mistral-text-extractor")
	}
	
	return providers
}
