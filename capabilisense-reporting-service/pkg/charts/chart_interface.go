package charts

import (
	"fmt"
	"image/color"
)

// ChartRenderer defines the common interface for all chart types
type ChartRenderer interface {
	Generate(data SpiderChartData, title, outputPath string) error
}

// SpiderChartAdapter adapts the existing SpiderChart to the ChartRenderer interface
type SpiderChartAdapter struct {
	chart *<PERSON><PERSON>hart
}

// Generate implements the ChartRenderer interface for SpiderChart
func (sca *SpiderChartAdapter) Generate(data SpiderChartData, title, outputPath string) error {
	// The SpiderChart uses the title from the data, so we update it
	data.ChartTitle = title
	return sca.chart.GenerateChart(data, outputPath)
}

// ChartStyle represents different visual styles
type ChartStyle string

const (
	StyleSimpleRays           ChartStyle = "simple_rays"
	StyleFancyRays            ChartStyle = "fancy_rays"
	StyleTexturedRays         ChartStyle = "textured_rays"
	StyleSVGRays              ChartStyle = "svg_rays"
	StyleSpider               ChartStyle = "spider"
	StyleAdaptiveRays         ChartStyle = "adaptive_rays"
	StyleAdaptiveTexturedRays ChartStyle = "adaptive_textured_rays"
)

// UnifiedChartConfig holds configuration for any chart type
type UnifiedChartConfig struct {
	// Common properties
	Width           int
	Height          int
	CenterX         int
	CenterY         int
	MaxRadius       int
	MinRadius       int
	BackgroundColor color.RGBA
	GridColor       color.RGBA
	TextColor       color.RGBA

	// Chart style selection
	Style ChartStyle

	// Style-specific configs (only one will be used)
	SpiderConfig   *SpiderChartConfig
	RaysConfig     *RaysChartConfig
	AdaptiveConfig *AdaptiveScalingConfig

	// Visual enhancement options
	UseAntiAliasing   bool
	UseGradients      bool
	UseTextures       bool
	UseShadows        bool
	QualityMultiplier int // For supersampling
}

// ChartFactory creates chart renderers based on style
type ChartFactory struct{}

// NewChartFactory creates a new chart factory
func NewChartFactory() *ChartFactory {
	return &ChartFactory{}
}

// CreateRenderer creates the appropriate chart renderer based on style
func (cf *ChartFactory) CreateRenderer(config UnifiedChartConfig) (ChartRenderer, error) {
	switch config.Style {
	case StyleSimpleRays:
		raysConfig := cf.convertToRaysConfig(config)
		return NewRaysChart(raysConfig), nil

	case StyleFancyRays:
		raysConfig := cf.convertToRaysConfig(config)
		return NewFancyRaysChart(raysConfig), nil

	case StyleTexturedRays:
		raysConfig := cf.convertToRaysConfig(config)
		return NewTexturedRaysChart(raysConfig), nil

	case StyleSVGRays:
		raysConfig := cf.convertToRaysConfig(config)
		return NewSVGRaysChart(raysConfig), nil

	case StyleAdaptiveRays:
		raysConfig := cf.convertToRaysConfig(config)
		adaptiveConfig := cf.convertToAdaptiveConfig(config)
		return NewAdaptiveRaysChart(raysConfig, adaptiveConfig), nil

	case StyleAdaptiveTexturedRays:
		raysConfig := cf.convertToRaysConfig(config)
		adaptiveConfig := cf.convertToAdaptiveConfig(config)
		return NewAdaptiveTexturedRaysChart(raysConfig, adaptiveConfig), nil

	case StyleSpider:
		spiderConfig := cf.convertToSpiderConfig(config)
		return &SpiderChartAdapter{chart: NewSpiderChart(spiderConfig)}, nil

	default:
		return nil, fmt.Errorf("unsupported chart style: %s", config.Style)
	}
}

// convertToRaysConfig converts unified config to rays-specific config
func (cf *ChartFactory) convertToRaysConfig(config UnifiedChartConfig) RaysChartConfig {
	// Use provided rays config or create from unified config
	if config.RaysConfig != nil {
		return *config.RaysConfig
	}

	// Create default rays config from unified settings
	return RaysChartConfig{
		Width:           config.Width,
		Height:          config.Height,
		CenterX:         config.CenterX,
		CenterY:         config.CenterY,
		MaxRadius:       config.MaxRadius,
		MinRadius:       config.MinRadius,
		BackgroundColor: config.BackgroundColor,
		GridColor:       config.GridColor,
		TextColor:       config.TextColor,
		RayColors:       getDefaultRayColors(),
		LineWidth:       2.0,
		ShowGrid:        true,
		ShowLabels:      true,
		ShowValues:      true,
		UseGradientRays: config.UseGradients,
		ModernStyle:     true,
		RayWidth:        25.0,
	}
}

// convertToSpiderConfig converts unified config to spider-specific config
func (cf *ChartFactory) convertToSpiderConfig(config UnifiedChartConfig) SpiderChartConfig {
	// Use provided spider config or create from unified config
	if config.SpiderConfig != nil {
		return *config.SpiderConfig
	}

	// Create default spider config from unified settings
	return SpiderChartConfig{
		Width:           config.Width,
		Height:          config.Height,
		CenterX:         config.CenterX,
		CenterY:         config.CenterY,
		MaxRadius:       config.MaxRadius,
		BackgroundColor: config.BackgroundColor,
		GridColor:       config.GridColor,
		TextColor:       config.TextColor,
		DataColor:       color.RGBA{0, 123, 191, 255},
		FillColor:       color.RGBA{0, 123, 191, 60},
		LineWidth:       2.0,
		ShowGrid:        true,
		ShowLabels:      true,
		ShowValues:      true,
	}
}

// getDefaultRayColors returns default color palette for rays
func getDefaultRayColors() []color.RGBA {
	return []color.RGBA{
		{138, 43, 226, 200}, // Blue Violet
		{75, 0, 130, 200},   // Indigo
		{255, 20, 147, 200}, // Deep Pink
		{255, 69, 0, 200},   // Red Orange
		{255, 140, 0, 200},  // Dark Orange
		{255, 215, 0, 200},  // Gold
		{50, 205, 50, 200},  // Lime Green
		{0, 191, 255, 200},  // Deep Sky Blue
	}
}

// convertToAdaptiveConfig converts unified config to adaptive scaling config
func (cf *ChartFactory) convertToAdaptiveConfig(config UnifiedChartConfig) AdaptiveScalingConfig {
	// Use provided adaptive config or create default
	if config.AdaptiveConfig != nil {
		return *config.AdaptiveConfig
	}

	// Create default adaptive config
	return DefaultAdaptiveScalingConfig()
}

// UnifiedChartGenerator provides a single interface for all chart types
type UnifiedChartGenerator struct {
	factory   *ChartFactory
	outputDir string
}

// NewUnifiedChartGenerator creates a new unified chart generator
func NewUnifiedChartGenerator(outputDir string) *UnifiedChartGenerator {
	return &UnifiedChartGenerator{
		factory:   NewChartFactory(),
		outputDir: outputDir,
	}
}

// GenerateChart generates any type of chart based on the unified config
func (ucg *UnifiedChartGenerator) GenerateChart(
	domains []DomainData,
	maxLevel int,
	title string,
	filename string,
	config UnifiedChartConfig,
) (string, error) {
	// Create chart data
	chartData := SpiderChartData{
		Domains:    domains,
		MaxLevel:   maxLevel,
		ChartTitle: title,
	}

	// Create appropriate renderer
	renderer, err := ucg.factory.CreateRenderer(config)
	if err != nil {
		return "", fmt.Errorf("failed to create chart renderer: %w", err)
	}

	// Generate output path
	outputPath := fmt.Sprintf("%s/%s", ucg.outputDir, filename)

	// Generate chart
	if err := renderer.Generate(chartData, title, outputPath); err != nil {
		return "", fmt.Errorf("failed to generate chart: %w", err)
	}

	return outputPath, nil
}

// Preset configurations for easy switching
func CreateUnifiedProfessionalConfig(style ChartStyle) UnifiedChartConfig {
	return UnifiedChartConfig{
		Width:             900,
		Height:            900,
		CenterX:           450,
		CenterY:           450,
		MaxRadius:         320,
		MinRadius:         50,
		BackgroundColor:   color.RGBA{255, 255, 255, 255},
		GridColor:         color.RGBA{180, 180, 180, 255},
		TextColor:         color.RGBA{33, 37, 41, 255},
		Style:             style,
		UseAntiAliasing:   true,
		UseGradients:      true,
		UseTextures:       false,
		UseShadows:        true,
		QualityMultiplier: 2,
	}
}

func CreateUnifiedModernConfig(style ChartStyle) UnifiedChartConfig {
	return UnifiedChartConfig{
		Width:             1000,
		Height:            1000,
		CenterX:           500,
		CenterY:           500,
		MaxRadius:         380,
		MinRadius:         60,
		BackgroundColor:   color.RGBA{255, 255, 255, 255},
		GridColor:         color.RGBA{200, 200, 200, 255},
		TextColor:         color.RGBA{33, 37, 41, 255},
		Style:             style,
		UseAntiAliasing:   true,
		UseGradients:      true,
		UseTextures:       true,
		UseShadows:        true,
		QualityMultiplier: 2,
	}
}

// Easy style switching functions
func SwitchToFancyRays(currentConfig UnifiedChartConfig) UnifiedChartConfig {
	currentConfig.Style = StyleFancyRays
	currentConfig.UseAntiAliasing = true
	currentConfig.QualityMultiplier = 2
	return currentConfig
}

func SwitchToSVGRays(currentConfig UnifiedChartConfig) UnifiedChartConfig {
	currentConfig.Style = StyleSVGRays
	currentConfig.UseGradients = true
	currentConfig.UseShadows = true
	return currentConfig
}

func SwitchToTexturedRays(currentConfig UnifiedChartConfig) UnifiedChartConfig {
	currentConfig.Style = StyleTexturedRays
	currentConfig.UseTextures = true
	return currentConfig
}

func SwitchToAdaptiveRays(currentConfig UnifiedChartConfig) UnifiedChartConfig {
	currentConfig.Style = StyleAdaptiveRays
	// Set up adaptive scaling with sensible defaults
	if currentConfig.AdaptiveConfig == nil {
		adaptiveConfig := DefaultAdaptiveScalingConfig()
		currentConfig.AdaptiveConfig = &adaptiveConfig
	}
	return currentConfig
}

func SwitchToAdaptiveTexturedRays(currentConfig UnifiedChartConfig) UnifiedChartConfig {
	currentConfig.Style = StyleAdaptiveTexturedRays
	// Set up adaptive scaling with enhanced settings for textured rays
	if currentConfig.AdaptiveConfig == nil {
		adaptiveConfig := DefaultAdaptiveScalingConfig()
		// Enhanced settings for textured rays
		adaptiveConfig.UnusedRangeIntensity = 0.8 // Stronger red indication
		adaptiveConfig.ShowUnusedRange = true
		adaptiveConfig.ShowZoomIndicator = true
		currentConfig.AdaptiveConfig = &adaptiveConfig
	}
	currentConfig.UseTextures = true
	return currentConfig
}
