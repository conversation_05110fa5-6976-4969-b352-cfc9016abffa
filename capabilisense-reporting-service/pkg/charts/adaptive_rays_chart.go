package charts

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"math"
	"os"
)

// AdaptiveRaysChart generates rays charts with adaptive scaling
type AdaptiveRays<PERSON>hart struct {
	config         RaysChartConfig
	adaptiveConfig AdaptiveScalingConfig
	fontRenderer   *EnhancedFontRenderer
}

// NewAdaptiveRays<PERSON><PERSON> creates a new adaptive rays chart generator
func NewAdaptiveRaysChart(config RaysChartConfig, adaptiveConfig AdaptiveScalingConfig) *AdaptiveRaysChart {
	return &AdaptiveRaysChart{
		config:         config,
		adaptiveConfig: adaptiveConfig,
		fontRenderer:   NewEnhancedFontRenderer(),
	}
}

// Generate creates an adaptive rays chart with intelligent scaling
func (arc *AdaptiveRaysChart) Generate(data SpiderChartData, title, outputPath string) error {
	// Calculate adaptive scaling
	scaling := CalculateAdaptiveScaling(data, arc.adaptiveConfig)

	// Create image
	img := image.NewRGBA(image.Rect(0, 0, arc.config.Width, arc.config.Height))

	// Fill background
	arc.fillBackground(img)

	// Draw title with zoom info
	if title != "" {
		arc.drawTitle(img, title, scaling)
	}

	// Draw adaptive grid
	if arc.config.ShowGrid {
		arc.drawAdaptiveGrid(img, data, scaling)
	}

	// Draw unused range indicator (red gradient)
	if scaling.IsZoomed && arc.adaptiveConfig.ShowUnusedRange {
		arc.drawUnusedRangeIndicator(img, scaling)
	}

	// Draw adaptive rays
	arc.drawAdaptiveRays(img, data, scaling)

	// Draw adaptive labels
	if arc.config.ShowLabels {
		arc.drawAdaptiveLabels(img, data, scaling)
	}

	// Draw zoom indicator
	if scaling.IsZoomed && arc.adaptiveConfig.ShowZoomIndicator {
		arc.drawZoomIndicator(img, scaling)
	}

	// Save image
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	if err := png.Encode(file, img); err != nil {
		return fmt.Errorf("failed to encode PNG: %w", err)
	}

	return nil
}

// fillBackground creates background with subtle gradient
func (arc *AdaptiveRaysChart) fillBackground(img *image.RGBA) {
	bounds := img.Bounds()
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			img.Set(x, y, arc.config.BackgroundColor)
		}
	}
}

// drawTitle draws title with optional zoom information
func (arc *AdaptiveRaysChart) drawTitle(img *image.RGBA, title string, scaling ScalingResult) {
	titleY := 30
	titleX := arc.config.Width/2 - len(title)*4
	arc.drawText(img, titleX, titleY, title, arc.config.TextColor)

	// Add zoom info if zoomed
	if scaling.IsZoomed && arc.adaptiveConfig.ShowZoomIndicator {
		zoomInfo := fmt.Sprintf("(Zoomed view: 0-%.1f of 0-%.0f scale)",
			scaling.ZoomedRange, scaling.UnusedRangeEnd)
		zoomX := arc.config.Width/2 - len(zoomInfo)*3
		arc.drawText(img, zoomX, titleY+20, zoomInfo, arc.adaptiveConfig.ZoomIndicatorColor)
	}
}

// drawAdaptiveGrid draws grid with both zoomed and original levels
func (arc *AdaptiveRaysChart) drawAdaptiveGrid(img *image.RGBA, data SpiderChartData, scaling ScalingResult) {
	centerX, centerY := arc.config.CenterX, arc.config.CenterY
	gridInfo := GenerateAdaptiveGrid(scaling, arc.adaptiveConfig, arc.config.MinRadius, arc.config.MaxRadius)

	// Draw original grid levels (faint) first
	for _, level := range gridInfo.OriginalLevels {
		arc.drawCircle(img, centerX, centerY, level.Radius, level.Color, level.Width)

		// Draw faint level label
		if arc.config.ShowValues {
			labelX := centerX + level.Radius + 5
			labelY := centerY + 5
			arc.drawText(img, labelX, labelY, level.Label, level.Color)
		}
	}

	// Draw zoomed grid levels (prominent)
	for _, level := range gridInfo.ZoomedLevels {
		arc.drawCircle(img, centerX, centerY, level.Radius, level.Color, level.Width)

		// Draw level label
		if arc.config.ShowValues {
			labelX := centerX + level.Radius + 5
			labelY := centerY + 5
			arc.drawText(img, labelX, labelY, level.Label, arc.config.TextColor)
		}
	}
}

// drawUnusedRangeIndicator draws red gradient for unused range
func (arc *AdaptiveRaysChart) drawUnusedRangeIndicator(img *image.RGBA, scaling ScalingResult) {
	centerX, centerY := arc.config.CenterX, arc.config.CenterY

	// Calculate where unused range starts in pixels
	usedRangeRadius := int(ApplyAdaptiveRadius(scaling.ZoomedRange, scaling, arc.config.MinRadius, arc.config.MaxRadius))

	// Draw concentric rings with red gradient
	numRings := (arc.config.MaxRadius - usedRangeRadius) / int(arc.adaptiveConfig.UnusedRangeWidth)
	if numRings < 1 {
		numRings = 1
	}

	for ring := 0; ring < numRings; ring++ {
		ringRadius := usedRangeRadius + ring*int(arc.adaptiveConfig.UnusedRangeWidth)
		if ringRadius > arc.config.MaxRadius {
			ringRadius = arc.config.MaxRadius
		}

		// Calculate opacity - stronger near the used range
		ringRatio := float64(ring) / float64(numRings)
		opacity := arc.adaptiveConfig.UnusedRangeIntensity * (1.0 - ringRatio*0.5)

		ringColor := color.RGBA{
			R: arc.adaptiveConfig.UnusedRangeColor.R,
			G: arc.adaptiveConfig.UnusedRangeColor.G,
			B: arc.adaptiveConfig.UnusedRangeColor.B,
			A: uint8(float64(arc.adaptiveConfig.UnusedRangeColor.A) * opacity),
		}

		// Draw ring
		arc.drawRing(img, centerX, centerY, ringRadius, ringColor, arc.adaptiveConfig.UnusedRangeWidth)
	}
}

// drawAdaptiveRays draws rays with adaptive scaling
func (arc *AdaptiveRaysChart) drawAdaptiveRays(img *image.RGBA, data SpiderChartData, scaling ScalingResult) {
	centerX, centerY := arc.config.CenterX, arc.config.CenterY
	numDomains := len(data.Domains)

	if numDomains == 0 {
		return
	}

	for i, domain := range data.Domains {
		// Calculate angle for this ray
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2

		// Calculate ray length using adaptive scaling
		rayLength := ApplyAdaptiveRadius(domain.Score, scaling, arc.config.MinRadius, arc.config.MaxRadius)

		// Get adaptive ray color
		baseColor := arc.getRayColor(i, domain.Score, data.MaxLevel)
		rayColor := GetAdaptiveRayColor(baseColor, domain.Score, scaling, arc.adaptiveConfig)

		// Draw ray
		arc.drawRay(img, centerX, centerY, angle, rayLength, rayColor, domain.Score, scaling)
	}
}

// drawRay draws a single adaptive ray
func (arc *AdaptiveRaysChart) drawRay(img *image.RGBA, centerX, centerY int, angle, length float64,
	rayColor color.RGBA, score float64, scaling ScalingResult) {

	// Calculate ray endpoints
	endX := centerX + int(length*math.Cos(angle))
	endY := centerY + int(length*math.Sin(angle))

	// Ray width
	rayWidthPixels := int(arc.config.RayWidth)

	// Draw ray as thick line
	arc.drawThickLine(img, centerX, centerY, endX, endY, rayColor, rayWidthPixels)

	// Draw end cap with adaptive styling
	if arc.config.ModernStyle {
		// For low scores in zoomed view, make end cap more prominent
		capRadius := rayWidthPixels / 2
		if scaling.IsZoomed && score < scaling.ZoomedRange*0.5 {
			capRadius = int(float64(capRadius) * 1.3) // Slightly larger for visibility
		}
		arc.drawCircle(img, endX, endY, capRadius, rayColor, 1.0)
	}
}

// drawAdaptiveLabels draws domain labels with adaptive information
func (arc *AdaptiveRaysChart) drawAdaptiveLabels(img *image.RGBA, data SpiderChartData, scaling ScalingResult) {
	centerX, centerY := arc.config.CenterX, arc.config.CenterY
	labelRadius := float64(arc.config.MaxRadius) + 40
	numDomains := len(data.Domains)

	for i, domain := range data.Domains {
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2

		// Calculate label position
		labelX := centerX + int(labelRadius*math.Cos(angle))
		labelY := centerY + int(labelRadius*math.Sin(angle))

		// Format label with adaptive information
		labelText := arc.formatAdaptiveLabel(domain, scaling)

		// Adjust text position based on angle
		textWidth := len(labelText) * 8
		if angle > math.Pi/4 && angle < 3*math.Pi/4 {
			labelX -= textWidth / 2
		} else if angle >= 3*math.Pi/4 || angle <= -3*math.Pi/4 {
			labelX -= textWidth
		} else if angle > -3*math.Pi/4 && angle < -math.Pi/4 {
			labelX -= textWidth / 2
		}

		// Color-code label based on score level
		labelColor := arc.config.TextColor
		if scaling.IsZoomed {
			// Use different colors to indicate score levels
			scoreRatio := domain.Score / scaling.UnusedRangeEnd
			if scoreRatio < 0.3 {
				labelColor = color.RGBA{220, 53, 69, 255} // Red for very low scores
			} else if scoreRatio < 0.6 {
				labelColor = color.RGBA{255, 193, 7, 255} // Yellow for medium-low scores
			}
		}

		arc.drawText(img, labelX, labelY, labelText, labelColor)
	}
}

// formatAdaptiveLabel formats labels with adaptive context
func (arc *AdaptiveRaysChart) formatAdaptiveLabel(domain DomainData, scaling ScalingResult) string {
	if !arc.adaptiveConfig.ShowActualRange {
		return domain.Name
	}

	if scaling.IsZoomed {
		// Show score with context about the full scale
		return fmt.Sprintf("%s\n%.1f/%.0f", domain.Name, domain.Score, scaling.UnusedRangeEnd)
	} else {
		// Standard label
		return fmt.Sprintf("%s (%.1f)", domain.Name, domain.Score)
	}
}

// drawZoomIndicator draws information about the zoom level
func (arc *AdaptiveRaysChart) drawZoomIndicator(img *image.RGBA, scaling ScalingResult) {
	// Draw zoom indicator in bottom right
	indicatorX := arc.config.Width - 200
	indicatorY := arc.config.Height - 40

	zoomText := fmt.Sprintf("Zoomed: 0-%.1f of 0-%.0f", scaling.ZoomedRange, scaling.UnusedRangeEnd)
	arc.drawText(img, indicatorX, indicatorY, zoomText, arc.adaptiveConfig.ZoomIndicatorColor)

	// Draw small visual indicator
	arc.drawZoomVisualIndicator(img, indicatorX-30, indicatorY-5, scaling)
}

// drawZoomVisualIndicator draws a small visual zoom indicator
func (arc *AdaptiveRaysChart) drawZoomVisualIndicator(img *image.RGBA, x, y int, scaling ScalingResult) {
	// Draw small rectangle showing zoom ratio
	totalWidth := 20
	usedWidth := int(float64(totalWidth) * (scaling.ZoomedRange / scaling.UnusedRangeEnd))

	// Used range (green)
	for i := 0; i < usedWidth; i++ {
		for j := 0; j < 8; j++ {
			img.Set(x+i, y+j, color.RGBA{40, 167, 69, 255}) // Green
		}
	}

	// Unused range (red)
	for i := usedWidth; i < totalWidth; i++ {
		for j := 0; j < 8; j++ {
			img.Set(x+i, y+j, arc.adaptiveConfig.UnusedRangeColor)
		}
	}
}

// Helper methods
func (arc *AdaptiveRaysChart) getRayColor(index int, score float64, maxLevel int) color.RGBA {
	if len(arc.config.RayColors) == 0 {
		return color.RGBA{100, 100, 100, 200}
	}
	return arc.config.RayColors[index%len(arc.config.RayColors)]
}

func (arc *AdaptiveRaysChart) drawText(img *image.RGBA, x, y int, text string, col color.RGBA) {
	// Use vector font renderer for clean, scalable text
	config := DefaultFontConfig()
	config.Color = col
	config.Size = FontSizeSmall
	arc.fontRenderer.DrawText(img, x, y, text, config)
}

func (arc *AdaptiveRaysChart) drawCircle(img *image.RGBA, centerX, centerY, radius int, col color.RGBA, lineWidth float64) {
	for angle := 0.0; angle < 2*math.Pi; angle += 0.01 {
		x := centerX + int(float64(radius)*math.Cos(angle))
		y := centerY + int(float64(radius)*math.Sin(angle))

		thickness := int(lineWidth)
		for dx := -thickness; dx <= thickness; dx++ {
			for dy := -thickness; dy <= thickness; dy++ {
				if dx*dx+dy*dy <= thickness*thickness {
					px, py := x+dx, y+dy
					if px >= 0 && py >= 0 && px < img.Bounds().Dx() && py < img.Bounds().Dy() {
						arc.setPixelBlended(img, px, py, col)
					}
				}
			}
		}
	}
}

func (arc *AdaptiveRaysChart) drawRing(img *image.RGBA, centerX, centerY, radius int, col color.RGBA, width float64) {
	innerRadius := radius - int(width/2)
	outerRadius := radius + int(width/2)

	for angle := 0.0; angle < 2*math.Pi; angle += 0.01 {
		for r := innerRadius; r <= outerRadius; r++ {
			x := centerX + int(float64(r)*math.Cos(angle))
			y := centerY + int(float64(r)*math.Sin(angle))

			if x >= 0 && y >= 0 && x < img.Bounds().Dx() && y < img.Bounds().Dy() {
				arc.setPixelBlended(img, x, y, col)
			}
		}
	}
}

func (arc *AdaptiveRaysChart) drawThickLine(img *image.RGBA, x1, y1, x2, y2 int, col color.RGBA, thickness int) {
	// Simple thick line implementation
	dx := float64(x2 - x1)
	dy := float64(y2 - y1)
	length := math.Sqrt(dx*dx + dy*dy)

	if length == 0 {
		return
	}

	dx /= length
	dy /= length

	perpX := -dy
	perpY := dx

	halfThickness := float64(thickness) / 2.0

	for t := 0.0; t <= length; t += 0.5 {
		centerX := float64(x1) + dx*t
		centerY := float64(y1) + dy*t

		for w := -halfThickness; w <= halfThickness; w += 0.5 {
			pixelX := int(centerX + perpX*w)
			pixelY := int(centerY + perpY*w)

			if pixelX >= 0 && pixelY >= 0 && pixelX < img.Bounds().Dx() && pixelY < img.Bounds().Dy() {
				img.Set(pixelX, pixelY, col)
			}
		}
	}
}

func (arc *AdaptiveRaysChart) setPixelBlended(img *image.RGBA, x, y int, newColor color.RGBA) {
	existing := img.RGBAAt(x, y)
	alpha := float64(newColor.A) / 255.0
	invAlpha := 1.0 - alpha

	blended := color.RGBA{
		R: uint8(float64(newColor.R)*alpha + float64(existing.R)*invAlpha),
		G: uint8(float64(newColor.G)*alpha + float64(existing.G)*invAlpha),
		B: uint8(float64(newColor.B)*alpha + float64(existing.B)*invAlpha),
		A: uint8(math.Max(float64(existing.A), float64(newColor.A))),
	}

	img.Set(x, y, blended)
}
