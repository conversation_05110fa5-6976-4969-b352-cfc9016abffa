package charts

import (
	"image"
	"image/color"
	"math"
	"strings"
	"unicode"

	"golang.org/x/image/font"
	"golang.org/x/image/font/gofont/gobold"
	"golang.org/x/image/font/gofont/goregular"
	"golang.org/x/image/font/opentype"
	"golang.org/x/image/math/fixed"
)

// FontSize represents different font sizes in points
type FontSize float64

const (
	FontSizeSmall  FontSize = 14.0
	FontSizeMedium FontSize = 18.0
	FontSizeLarge  FontSize = 24.0
	FontSizeXLarge FontSize = 32.0
	FontSizeTitle  FontSize = 40.0
)

// FontStyle represents different font styles
type FontStyle int

const (
	FontStyleRegular FontStyle = iota
	FontStyleBold
	FontStyleItalic
)

// VectorFontRenderer provides high-quality vector font rendering
type VectorFontRenderer struct {
	regularFace font.Face
	boldFace    font.Face
	dpi         float64
}

// NewVectorFontRenderer creates a new vector font renderer with scalable fonts
func NewVectorFontRenderer() *VectorFontRenderer {
	// Parse the built-in Go fonts (vector fonts)
	regularFont, err := opentype.Parse(goregular.TTF)
	if err != nil {
		panic("Failed to parse regular font: " + err.Error())
	}

	boldFont, err := opentype.Parse(gobold.TTF)
	if err != nil {
		panic("Failed to parse bold font: " + err.Error())
	}

	// Create font faces with default size and better antialiasing
	dpi := 72.0
	regularFace, err := opentype.NewFace(regularFont, &opentype.FaceOptions{
		Size:    float64(FontSizeMedium),
		DPI:     dpi,
		Hinting: font.HintingNone, // Better antialiasing
	})
	if err != nil {
		panic("Failed to create regular font face: " + err.Error())
	}

	boldFace, err := opentype.NewFace(boldFont, &opentype.FaceOptions{
		Size:    float64(FontSizeMedium),
		DPI:     dpi,
		Hinting: font.HintingNone, // Better antialiasing
	})
	if err != nil {
		panic("Failed to create bold font face: " + err.Error())
	}

	return &VectorFontRenderer{
		regularFace: regularFace,
		boldFace:    boldFace,
		dpi:         dpi,
	}
}

// EnhancedFontRenderer provides better font rendering with vector fonts
type EnhancedFontRenderer struct {
	vectorRenderer *VectorFontRenderer
}

// NewEnhancedFontRenderer creates a new enhanced font renderer with vector fonts
func NewEnhancedFontRenderer() *EnhancedFontRenderer {
	return &EnhancedFontRenderer{
		vectorRenderer: NewVectorFontRenderer(),
	}
}

// FontConfig holds font rendering configuration
type FontConfig struct {
	Size         FontSize
	Style        FontStyle
	Color        color.RGBA
	Outline      bool
	OutlineColor color.RGBA
	Shadow       bool
	ShadowColor  color.RGBA
	ShadowOffset int
	AntiAlias    bool
}

// DefaultFontConfig returns sensible font defaults with clean rendering
func DefaultFontConfig() FontConfig {
	return FontConfig{
		Size:         FontSizeMedium,
		Style:        FontStyleRegular,
		Color:        color.RGBA{33, 37, 41, 255}, // Dark gray
		Outline:      false,                       // Disable outline for cleaner look
		OutlineColor: color.RGBA{255, 255, 255, 200},
		Shadow:       false, // Disable shadow for cleaner look
		ShadowColor:  color.RGBA{0, 0, 0, 100},
		ShadowOffset: 1,
		AntiAlias:    true,
	}
}

// LargeFontConfig returns configuration for large, readable fonts
func LargeFontConfig() FontConfig {
	return FontConfig{
		Size:         FontSizeLarge,
		Style:        FontStyleRegular,
		Color:        color.RGBA{33, 37, 41, 255},
		Outline:      false, // Clean rendering without outline
		OutlineColor: color.RGBA{255, 255, 255, 220},
		Shadow:       false, // Clean rendering without shadow
		ShadowColor:  color.RGBA{0, 0, 0, 80},
		ShadowOffset: 2,
		AntiAlias:    true,
	}
}

// TitleFontConfig returns configuration for title text
func TitleFontConfig() FontConfig {
	return FontConfig{
		Size:         FontSizeTitle,
		Style:        FontStyleBold,
		Color:        color.RGBA{33, 37, 41, 255},
		Outline:      false, // Clean rendering for titles
		OutlineColor: color.RGBA{255, 255, 255, 240},
		Shadow:       false, // Clean rendering for titles
		ShadowColor:  color.RGBA{0, 0, 0, 100},
		ShadowOffset: 3,
		AntiAlias:    true,
	}
}

// DrawText renders text with enhanced styling and proper character handling using vector fonts
func (efr *EnhancedFontRenderer) DrawText(img *image.RGBA, x, y int, text string, config FontConfig) {
	// Clean and prepare text
	cleanText := efr.cleanText(text)

	// Create font face for the specific size
	fontFace := efr.createFontFace(config)
	defer fontFace.Close()

	// Draw shadow first if enabled
	if config.Shadow {
		shadowX := x + config.ShadowOffset
		shadowY := y + config.ShadowOffset
		efr.drawVectorText(img, shadowX, shadowY, cleanText, fontFace, config.ShadowColor)
	}

	// Draw outline if enabled
	if config.Outline {
		efr.drawTextOutline(img, x, y, cleanText, fontFace, config.OutlineColor)
	}

	// Draw main text
	efr.drawVectorText(img, x, y, cleanText, fontFace, config.Color)
}

// createFontFace creates a font face for the specified configuration
func (efr *EnhancedFontRenderer) createFontFace(config FontConfig) font.Face {
	// Parse the appropriate font based on style
	var fontData []byte
	switch config.Style {
	case FontStyleBold:
		fontData = gobold.TTF
	default:
		fontData = goregular.TTF
	}

	// Parse the font
	parsedFont, err := opentype.Parse(fontData)
	if err != nil {
		// Fallback to regular font
		parsedFont, _ = opentype.Parse(goregular.TTF)
	}

	// Create font face with the specified size and better antialiasing
	face, err := opentype.NewFace(parsedFont, &opentype.FaceOptions{
		Size:    float64(config.Size),
		DPI:     efr.vectorRenderer.dpi,
		Hinting: font.HintingNone, // Better for antialiasing
	})
	if err != nil {
		// Fallback to default face
		return efr.vectorRenderer.regularFace
	}

	return face
}

// drawVectorText draws text using vector fonts with high quality
func (efr *EnhancedFontRenderer) drawVectorText(img *image.RGBA, x, y int, text string, face font.Face, col color.RGBA) {
	point := fixed.Point26_6{
		X: fixed.Int26_6(x * 64),
		Y: fixed.Int26_6(y * 64),
	}

	d := &font.Drawer{
		Dst:  img,
		Src:  &image.Uniform{col},
		Face: face,
		Dot:  point,
	}
	d.DrawString(text)
}

// DrawMultilineText handles text with line breaks
func (efr *EnhancedFontRenderer) DrawMultilineText(img *image.RGBA, x, y int, text string, config FontConfig) {
	lines := strings.Split(text, "\n")
	lineHeight := efr.getLineHeight(config.Size)

	for i, line := range lines {
		lineY := y + i*lineHeight
		efr.DrawText(img, x, lineY, line, config)
	}
}

// MeasureText returns the accurate width and height of text using vector fonts
func (efr *EnhancedFontRenderer) MeasureText(text string, config FontConfig) (width, height int) {
	cleanText := efr.cleanText(text)

	// Create font face for accurate measurement
	fontFace := efr.createFontFace(config)
	defer fontFace.Close()

	lines := strings.Split(cleanText, "\n")
	maxWidth := 0

	// Measure each line accurately
	for _, line := range lines {
		lineWidth := font.MeasureString(fontFace, line)
		lineWidthPixels := int(lineWidth >> 6) // Convert from fixed.Int26_6 to pixels
		if lineWidthPixels > maxWidth {
			maxWidth = lineWidthPixels
		}
	}

	// Calculate total height based on font metrics
	metrics := fontFace.Metrics()
	lineHeight := int((metrics.Height + metrics.Descent) >> 6) // Convert to pixels
	totalHeight := len(lines) * lineHeight

	return maxWidth, totalHeight
}

// cleanText removes problematic characters and handles special cases
func (efr *EnhancedFontRenderer) cleanText(text string) string {
	var result strings.Builder

	for _, r := range text {
		switch {
		case r == '(' || r == ')':
			result.WriteRune(r)
		case r == '.' || r == ',':
			result.WriteRune(r)
		case r == '/' || r == '-':
			result.WriteRune(r)
		case r == ':' || r == ';':
			result.WriteRune(r)
		case r == ' ':
			result.WriteRune(r)
		case unicode.IsLetter(r) || unicode.IsDigit(r):
			result.WriteRune(r)
		case r == '\n':
			result.WriteRune(r)
		case r == '&':
			result.WriteString("&") // Keep ampersand as is
		default:
			// Replace ALL other characters with safe alternatives
			if r > 127 || r < 32 {
				// Skip non-printable and non-ASCII characters entirely
				continue
			} else {
				// Replace other problematic ASCII with space
				result.WriteRune(' ')
			}
		}
	}

	return result.String()
}

// getLineHeight returns line height for different font sizes (used for multiline text)
func (efr *EnhancedFontRenderer) getLineHeight(size FontSize) int {
	// For vector fonts, line height is typically 1.2-1.5 times the font size
	return int(float64(size) * 1.3)
}

// drawTextOutline draws text outline for better readability using vector fonts
func (efr *EnhancedFontRenderer) drawTextOutline(img *image.RGBA, x, y int, text string, face font.Face, outlineColor color.RGBA) {
	// Draw outline by rendering text in multiple positions
	offsets := []struct{ dx, dy int }{
		{-1, -1}, {0, -1}, {1, -1},
		{-1, 0}, {1, 0},
		{-1, 1}, {0, 1}, {1, 1},
	}

	for _, offset := range offsets {
		efr.drawVectorText(img, x+offset.dx, y+offset.dy, text, face, outlineColor)
	}
}

// blendPixel blends a pixel with alpha blending
func (efr *EnhancedFontRenderer) blendPixel(img *image.RGBA, x, y int, newColor color.RGBA) {
	if x < 0 || y < 0 || x >= img.Bounds().Dx() || y >= img.Bounds().Dy() {
		return
	}

	existing := img.RGBAAt(x, y)
	alpha := float64(newColor.A) / 255.0
	invAlpha := 1.0 - alpha

	blended := color.RGBA{
		R: uint8(float64(newColor.R)*alpha + float64(existing.R)*invAlpha),
		G: uint8(float64(newColor.G)*alpha + float64(existing.G)*invAlpha),
		B: uint8(float64(newColor.B)*alpha + float64(existing.B)*invAlpha),
		A: uint8(math.Max(float64(existing.A), float64(newColor.A))),
	}

	img.Set(x, y, blended)
}
