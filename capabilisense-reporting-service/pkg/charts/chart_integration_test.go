package charts

import (
	"os"
	"testing"
)

// TestChartIntegration tests the complete chart generation pipeline
func TestChartIntegration(t *testing.T) {
	// Create temporary directory for test charts
	tempDir, err := os.MkdirTemp("", "chart_test_*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Test data
	domains := []DomainData{
		{Name: "Strategy & Planning", Score: 1.2},
		{Name: "Operations Excellence", Score: 2.3},
		{Name: "Technology Innovation", Score: 0.8},
		{Name: "People & Culture", Score: 1.9},
		{Name: "Governance & Risk", Score: 1.5},
	}

	// Test all chart styles
	testCases := []struct {
		name   string
		style  ChartStyle
		config UnifiedChartConfig
	}{
		{
			name:   "Simple Rays",
			style:  StyleSimpleRays,
			config: CreateUnifiedProfessionalConfig(StyleSimpleRays),
		},
		{
			name:   "Textured Rays",
			style:  StyleTexturedRays,
			config: CreateUnifiedModernConfig(StyleTexturedRays),
		},
		{
			name:   "Adaptive Textured Rays (Ultimate)",
			style:  StyleAdaptiveTexturedRays,
			config: SwitchToAdaptiveTexturedRays(CreateUnifiedModernConfig(StyleAdaptiveTexturedRays)),
		},
		{
			name:   "Adaptive Rays",
			style:  StyleAdaptiveRays,
			config: SwitchToAdaptiveRays(CreateUnifiedProfessionalConfig(StyleAdaptiveRays)),
		},
	}

	generator := NewUnifiedChartGenerator(tempDir)

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			filename := string(tc.style) + "_test.png"
			title := "Test Assessment - " + tc.name

			chartPath, err := generator.GenerateChart(
				domains,
				5, // Max level
				title,
				filename,
				tc.config,
			)

			if err != nil {
				t.Errorf("Failed to generate %s chart: %v", tc.name, err)
				return
			}

			// Verify file exists
			if _, err := os.Stat(chartPath); os.IsNotExist(err) {
				t.Errorf("Chart file not created: %s", chartPath)
				return
			}

			// Verify file has content
			info, err := os.Stat(chartPath)
			if err != nil {
				t.Errorf("Failed to stat chart file: %v", err)
				return
			}

			if info.Size() == 0 {
				t.Errorf("Chart file is empty: %s", chartPath)
				return
			}

			t.Logf("✅ %s chart generated successfully: %s (size: %d bytes)",
				tc.name, chartPath, info.Size())
		})
	}
}

// TestAdaptiveScaling tests the adaptive scaling behavior specifically
func TestAdaptiveScaling(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "adaptive_test_*")
	if err != nil {
		t.Fatalf("Failed to create temp directory: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Test cases with different score ranges
	testCases := []struct {
		name        string
		domains     []DomainData
		expectZoom  bool
		description string
	}{
		{
			name: "Low Scores - Should Zoom",
			domains: []DomainData{
				{Name: "Domain A", Score: 0.5},
				{Name: "Domain B", Score: 1.2},
				{Name: "Domain C", Score: 0.8},
			},
			expectZoom:  true,
			description: "Max score 1.2 should trigger adaptive zoom",
		},
		{
			name: "High Scores - No Zoom",
			domains: []DomainData{
				{Name: "Domain A", Score: 3.5},
				{Name: "Domain B", Score: 4.2},
				{Name: "Domain C", Score: 3.8},
			},
			expectZoom:  false,
			description: "Max score 4.2 should not trigger zoom",
		},
		{
			name: "Mixed Scores - Should Zoom",
			domains: []DomainData{
				{Name: "Domain A", Score: 2.1},
				{Name: "Domain B", Score: 1.8},
				{Name: "Domain C", Score: 2.3},
			},
			expectZoom:  true,
			description: "Max score 2.3 should trigger adaptive zoom",
		},
	}

	generator := NewUnifiedChartGenerator(tempDir)

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			config := CreateUnifiedModernConfig(StyleAdaptiveTexturedRays)
			config = SwitchToAdaptiveTexturedRays(config)

			filename := "adaptive_test_" + tc.name + ".png"

			chartPath, err := generator.GenerateChart(
				tc.domains,
				5,
				"Adaptive Scaling Test - "+tc.name,
				filename,
				config,
			)

			if err != nil {
				t.Errorf("Failed to generate adaptive chart: %v", err)
				return
			}

			// Verify file exists and has content
			info, err := os.Stat(chartPath)
			if err != nil {
				t.Errorf("Chart file error: %v", err)
				return
			}

			if info.Size() == 0 {
				t.Errorf("Chart file is empty")
				return
			}

			t.Logf("✅ %s: %s (size: %d bytes)", tc.description, chartPath, info.Size())
		})
	}
}

// TestVectorFonts tests the enhanced font rendering
func TestVectorFonts(t *testing.T) {
	// Test font renderer creation
	renderer := NewEnhancedFontRenderer()
	if renderer == nil {
		t.Fatal("Failed to create enhanced font renderer")
	}

	// Test font configurations
	configs := []FontConfig{
		DefaultFontConfig(),
		LargeFontConfig(),
	}

	for i, config := range configs {
		t.Run("FontConfig"+string(rune('A'+i)), func(t *testing.T) {
			if config.Size == 0 {
				t.Error("Font size should not be zero")
			}
			if config.AntiAlias != true {
				t.Error("AntiAlias should be enabled")
			}
			t.Logf("✅ Font config valid: Size=%v, Style=%v", config.Size, config.Style)
		})
	}
}
