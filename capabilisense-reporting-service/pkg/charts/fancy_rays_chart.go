package charts

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"math"
	"os"
)

// FancyRaysChart generates sophisticated rays charts with visual effects
type Fancy<PERSON>ays<PERSON>hart struct {
	config RaysChartConfig
}

// NewFancyRays<PERSON>hart creates a new fancy rays chart generator
func NewFancyRaysChart(config RaysChartConfig) *FancyRaysChart {
	return &FancyRaysChart{config: config}
}

// Generate creates a fancy rays chart with advanced visual effects
func (frc *FancyRaysChart) Generate(data SpiderChartData, title, outputPath string) error {
	// Create high-resolution image for better quality
	scale := 2 // 2x supersampling for anti-aliasing
	img := image.NewRGBA(image.Rect(0, 0, frc.config.Width*scale, frc.config.Height*scale))

	// Fill background with gradient
	frc.drawGradientBackground(img, scale)

	// Draw fancy rays with multiple visual effects
	frc.drawFancyRays(img, data, scale)

	// Draw enhanced grid
	if frc.config.ShowGrid {
		frc.drawEnhancedGrid(img, data, scale)
	}

	// Downscale for final output (anti-aliasing effect)
	finalImg := frc.downscale(img, scale)

	// Save image
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	if err := png.Encode(file, finalImg); err != nil {
		return fmt.Errorf("failed to encode PNG: %w", err)
	}

	return nil
}

// drawGradientBackground creates a subtle radial gradient background
func (frc *FancyRaysChart) drawGradientBackground(img *image.RGBA, scale int) {
	centerX := frc.config.CenterX * scale
	centerY := frc.config.CenterY * scale
	maxRadius := float64(frc.config.MaxRadius * scale)

	bounds := img.Bounds()
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			// Calculate distance from center
			dx := float64(x - centerX)
			dy := float64(y - centerY)
			distance := math.Sqrt(dx*dx + dy*dy)

			// Create subtle gradient
			ratio := math.Min(distance/maxRadius, 1.0)

			// Base background color
			baseR := float64(frc.config.BackgroundColor.R)
			baseG := float64(frc.config.BackgroundColor.G)
			baseB := float64(frc.config.BackgroundColor.B)

			// Slightly darker towards edges
			r := uint8(baseR * (1.0 - ratio*0.1))
			g := uint8(baseG * (1.0 - ratio*0.1))
			b := uint8(baseB * (1.0 - ratio*0.1))

			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}
}

// drawFancyRays creates sophisticated rays with multiple visual effects
func (frc *FancyRaysChart) drawFancyRays(img *image.RGBA, data SpiderChartData, scale int) {
	centerX := float64(frc.config.CenterX * scale)
	centerY := float64(frc.config.CenterY * scale)
	numDomains := len(data.Domains)

	if numDomains == 0 {
		return
	}

	for i, domain := range data.Domains {
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2
		rayLength := frc.calculateRadius(domain.Score, data.MaxLevel, data) * float64(scale)
		rayColor := frc.getRayColor(i, domain.Score, data.MaxLevel)

		// Draw ray with multiple effects
		frc.drawSegmentedRay(img, centerX, centerY, angle, rayLength, rayColor, domain.Score, data.MaxLevel, scale)
	}
}

// drawSegmentedRay creates a ray with expanding segments and visual effects
func (frc *FancyRaysChart) drawSegmentedRay(img *image.RGBA, centerX, centerY, angle, length float64,
	rayColor color.RGBA, score float64, maxLevel int, scale int) {

	// Number of segments based on score
	numSegments := int(math.Ceil(score))
	if numSegments > maxLevel {
		numSegments = maxLevel
	}

	segmentLength := length / float64(maxLevel)
	baseWidth := frc.config.RayWidth * float64(scale)

	for segment := 0; segment < numSegments; segment++ {
		// Calculate segment properties
		segmentStart := float64(segment) * segmentLength
		segmentEnd := float64(segment+1) * segmentLength

		// Expanding width
		widthMultiplier := 1.0 + float64(segment)*0.4
		segmentWidth := baseWidth * widthMultiplier

		// Fading opacity
		opacity := 0.9 - float64(segment)*0.15
		if opacity < 0.3 {
			opacity = 0.3
		}

		// Color intensity based on segment
		intensity := 1.0 - float64(segment)*0.1
		segmentColor := color.RGBA{
			R: uint8(float64(rayColor.R) * intensity),
			G: uint8(float64(rayColor.G) * intensity),
			B: uint8(float64(rayColor.B) * intensity),
			A: uint8(255 * opacity),
		}

		// Draw segment with gradient effect
		frc.drawGradientSegment(img, centerX, centerY, angle, segmentStart, segmentEnd, segmentWidth, segmentColor)

		// Add segment separator (small bright line)
		if segment < numSegments-1 {
			separatorPos := segmentEnd + 2
			sepX := centerX + separatorPos*math.Cos(angle)
			sepY := centerY + separatorPos*math.Sin(angle)
			frc.drawGlowPoint(img, sepX, sepY, 3*float64(scale), color.RGBA{255, 255, 255, 180})
		}
	}

	// Add end cap with glow effect
	if numSegments > 0 {
		endX := centerX + length*math.Cos(angle)
		endY := centerY + length*math.Sin(angle)
		capRadius := baseWidth * 0.8

		// Bright end cap
		brightColor := color.RGBA{
			R: uint8(math.Min(255, float64(rayColor.R)*1.3)),
			G: uint8(math.Min(255, float64(rayColor.G)*1.3)),
			B: uint8(math.Min(255, float64(rayColor.B)*1.3)),
			A: 255,
		}
		frc.drawGlowPoint(img, endX, endY, capRadius, brightColor)
	}
}

// drawGradientSegment draws a segment with radial gradient effect
func (frc *FancyRaysChart) drawGradientSegment(img *image.RGBA, centerX, centerY, angle, start, end, width float64, col color.RGBA) {
	// Calculate segment corners
	startX := centerX + start*math.Cos(angle)
	startY := centerY + start*math.Sin(angle)
	endX := centerX + end*math.Cos(angle)
	endY := centerY + end*math.Sin(angle)

	// Perpendicular direction for width
	perpX := -math.Sin(angle)
	perpY := math.Cos(angle)

	// Draw filled polygon with gradient effect
	steps := int(math.Max(width, end-start))
	for step := 0; step < steps; step++ {
		t := float64(step) / float64(steps)

		// Interpolate along segment
		x := startX + t*(endX-startX)
		y := startY + t*(endY-startY)

		// Draw cross-section with gradient
		for w := -width / 2; w <= width/2; w += 0.5 {
			px := x + w*perpX
			py := y + w*perpY

			// Distance from center of ray for gradient effect
			distFromCenter := math.Abs(w) / (width / 2)
			alpha := float64(col.A) * (1.0 - distFromCenter*0.7)

			gradientColor := color.RGBA{
				R: col.R,
				G: col.G,
				B: col.B,
				A: uint8(alpha),
			}

			frc.setPixelBlended(img, int(px), int(py), gradientColor)
		}
	}
}

// drawGlowPoint draws a point with glow effect
func (frc *FancyRaysChart) drawGlowPoint(img *image.RGBA, x, y, radius float64, col color.RGBA) {
	for dy := -radius; dy <= radius; dy++ {
		for dx := -radius; dx <= radius; dx++ {
			distance := math.Sqrt(dx*dx + dy*dy)
			if distance <= radius {
				// Glow intensity based on distance
				intensity := 1.0 - (distance / radius)
				intensity = math.Pow(intensity, 0.5) // Softer falloff

				glowColor := color.RGBA{
					R: col.R,
					G: col.G,
					B: col.B,
					A: uint8(float64(col.A) * intensity),
				}

				px := int(x + dx)
				py := int(y + dy)
				frc.setPixelBlended(img, px, py, glowColor)
			}
		}
	}
}

// setPixelBlended sets a pixel with alpha blending
func (frc *FancyRaysChart) setPixelBlended(img *image.RGBA, x, y int, newColor color.RGBA) {
	bounds := img.Bounds()
	if x < bounds.Min.X || x >= bounds.Max.X || y < bounds.Min.Y || y >= bounds.Max.Y {
		return
	}

	// Get existing color
	existing := img.RGBAAt(x, y)

	// Alpha blend
	alpha := float64(newColor.A) / 255.0
	invAlpha := 1.0 - alpha

	blended := color.RGBA{
		R: uint8(float64(newColor.R)*alpha + float64(existing.R)*invAlpha),
		G: uint8(float64(newColor.G)*alpha + float64(existing.G)*invAlpha),
		B: uint8(float64(newColor.B)*alpha + float64(existing.B)*invAlpha),
		A: uint8(math.Max(float64(existing.A), float64(newColor.A))),
	}

	img.Set(x, y, blended)
}

// downscale reduces image size for anti-aliasing effect
func (frc *FancyRaysChart) downscale(img *image.RGBA, scale int) *image.RGBA {
	bounds := img.Bounds()
	newWidth := bounds.Dx() / scale
	newHeight := bounds.Dy() / scale

	result := image.NewRGBA(image.Rect(0, 0, newWidth, newHeight))

	for y := 0; y < newHeight; y++ {
		for x := 0; x < newWidth; x++ {
			// Sample multiple pixels and average
			var r, g, b, a float64
			samples := 0

			for sy := 0; sy < scale; sy++ {
				for sx := 0; sx < scale; sx++ {
					srcX := x*scale + sx
					srcY := y*scale + sy
					if srcX < bounds.Max.X && srcY < bounds.Max.Y {
						pixel := img.RGBAAt(srcX, srcY)
						r += float64(pixel.R)
						g += float64(pixel.G)
						b += float64(pixel.B)
						a += float64(pixel.A)
						samples++
					}
				}
			}

			if samples > 0 {
				result.Set(x, y, color.RGBA{
					R: uint8(r / float64(samples)),
					G: uint8(g / float64(samples)),
					B: uint8(b / float64(samples)),
					A: uint8(a / float64(samples)),
				})
			}
		}
	}

	return result
}

// Helper methods (same as original implementation)
func (frc *FancyRaysChart) calculateRadius(score float64, maxLevel int, data SpiderChartData) float64 {
	ratio := score / float64(maxLevel)
	return float64(frc.config.MinRadius) + ratio*float64(frc.config.MaxRadius-frc.config.MinRadius)
}

func (frc *FancyRaysChart) getRayColor(index int, score float64, maxLevel int) color.RGBA {
	if len(frc.config.RayColors) == 0 {
		return color.RGBA{100, 100, 100, 200}
	}
	return frc.config.RayColors[index%len(frc.config.RayColors)]
}

func (frc *FancyRaysChart) drawEnhancedGrid(img *image.RGBA, data SpiderChartData, scale int) {
	// Enhanced grid implementation would go here
}
