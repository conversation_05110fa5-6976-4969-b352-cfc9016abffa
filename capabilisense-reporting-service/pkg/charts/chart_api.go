package charts

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"time"

	"capabilisense-reporting-service/pkg/dataextraction"
)

// ChartAPIHandler handles HTTP requests for chart generation
type ChartAPIHandler struct {
	generator *ChartGenerator
	outputDir string
	baseURL   string // Base URL for serving generated charts
}

// NewChartAPIHandler creates a new chart API handler
func NewChartAPIHandler(outputDir, baseURL string) *ChartAPIHandler {
	// Ensure output directory exists
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Printf("Warning: Failed to create chart output directory: %v", err)
	}

	return &ChartAPIHandler{
		generator: NewChartGenerator(outputDir),
		outputDir: outputDir,
		baseURL:   baseURL,
	}
}

// ChartGenerationRequest represents a request to generate a chart
type ChartGenerationRequest struct {
	StageAData   *dataextraction.StageAOutput `json:"stage_a_data,omitempty"`
	CustomData   *SpiderChartData             `json:"custom_data,omitempty"`
	SpiderConfig *SpiderChartConfig           `json:"spider_config,omitempty"`
	RaysConfig   *RaysChartConfig             `json:"rays_config,omitempty"`
	Filename     string                       `json:"filename,omitempty"`
	ChartType    string                       `json:"chart_type"`              // "spider", "rays"
	ConfigPreset string                       `json:"config_preset,omitempty"` // "default", "professional", "high_contrast", "modern"
}

// ChartGenerationResponse represents the response from chart generation
type ChartGenerationResponse struct {
	Success      bool                   `json:"success"`
	ChartURL     string                 `json:"chart_url,omitempty"`
	LocalPath    string                 `json:"local_path,omitempty"`
	ChartData    SpiderChartData        `json:"chart_data,omitempty"`
	SpiderConfig *SpiderChartConfig     `json:"spider_config,omitempty"`
	RaysConfig   *RaysChartConfig       `json:"rays_config,omitempty"`
	Error        string                 `json:"error,omitempty"`
	GeneratedAt  time.Time              `json:"generated_at"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// GenerateChartHandler handles POST requests to generate charts
func (h *ChartAPIHandler) GenerateChartHandler(w http.ResponseWriter, r *http.Request) {
	// Set CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/json")

	// Handle preflight requests
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// Only allow POST requests
	if r.Method != "POST" {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// Parse request body
	var req ChartGenerationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request body: "+err.Error())
		return
	}

	// Validate request
	if err := h.validateRequest(&req); err != nil {
		h.writeErrorResponse(w, http.StatusBadRequest, "Invalid request: "+err.Error())
		return
	}

	// Generate chart
	response, err := h.generateChart(&req)
	if err != nil {
		h.writeErrorResponse(w, http.StatusInternalServerError, "Chart generation failed: "+err.Error())
		return
	}

	// Write successful response
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(response)
}

// generateChart generates a chart based on the request
func (h *ChartAPIHandler) generateChart(req *ChartGenerationRequest) (*ChartGenerationResponse, error) {
	startTime := time.Now()

	// Generate filename if not provided
	filename := req.Filename
	if filename == "" {
		filename = fmt.Sprintf("%s_chart_%d.png", req.ChartType, time.Now().Unix())
	}
	if filepath.Ext(filename) == "" {
		filename += ".png"
	}

	var chartData SpiderChartData
	var outputPath string
	var err error
	var spiderConfig *SpiderChartConfig
	var raysConfig *RaysChartConfig

	// Convert data to chart format
	if req.StageAData != nil {
		chartData = h.generator.convertToSpiderChartData(req.StageAData)
	} else if req.CustomData != nil {
		chartData = *req.CustomData
	} else {
		return nil, fmt.Errorf("no chart data provided")
	}

	// Generate chart based on type
	switch req.ChartType {
	case "rays":
		// Get rays configuration
		raysConfig = h.getRaysConfig(req)

		if req.StageAData != nil {
			outputPath, err = h.generator.GenerateRaysChartFromAssessment(req.StageAData, filename)
		} else {
			outputPath, err = h.generator.GenerateCustomRaysChart(
				chartData.Domains,
				chartData.MaxLevel,
				chartData.ChartTitle,
				filename,
				raysConfig,
			)
		}
	case "spider":
		fallthrough
	default:
		// Get spider configuration
		spiderConfig = h.getSpiderConfig(req)

		if req.StageAData != nil {
			outputPath, err = h.generator.GenerateSpiderChartFromAssessment(req.StageAData, filename)
		} else {
			outputPath, err = h.generator.GenerateCustomSpiderChart(
				chartData.Domains,
				chartData.MaxLevel,
				chartData.ChartTitle,
				filename,
				spiderConfig,
			)
		}
	}

	if err != nil {
		return nil, err
	}

	// Generate chart URL
	chartURL := h.generateChartURL(filename)

	// Create response
	response := &ChartGenerationResponse{
		Success:      true,
		ChartURL:     chartURL,
		LocalPath:    outputPath,
		ChartData:    chartData,
		SpiderConfig: spiderConfig,
		RaysConfig:   raysConfig,
		GeneratedAt:  startTime,
		Metadata: map[string]interface{}{
			"generation_time_ms": time.Since(startTime).Milliseconds(),
			"chart_type":         req.ChartType,
			"config_preset":      req.ConfigPreset,
			"domains_count":      len(chartData.Domains),
			"max_level":          chartData.MaxLevel,
		},
	}

	log.Printf("Chart generated successfully: %s (took %v)", filename, time.Since(startTime))
	return response, nil
}

// validateRequest validates the chart generation request
func (h *ChartAPIHandler) validateRequest(req *ChartGenerationRequest) error {
	if req.ChartType == "" {
		req.ChartType = "spider" // Default to spider chart
	}

	if req.ChartType != "spider" && req.ChartType != "rays" {
		return fmt.Errorf("unsupported chart type: %s (supported: spider, rays)", req.ChartType)
	}

	if req.StageAData == nil && req.CustomData == nil {
		return fmt.Errorf("either stage_a_data or custom_data must be provided")
	}

	if req.CustomData != nil {
		if err := ValidateChartData(*req.CustomData); err != nil {
			return fmt.Errorf("invalid custom data: %w", err)
		}
	}

	return nil
}

// getSpiderConfig returns the appropriate spider chart configuration based on the request
func (h *ChartAPIHandler) getSpiderConfig(req *ChartGenerationRequest) *SpiderChartConfig {
	// Use custom config if provided
	if req.SpiderConfig != nil {
		return req.SpiderConfig
	}

	// Use preset configuration
	switch req.ConfigPreset {
	case "professional":
		config := CreateProfessionalConfig()
		return &config
	case "high_contrast":
		config := CreateHighContrastConfig()
		return &config
	case "modern_gradient":
		config := CreateModernGradientConfig()
		return &config
	default:
		config := DefaultSpiderChartConfig()
		return &config
	}
}

// getRaysConfig returns the appropriate rays chart configuration based on the request
func (h *ChartAPIHandler) getRaysConfig(req *ChartGenerationRequest) *RaysChartConfig {
	// Use custom config if provided
	if req.RaysConfig != nil {
		return req.RaysConfig
	}

	// Use preset configuration
	switch req.ConfigPreset {
	case "professional":
		config := CreateProfessionalRaysConfig()
		return &config
	case "modern":
		config := CreateModernRaysConfig()
		return &config
	default:
		config := DefaultRaysChartConfig()
		return &config
	}
}

// generateChartURL generates a URL for accessing the generated chart
func (h *ChartAPIHandler) generateChartURL(filename string) string {
	if h.baseURL == "" {
		return filename // Return just filename if no base URL
	}
	return fmt.Sprintf("%s/charts/%s", h.baseURL, filename)
}

// ServeChartsHandler serves generated chart files
func (h *ChartAPIHandler) ServeChartsHandler(w http.ResponseWriter, r *http.Request) {
	// Extract filename from URL path
	filename := filepath.Base(r.URL.Path)
	if filename == "." || filename == "/" {
		http.Error(w, "Chart filename required", http.StatusBadRequest)
		return
	}

	// Construct file path
	filePath := filepath.Join(h.outputDir, filename)

	// Check if file exists
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.Error(w, "Chart not found", http.StatusNotFound)
		return
	}

	// Set appropriate headers
	w.Header().Set("Content-Type", "image/png")
	w.Header().Set("Cache-Control", "public, max-age=3600") // Cache for 1 hour

	// Serve the file
	http.ServeFile(w, r, filePath)
}

// ListChartsHandler lists all available charts
func (h *ChartAPIHandler) ListChartsHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "GET" {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed")
		return
	}

	// Read chart directory
	files, err := os.ReadDir(h.outputDir)
	if err != nil {
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to read charts directory")
		return
	}

	// Build chart list
	charts := []map[string]interface{}{}
	for _, file := range files {
		if !file.IsDir() && filepath.Ext(file.Name()) == ".png" {
			info, _ := file.Info()
			charts = append(charts, map[string]interface{}{
				"filename":    file.Name(),
				"url":         h.generateChartURL(file.Name()),
				"size_bytes":  info.Size(),
				"modified_at": info.ModTime(),
			})
		}
	}

	response := map[string]interface{}{
		"charts": charts,
		"count":  len(charts),
	}

	json.NewEncoder(w).Encode(response)
}

// writeErrorResponse writes a standardized error response
func (h *ChartAPIHandler) writeErrorResponse(w http.ResponseWriter, statusCode int, message string) {
	w.WriteHeader(statusCode)
	response := ChartGenerationResponse{
		Success:     false,
		Error:       message,
		GeneratedAt: time.Now(),
	}
	json.NewEncoder(w).Encode(response)
}

// SetupChartRoutes sets up HTTP routes for chart generation
func SetupChartRoutes(outputDir, baseURL string) *http.ServeMux {
	handler := NewChartAPIHandler(outputDir, baseURL)
	mux := http.NewServeMux()

	// Chart generation endpoint
	mux.HandleFunc("/api/v1/generate-chart", handler.GenerateChartHandler)

	// Chart serving endpoint
	mux.HandleFunc("/charts/", handler.ServeChartsHandler)

	// Chart listing endpoint
	mux.HandleFunc("/api/v1/charts", handler.ListChartsHandler)

	return mux
}
