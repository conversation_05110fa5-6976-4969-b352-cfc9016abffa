package charts

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"math"
	"os"

	"golang.org/x/image/draw"
)

// RaysChartConfig holds configuration for rays chart appearance
type RaysChartConfig struct {
	Width           int
	Height          int
	CenterX         int
	CenterY         int
	MaxRadius       int
	MinRadius       int
	BackgroundColor color.RGBA
	GridColor       color.RGBA
	TextColor       color.RGBA
	RayColors       []color.RGBA
	LineWidth       float64
	ShowGrid        bool
	ShowLabels      bool
	ShowValues      bool
	// Professional styling options
	UseGradientRays bool
	GradientColors  []color.RGBA
	GridLineWidth   float64
	TitleFontSize   int
	LabelFontSize   int
	ShowShadow      bool
	ShadowColor     color.RGBA
	ShadowOffset    int
	AntiAliasing    bool
	ModernStyle     bool
	// Advanced features
	UseNonLinearAxis   bool
	ShowScoresOnLabels bool
	RayWidth           float64
	RaySpacing         float64
}

// DefaultRaysChartConfig returns a default configuration for rays charts
func DefaultRaysChartConfig() RaysChartConfig {
	return RaysChartConfig{
		Width:           800,
		Height:          800,
		CenterX:         400,
		CenterY:         400,
		MaxRadius:       300,
		MinRadius:       50,
		BackgroundColor: color.RGBA{255, 255, 255, 255}, // White
		GridColor:       color.RGBA{200, 200, 200, 255}, // Light gray
		TextColor:       color.RGBA{50, 50, 50, 255},    // Dark gray
		RayColors: []color.RGBA{
			{138, 43, 226, 200}, // Blue Violet
			{75, 0, 130, 200},   // Indigo
			{255, 20, 147, 200}, // Deep Pink
			{255, 69, 0, 200},   // Red Orange
			{255, 140, 0, 200},  // Dark Orange
			{255, 215, 0, 200},  // Gold
			{50, 205, 50, 200},  // Lime Green
			{0, 191, 255, 200},  // Deep Sky Blue
		},
		LineWidth:  2.0,
		ShowGrid:   true,
		ShowLabels: true,
		ShowValues: true,
		// Professional styling defaults
		UseGradientRays: true,
		GradientColors:  []color.RGBA{},
		GridLineWidth:   1.0,
		TitleFontSize:   16,
		LabelFontSize:   14,
		ShowShadow:      false,
		ShadowColor:     color.RGBA{0, 0, 0, 50},
		ShadowOffset:    2,
		AntiAliasing:    false,
		ModernStyle:     false,
		// Advanced features defaults
		UseNonLinearAxis:   true,
		ShowScoresOnLabels: true,
		RayWidth:           20.0,
		RaySpacing:         5.0,
	}
}

// RaysChart generates rays charts
type RaysChart struct {
	config       RaysChartConfig
	fontRenderer *EnhancedFontRenderer
}

// NewRaysChart creates a new rays chart generator
func NewRaysChart(config RaysChartConfig) *RaysChart {
	return &RaysChart{
		config:       config,
		fontRenderer: NewEnhancedFontRenderer(),
	}
}

// Generate creates a rays chart and saves it to the specified path
func (rc *RaysChart) Generate(data SpiderChartData, title, outputPath string) error {
	// Create image
	img := image.NewRGBA(image.Rect(0, 0, rc.config.Width, rc.config.Height))

	// Fill background
	draw.Draw(img, img.Bounds(), &image.Uniform{rc.config.BackgroundColor}, image.Point{}, draw.Src)

	// Draw title
	if title != "" {
		rc.drawText(img, rc.config.CenterX-len(title)*4, 30, title, rc.config.TextColor)
	}

	// Draw grid circles if enabled
	if rc.config.ShowGrid {
		rc.drawGrid(img, data)
	}

	// Draw rays
	rc.drawRays(img, data)

	// Draw labels
	if rc.config.ShowLabels {
		rc.drawLabels(img, data)
	}

	// Save image
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	if err := png.Encode(file, img); err != nil {
		return fmt.Errorf("failed to encode PNG: %w", err)
	}

	return nil
}

// drawGrid draws concentric circles for the grid
func (rc *RaysChart) drawGrid(img *image.RGBA, data SpiderChartData) {
	centerX, centerY := rc.config.CenterX, rc.config.CenterY

	// Draw concentric circles for each level
	for level := 1; level <= data.MaxLevel; level++ {
		radius := rc.calculateRadius(float64(level), data.MaxLevel, data)
		rc.drawCircle(img, centerX, centerY, int(radius), rc.config.GridColor, rc.config.GridLineWidth)

		// Draw level labels
		if level < data.MaxLevel { // Don't draw label for outer circle
			labelText := fmt.Sprintf("%d", level)
			rc.drawText(img, centerX+int(radius)+5, centerY+5, labelText, rc.config.GridColor)
		}
	}
}

// calculateRadius calculates the radius for a given score with non-linear scaling
func (rc *RaysChart) calculateRadius(score float64, maxLevel int, data SpiderChartData) float64 {
	if !rc.config.UseNonLinearAxis {
		// Linear scaling
		ratio := score / float64(maxLevel)
		return float64(rc.config.MinRadius) + ratio*float64(rc.config.MaxRadius-rc.config.MinRadius)
	}

	// Find the maximum actual score in the data
	maxActualScore := 0.0
	for _, domain := range data.Domains {
		if domain.Score > maxActualScore {
			maxActualScore = domain.Score
		}
	}

	// Non-linear scaling for better space utilization
	var ratio float64
	if maxActualScore < 2.0 {
		// Use square root scaling for low scores
		normalizedScore := score / maxActualScore
		ratio = math.Sqrt(normalizedScore) * 0.8
	} else {
		// Hybrid scaling for higher scores
		effectiveMax := math.Max(maxActualScore, float64(maxLevel)*0.6)
		ratio = score / effectiveMax
	}

	return float64(rc.config.MinRadius) + ratio*float64(rc.config.MaxRadius-rc.config.MinRadius)
}

// drawRays draws the individual rays for each domain
func (rc *RaysChart) drawRays(img *image.RGBA, data SpiderChartData) {
	centerX, centerY := rc.config.CenterX, rc.config.CenterY
	numDomains := len(data.Domains)

	if numDomains == 0 {
		return
	}

	for i, domain := range data.Domains {
		// Calculate angle for this ray
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2 // Start from top

		// Calculate ray length based on score
		rayLength := rc.calculateRadius(domain.Score, data.MaxLevel, data)

		// Get ray color
		rayColor := rc.getRayColor(i, domain.Score, data.MaxLevel)

		// Draw ray
		rc.drawRay(img, centerX, centerY, angle, rayLength, rayColor)
	}
}

// getRayColor returns the color for a ray based on index and score
func (rc *RaysChart) getRayColor(index int, score float64, maxLevel int) color.RGBA {
	if len(rc.config.RayColors) == 0 {
		return color.RGBA{100, 100, 100, 200} // Default gray
	}

	// Use cycling colors
	baseColor := rc.config.RayColors[index%len(rc.config.RayColors)]

	if rc.config.UseGradientRays {
		// Adjust color intensity based on score
		intensity := score / float64(maxLevel)
		if intensity > 1.0 {
			intensity = 1.0
		}

		return color.RGBA{
			R: uint8(float64(baseColor.R) * intensity),
			G: uint8(float64(baseColor.G) * intensity),
			B: uint8(float64(baseColor.B) * intensity),
			A: baseColor.A,
		}
	}

	return baseColor
}

// drawRay draws a single ray from center to the specified length
func (rc *RaysChart) drawRay(img *image.RGBA, centerX, centerY int, angle, length float64, rayColor color.RGBA) {
	// Calculate ray endpoints
	endX := centerX + int(length*math.Cos(angle))
	endY := centerY + int(length*math.Sin(angle))

	// Calculate ray width in pixels
	rayWidthPixels := int(rc.config.RayWidth)

	// Draw ray as a thick line
	rc.drawThickLine(img, centerX, centerY, endX, endY, rayColor, rayWidthPixels)

	// Draw ray cap (rounded end)
	if rc.config.ModernStyle {
		rc.drawCircle(img, endX, endY, rayWidthPixels/2, rayColor, 1.0)
	}
}

// drawThickLine draws a thick line between two points
func (rc *RaysChart) drawThickLine(img *image.RGBA, x1, y1, x2, y2 int, col color.RGBA, thickness int) {
	// Calculate line direction
	dx := float64(x2 - x1)
	dy := float64(y2 - y1)
	length := math.Sqrt(dx*dx + dy*dy)

	if length == 0 {
		return
	}

	// Normalize direction
	dx /= length
	dy /= length

	// Calculate perpendicular direction for thickness
	perpX := -dy
	perpY := dx

	// Draw line by filling rectangle
	halfThickness := float64(thickness) / 2.0

	for t := 0.0; t <= length; t += 0.5 {
		centerX := float64(x1) + dx*t
		centerY := float64(y1) + dy*t

		for w := -halfThickness; w <= halfThickness; w += 0.5 {
			pixelX := int(centerX + perpX*w)
			pixelY := int(centerY + perpY*w)

			if pixelX >= 0 && pixelY >= 0 && pixelX < img.Bounds().Dx() && pixelY < img.Bounds().Dy() {
				img.Set(pixelX, pixelY, col)
			}
		}
	}
}

// drawLabels draws domain labels around the chart
func (rc *RaysChart) drawLabels(img *image.RGBA, data SpiderChartData) {
	centerX, centerY := rc.config.CenterX, rc.config.CenterY
	labelRadius := float64(rc.config.MaxRadius) + 40
	numDomains := len(data.Domains)

	for i, domain := range data.Domains {
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2

		// Calculate label position
		labelX := centerX + int(labelRadius*math.Cos(angle))
		labelY := centerY + int(labelRadius*math.Sin(angle))

		// Format label with score if enabled
		labelText := rc.formatDomainLabel(domain)

		// Adjust text position based on angle for better readability
		textWidth := len(labelText) * 8
		if angle > math.Pi/4 && angle < 3*math.Pi/4 {
			labelX -= textWidth / 2 // Bottom labels - center
		} else if angle >= 3*math.Pi/4 || angle <= -3*math.Pi/4 {
			labelX -= textWidth // Left labels - right align
		} else if angle > -3*math.Pi/4 && angle < -math.Pi/4 {
			labelX -= textWidth / 2 // Top labels - center
		}

		rc.drawText(img, labelX, labelY, labelText, rc.config.TextColor)
	}
}

// formatDomainLabel formats domain labels with scores if enabled
func (rc *RaysChart) formatDomainLabel(domain DomainData) string {
	if rc.config.ShowScoresOnLabels {
		return fmt.Sprintf("%s (%.1f)", domain.Name, domain.Score)
	}
	return domain.Name
}

// drawText draws text at the specified position using vector fonts
func (rc *RaysChart) drawText(img *image.RGBA, x, y int, text string, col color.RGBA) {
	// Use vector font renderer for clean, scalable text
	config := DefaultFontConfig()
	config.Color = col
	config.Size = FontSizeSmall
	rc.fontRenderer.DrawText(img, x, y, text, config)
}

// drawCircle draws a circle outline
func (rc *RaysChart) drawCircle(img *image.RGBA, centerX, centerY, radius int, col color.RGBA, lineWidth float64) {
	// Simple circle drawing using Bresenham-like algorithm
	for angle := 0.0; angle < 2*math.Pi; angle += 0.01 {
		x := centerX + int(float64(radius)*math.Cos(angle))
		y := centerY + int(float64(radius)*math.Sin(angle))

		// Draw thick line by drawing multiple pixels around the point
		thickness := int(lineWidth)
		for dx := -thickness; dx <= thickness; dx++ {
			for dy := -thickness; dy <= thickness; dy++ {
				if dx*dx+dy*dy <= thickness*thickness {
					px, py := x+dx, y+dy
					if px >= 0 && py >= 0 && px < img.Bounds().Dx() && py < img.Bounds().Dy() {
						img.Set(px, py, col)
					}
				}
			}
		}
	}
}
