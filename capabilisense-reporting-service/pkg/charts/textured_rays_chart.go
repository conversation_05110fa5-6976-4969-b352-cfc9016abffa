package charts

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"math"
	"os"
)

// TexturedRaysChart generates rays with procedural textures
type TexturedRaysChart struct {
	config RaysChartConfig
}

// NewTexturedRays<PERSON>hart creates a new textured rays chart generator
func NewTexturedRaysChart(config RaysChartConfig) *TexturedRaysChart {
	return &TexturedRaysChart{config: config}
}

// Generate creates a textured rays chart
func (trc *TexturedRaysChart) Generate(data SpiderChartData, title, outputPath string) error {
	img := image.NewRGBA(image.Rect(0, 0, trc.config.Width, trc.config.Height))

	// Fill background
	trc.drawTexturedBackground(img)

	// Draw textured rays
	trc.drawTexturedRays(img, data)

	// Save image
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create file: %w", err)
	}
	defer file.Close()

	if err := png.Encode(file, img); err != nil {
		return fmt.Errorf("failed to encode PNG: %w", err)
	}

	return nil
}

// drawTexturedBackground creates a subtle textured background
func (trc *TexturedRaysChart) drawTexturedBackground(img *image.RGBA) {
	bounds := img.Bounds()

	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			// Create subtle noise texture
			noise := trc.perlinNoise(float64(x)*0.01, float64(y)*0.01) * 0.05

			baseColor := trc.config.BackgroundColor
			r := uint8(math.Max(0, math.Min(255, float64(baseColor.R)*(1.0+noise))))
			g := uint8(math.Max(0, math.Min(255, float64(baseColor.G)*(1.0+noise))))
			b := uint8(math.Max(0, math.Min(255, float64(baseColor.B)*(1.0+noise))))

			img.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}
}

// drawTexturedRays creates rays with procedural textures
func (trc *TexturedRaysChart) drawTexturedRays(img *image.RGBA, data SpiderChartData) {
	centerX := float64(trc.config.CenterX)
	centerY := float64(trc.config.CenterY)
	numDomains := len(data.Domains)

	if numDomains == 0 {
		return
	}

	for i, domain := range data.Domains {
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2
		rayLength := trc.calculateRadius(domain.Score, data.MaxLevel, data)
		rayColor := trc.getRayColor(i, domain.Score, data.MaxLevel)

		// Draw textured ray with scale pattern
		trc.drawScalePatternRay(img, centerX, centerY, angle, rayLength, rayColor, domain.Score, data.MaxLevel)
	}
}

// drawScalePatternRay creates a ray with scale-like texture pattern
func (trc *TexturedRaysChart) drawScalePatternRay(img *image.RGBA, centerX, centerY, angle, length float64,
	rayColor color.RGBA, score float64, maxLevel int) {

	// Number of scales based on score
	numScales := int(math.Ceil(score * 3)) // More scales for higher scores
	if numScales > maxLevel*3 {
		numScales = maxLevel * 3
	}

	scaleLength := length / float64(numScales)
	baseWidth := trc.config.RayWidth

	for scale := 0; scale < numScales; scale++ {
		// Calculate scale properties
		scaleStart := float64(scale) * scaleLength
		scaleEnd := float64(scale+1) * scaleLength

		// Expanding width for each scale
		widthMultiplier := 1.0 + float64(scale)*0.2
		scaleWidth := baseWidth * widthMultiplier

		// Color variation for each scale
		colorVariation := 1.0 - float64(scale)*0.08
		scaleColor := color.RGBA{
			R: uint8(float64(rayColor.R) * colorVariation),
			G: uint8(float64(rayColor.G) * colorVariation),
			B: uint8(float64(rayColor.B) * colorVariation),
			A: rayColor.A,
		}

		// Draw individual scale with texture
		trc.drawTexturedScale(img, centerX, centerY, angle, scaleStart, scaleEnd, scaleWidth, scaleColor, scale)
	}
}

// drawTexturedScale draws a single scale with procedural texture
func (trc *TexturedRaysChart) drawTexturedScale(img *image.RGBA, centerX, centerY, angle, start, end, width float64,
	col color.RGBA, scaleIndex int) {

	// Calculate scale boundaries
	startX := centerX + start*math.Cos(angle)
	startY := centerY + start*math.Sin(angle)
	endX := centerX + end*math.Cos(angle)
	endY := centerY + end*math.Sin(angle)

	// Perpendicular direction for width
	perpX := -math.Sin(angle)
	perpY := math.Cos(angle)

	// Draw scale with texture pattern
	steps := int(end - start)
	if steps < 10 {
		steps = 10
	}

	for step := 0; step < steps; step++ {
		t := float64(step) / float64(steps)

		// Position along scale
		x := startX + t*(endX-startX)
		y := startY + t*(endY-startY)

		// Scale width varies along length (diamond shape)
		widthFactor := 1.0 - math.Abs(t-0.5)*2 // Diamond shape: 0 at ends, 1 at middle
		currentWidth := width * widthFactor

		// Draw cross-section with texture
		for w := -currentWidth / 2; w <= currentWidth/2; w += 0.5 {
			px := x + w*perpX
			py := y + w*perpY

			// Apply texture based on position
			textureValue := trc.scaleTexture(px, py, float64(scaleIndex))

			// Modify color based on texture
			texturedColor := color.RGBA{
				R: uint8(math.Max(0, math.Min(255, float64(col.R)*textureValue))),
				G: uint8(math.Max(0, math.Min(255, float64(col.G)*textureValue))),
				B: uint8(math.Max(0, math.Min(255, float64(col.B)*textureValue))),
				A: col.A,
			}

			trc.setPixel(img, int(px), int(py), texturedColor)
		}
	}

	// Add scale edge highlights
	trc.drawScaleEdges(img, startX, startY, endX, endY, width, perpX, perpY, col)
}

// scaleTexture generates procedural texture for scales
func (trc *TexturedRaysChart) scaleTexture(x, y float64, scaleIndex float64) float64 {
	// Combine multiple noise patterns for complex texture

	// Base pattern - creates scale-like ridges
	ridgePattern := math.Sin(x*0.1+scaleIndex) * math.Sin(y*0.1+scaleIndex)

	// Fine detail noise
	detailNoise := trc.perlinNoise(x*0.05, y*0.05)

	// Radial pattern from center
	centerX := float64(trc.config.CenterX)
	centerY := float64(trc.config.CenterY)
	distance := math.Sqrt((x-centerX)*(x-centerX) + (y-centerY)*(y-centerY))
	radialPattern := math.Sin(distance * 0.02)

	// Combine patterns
	texture := 0.7 + ridgePattern*0.2 + detailNoise*0.1 + radialPattern*0.1

	// Clamp to valid range
	return math.Max(0.3, math.Min(1.2, texture))
}

// drawScaleEdges adds highlights to scale edges
func (trc *TexturedRaysChart) drawScaleEdges(img *image.RGBA, startX, startY, endX, endY, width, perpX, perpY float64, col color.RGBA) {
	// Bright edge color
	edgeColor := color.RGBA{
		R: uint8(math.Min(255, float64(col.R)*1.4)),
		G: uint8(math.Min(255, float64(col.G)*1.4)),
		B: uint8(math.Min(255, float64(col.B)*1.4)),
		A: uint8(float64(col.A) * 0.8),
	}

	// Draw top edge
	for t := 0.0; t <= 1.0; t += 0.1 {
		x := startX + t*(endX-startX)
		y := startY + t*(endY-startY)

		// Width varies along length
		widthFactor := 1.0 - math.Abs(t-0.5)*2
		currentWidth := width * widthFactor

		// Top edge
		topX := x + (currentWidth/2)*perpX
		topY := y + (currentWidth/2)*perpY
		trc.setPixel(img, int(topX), int(topY), edgeColor)

		// Bottom edge
		bottomX := x - (currentWidth/2)*perpX
		bottomY := y - (currentWidth/2)*perpY
		trc.setPixel(img, int(bottomX), int(bottomY), edgeColor)
	}
}

// perlinNoise generates Perlin-like noise
func (trc *TexturedRaysChart) perlinNoise(x, y float64) float64 {
	// Simplified Perlin noise implementation
	xi := int(math.Floor(x))
	yi := int(math.Floor(y))

	xf := x - float64(xi)
	yf := y - float64(yi)

	// Get random values at grid corners
	a := trc.randomValue(xi, yi)
	b := trc.randomValue(xi+1, yi)
	c := trc.randomValue(xi, yi+1)
	d := trc.randomValue(xi+1, yi+1)

	// Interpolate
	i1 := trc.interpolate(a, b, xf)
	i2 := trc.interpolate(c, d, xf)

	return trc.interpolate(i1, i2, yf)
}

// randomValue generates pseudo-random value for given coordinates
func (trc *TexturedRaysChart) randomValue(x, y int) float64 {
	// Simple hash function for deterministic "random" values
	n := x + y*57
	n = (n << 13) ^ n
	return (1.0 - float64((n*(n*n*15731+789221)+1376312589)&0x7fffffff)/1073741824.0)
}

// interpolate performs smooth interpolation
func (trc *TexturedRaysChart) interpolate(a, b, t float64) float64 {
	// Smooth interpolation using cosine
	ft := t * math.Pi
	f := (1.0 - math.Cos(ft)) * 0.5
	return a*(1.0-f) + b*f
}

// setPixel safely sets a pixel in the image
func (trc *TexturedRaysChart) setPixel(img *image.RGBA, x, y int, col color.RGBA) {
	bounds := img.Bounds()
	if x >= bounds.Min.X && x < bounds.Max.X && y >= bounds.Min.Y && y < bounds.Max.Y {
		img.Set(x, y, col)
	}
}

// Helper methods
func (trc *TexturedRaysChart) calculateRadius(score float64, maxLevel int, data SpiderChartData) float64 {
	ratio := score / float64(maxLevel)
	return float64(trc.config.MinRadius) + ratio*float64(trc.config.MaxRadius-trc.config.MinRadius)
}

func (trc *TexturedRaysChart) getRayColor(index int, score float64, maxLevel int) color.RGBA {
	if len(trc.config.RayColors) == 0 {
		return color.RGBA{100, 100, 100, 200}
	}
	return trc.config.RayColors[index%len(trc.config.RayColors)]
}
