package charts

import (
	"fmt"
	"image/color"
	"path/filepath"

	"capabilisense-reporting-service/pkg/dataextraction"
)

// ChartGenerator provides utilities to generate charts from assessment data
type ChartGenerator struct {
	outputDir string
}

// NewChartGenerator creates a new chart generator
func NewChartGenerator(outputDir string) *ChartGenerator {
	return &ChartGenerator{
		outputDir: outputDir,
	}
}

// GenerateSpiderChartFromAssessment creates a spider chart from Stage A assessment data
func (cg *ChartGenerator) GenerateSpiderChartFromAssessment(
	stageAData *dataextraction.StageAOutput,
	filename string,
) (string, error) {
	// Convert Stage A data to spider chart format
	chartData := cg.convertToSpiderChartData(stageAData)

	// Create spider chart with default config
	config := DefaultSpiderChartConfig()

	// Customize config based on number of domains
	numDomains := len(chartData.Domains)
	if numDomains > 8 {
		// For many domains, make chart larger and adjust radius
		config.Width = 1000
		config.Height = 1000
		config.CenterX = 500
		config.CenterY = 500
		config.MaxRadius = 350
	} else if numDomains < 4 {
		// For few domains, make chart smaller
		config.Width = 600
		config.Height = 600
		config.CenterX = 300
		config.CenterY = 300
		config.MaxRadius = 200
	}

	// Create spider chart
	chart := NewSpiderChart(config)

	// Generate output path
	outputPath := filepath.Join(cg.outputDir, filename)

	// Generate chart
	if err := chart.GenerateChart(chartData, outputPath); err != nil {
		return "", fmt.Errorf("failed to generate spider chart: %w", err)
	}

	return outputPath, nil
}

// GenerateRaysChartFromAssessment creates a rays chart from Stage A assessment data
func (cg *ChartGenerator) GenerateRaysChartFromAssessment(
	stageAData *dataextraction.StageAOutput,
	filename string,
) (string, error) {
	// Convert Stage A data to spider chart format (same data structure)
	chartData := cg.convertToSpiderChartData(stageAData)

	// Create rays chart with default config
	config := DefaultRaysChartConfig()

	// Customize config based on number of domains
	numDomains := len(chartData.Domains)
	if numDomains > 8 {
		// For many domains, make chart larger and adjust radius
		config.Width = 1000
		config.Height = 1000
		config.CenterX = 500
		config.CenterY = 500
		config.MaxRadius = 350
	} else if numDomains < 4 {
		// For few domains, make chart smaller
		config.Width = 600
		config.Height = 600
		config.CenterX = 300
		config.CenterY = 300
		config.MaxRadius = 200
	}

	// Create rays chart
	chart := NewRaysChart(config)

	// Generate output path
	outputPath := filepath.Join(cg.outputDir, filename)

	// Generate chart with title
	chartTitle := chartData.ChartTitle
	if err := chart.Generate(chartData, chartTitle, outputPath); err != nil {
		return "", fmt.Errorf("failed to generate rays chart: %w", err)
	}

	return outputPath, nil
}

// convertToSpiderChartData converts Stage A data to spider chart format
func (cg *ChartGenerator) convertToSpiderChartData(stageAData *dataextraction.StageAOutput) SpiderChartData {
	var domains []DomainData

	// Convert domain scores to chart data
	for _, domainScore := range stageAData.DomainScoresForSpiderChart {
		domains = append(domains, DomainData{
			Name:  domainScore.DomainName,
			Score: domainScore.AverageScore,
		})
	}

	// Determine chart title
	chartTitle := "Maturity Assessment Results"
	if stageAData.ReportMetadata.FrameworkName != "" {
		chartTitle = fmt.Sprintf("%s - Maturity Assessment", stageAData.ReportMetadata.FrameworkName)
	}

	return SpiderChartData{
		Domains:    domains,
		MaxLevel:   int(stageAData.OverallMaturity.ScaleMax),
		ChartTitle: chartTitle,
	}
}

// GenerateCustomSpiderChart creates a spider chart with custom configuration
func (cg *ChartGenerator) GenerateCustomSpiderChart(
	domains []DomainData,
	maxLevel int,
	title string,
	filename string,
	customConfig *SpiderChartConfig,
) (string, error) {
	chartData := SpiderChartData{
		Domains:    domains,
		MaxLevel:   maxLevel,
		ChartTitle: title,
	}

	// Use custom config or default
	config := DefaultSpiderChartConfig()
	if customConfig != nil {
		config = *customConfig
	}

	// Create spider chart
	chart := NewSpiderChart(config)

	// Generate output path
	outputPath := filepath.Join(cg.outputDir, filename)

	// Generate chart
	if err := chart.GenerateChart(chartData, outputPath); err != nil {
		return "", fmt.Errorf("failed to generate custom spider chart: %w", err)
	}

	return outputPath, nil
}

// GenerateCustomRaysChart creates a rays chart with custom configuration
func (cg *ChartGenerator) GenerateCustomRaysChart(
	domains []DomainData,
	maxLevel int,
	title string,
	filename string,
	customConfig *RaysChartConfig,
) (string, error) {
	chartData := SpiderChartData{
		Domains:    domains,
		MaxLevel:   maxLevel,
		ChartTitle: title,
	}

	// Use custom config or default
	config := DefaultRaysChartConfig()
	if customConfig != nil {
		config = *customConfig
	}

	// Create rays chart
	chart := NewRaysChart(config)

	// Generate output path
	outputPath := filepath.Join(cg.outputDir, filename)

	// Generate chart
	if err := chart.Generate(chartData, title, outputPath); err != nil {
		return "", fmt.Errorf("failed to generate custom rays chart: %w", err)
	}

	return outputPath, nil
}

// GetColorPalette returns a predefined color palette for multiple domains
func GetColorPalette() []color.RGBA {
	return []color.RGBA{
		{0, 120, 215, 255},   // Blue
		{40, 167, 69, 255},   // Green
		{220, 53, 69, 255},   // Red
		{255, 193, 7, 255},   // Yellow
		{108, 117, 125, 255}, // Gray
		{102, 16, 242, 255},  // Purple
		{255, 133, 27, 255},  // Orange
		{23, 162, 184, 255},  // Cyan
		{232, 62, 140, 255},  // Pink
		{134, 142, 150, 255}, // Light Gray
	}
}

// CreateProfessionalConfig returns a professional-looking chart configuration
func CreateProfessionalConfig() SpiderChartConfig {
	return SpiderChartConfig{
		Width:           900,
		Height:          900,
		CenterX:         450,
		CenterY:         450,
		MaxRadius:       320,
		BackgroundColor: color.RGBA{255, 255, 255, 255}, // White
		GridColor:       color.RGBA{180, 180, 180, 255}, // Light gray
		TextColor:       color.RGBA{33, 37, 41, 255},    // Dark gray
		DataColor:       color.RGBA{0, 123, 191, 255},   // Professional blue
		FillColor:       color.RGBA{0, 123, 191, 60},    // Semi-transparent blue
		LineWidth:       2.5,
		ShowGrid:        true,
		ShowLabels:      true,
		ShowValues:      true,
		// Enhanced professional styling
		UseGradientFill: false,
		GradientColors:  []color.RGBA{},
		GridLineWidth:   1.5,
		TitleFontSize:   18,
		LabelFontSize:   16, // Larger labels for better readability
		ShowShadow:      true,
		ShadowColor:     color.RGBA{0, 0, 0, 30},
		ShadowOffset:    3,
		AntiAliasing:    true,
		ModernStyle:     true,
		// Advanced features
		UseNonLinearAxis:   true, // Better space utilization
		ShowScoresOnLabels: true, // Include scores in labels
		MinAxisValue:       0.0,
	}
}

// CreateHighContrastConfig returns a high-contrast chart configuration for accessibility
func CreateHighContrastConfig() SpiderChartConfig {
	return SpiderChartConfig{
		Width:           800,
		Height:          800,
		CenterX:         400,
		CenterY:         400,
		MaxRadius:       300,
		BackgroundColor: color.RGBA{255, 255, 255, 255}, // White
		GridColor:       color.RGBA{100, 100, 100, 255}, // Dark gray
		TextColor:       color.RGBA{0, 0, 0, 255},       // Black
		DataColor:       color.RGBA{0, 0, 139, 255},     // Dark blue
		FillColor:       color.RGBA{0, 0, 139, 100},     // Semi-transparent dark blue
		LineWidth:       3.0,
		ShowGrid:        true,
		ShowLabels:      true,
		ShowValues:      true,
		// Enhanced accessibility styling
		UseGradientFill: false,
		GradientColors:  []color.RGBA{},
		GridLineWidth:   2.0,
		TitleFontSize:   20,
		LabelFontSize:   16,
		ShowShadow:      false,
		ShadowColor:     color.RGBA{0, 0, 0, 0},
		ShadowOffset:    0,
		AntiAliasing:    true,
		ModernStyle:     false,
	}
}

// CreateModernGradientConfig returns a modern chart with gradient fills like the example
func CreateModernGradientConfig() SpiderChartConfig {
	// Create a gradient similar to the example image
	gradientColors := []color.RGBA{
		{138, 43, 226, 180}, // Blue Violet (center)
		{75, 0, 130, 180},   // Indigo
		{255, 20, 147, 180}, // Deep Pink
		{255, 69, 0, 180},   // Red Orange
		{255, 140, 0, 180},  // Dark Orange
		{255, 215, 0, 180},  // Gold (outer)
	}

	return SpiderChartConfig{
		Width:           1000,
		Height:          1000,
		CenterX:         500,
		CenterY:         500,
		MaxRadius:       380,
		BackgroundColor: color.RGBA{255, 255, 255, 255}, // White background for documents
		GridColor:       color.RGBA{200, 200, 200, 255}, // Light gray grid
		TextColor:       color.RGBA{33, 37, 41, 255},    // Dark text for readability
		DataColor:       color.RGBA{33, 37, 41, 255},    // Dark outline
		FillColor:       color.RGBA{138, 43, 226, 120},  // Fallback fill
		LineWidth:       2.5,
		ShowGrid:        true,
		ShowLabels:      true,
		ShowValues:      true,
		// Modern gradient styling
		UseGradientFill: true,
		GradientColors:  gradientColors,
		GridLineWidth:   1.0,
		TitleFontSize:   24,
		LabelFontSize:   18, // Larger labels for better readability
		ShowShadow:      true,
		ShadowColor:     color.RGBA{0, 0, 0, 50},
		ShadowOffset:    3,
		AntiAliasing:    true,
		ModernStyle:     true,
		// Advanced features for better visualization
		UseNonLinearAxis:   true, // Better space utilization for low scores
		ShowScoresOnLabels: true, // Include scores in labels
		MinAxisValue:       0.0,
	}
}

// CreateProfessionalRaysConfig returns a professional-looking rays chart configuration
func CreateProfessionalRaysConfig() RaysChartConfig {
	return RaysChartConfig{
		Width:           900,
		Height:          900,
		CenterX:         450,
		CenterY:         450,
		MaxRadius:       320,
		MinRadius:       50,
		BackgroundColor: color.RGBA{255, 255, 255, 255}, // White
		GridColor:       color.RGBA{180, 180, 180, 255}, // Light gray
		TextColor:       color.RGBA{33, 37, 41, 255},    // Dark gray
		RayColors: []color.RGBA{
			{0, 123, 191, 200},   // Professional blue
			{40, 167, 69, 200},   // Green
			{220, 53, 69, 200},   // Red
			{255, 193, 7, 200},   // Yellow
			{108, 117, 125, 200}, // Gray
			{102, 16, 242, 200},  // Purple
			{255, 133, 27, 200},  // Orange
			{23, 162, 184, 200},  // Cyan
		},
		LineWidth:          2.5,
		ShowGrid:           true,
		ShowLabels:         true,
		ShowValues:         true,
		UseGradientRays:    true,
		GridLineWidth:      1.5,
		TitleFontSize:      18,
		LabelFontSize:      16,
		ShowShadow:         false,
		AntiAliasing:       true,
		ModernStyle:        true,
		UseNonLinearAxis:   true,
		ShowScoresOnLabels: true,
		RayWidth:           25.0,
		RaySpacing:         5.0,
	}
}

// CreateModernRaysConfig returns a modern rays chart like the StockSavvy example
func CreateModernRaysConfig() RaysChartConfig {
	// Colors inspired by the StockSavvy example
	rayColors := []color.RGBA{
		{138, 43, 226, 200},  // Blue Violet
		{75, 0, 130, 200},    // Indigo
		{255, 20, 147, 200},  // Deep Pink
		{255, 69, 0, 200},    // Red Orange
		{255, 140, 0, 200},   // Dark Orange
		{255, 215, 0, 200},   // Gold
		{50, 205, 50, 200},   // Lime Green
		{0, 191, 255, 200},   // Deep Sky Blue
		{138, 43, 226, 200},  // Blue Violet (repeat for more domains)
		{255, 105, 180, 200}, // Hot Pink
	}

	return RaysChartConfig{
		Width:              1000,
		Height:             1000,
		CenterX:            500,
		CenterY:            500,
		MaxRadius:          380,
		MinRadius:          60,
		BackgroundColor:    color.RGBA{255, 255, 255, 255}, // White background
		GridColor:          color.RGBA{200, 200, 200, 255}, // Light gray grid
		TextColor:          color.RGBA{33, 37, 41, 255},    // Dark text
		RayColors:          rayColors,
		LineWidth:          2.0,
		ShowGrid:           true,
		ShowLabels:         true,
		ShowValues:         true,
		UseGradientRays:    true,
		GridLineWidth:      1.0,
		TitleFontSize:      24,
		LabelFontSize:      18,
		ShowShadow:         false,
		AntiAliasing:       true,
		ModernStyle:        true,
		UseNonLinearAxis:   true,
		ShowScoresOnLabels: true,
		RayWidth:           30.0, // Thicker rays for modern look
		RaySpacing:         8.0,
	}
}

// Example usage function for testing
func ExampleUsage() {
	// Create chart generator
	generator := NewChartGenerator("./charts")

	// Example domain data
	domains := []DomainData{
		{Name: "Strategy", Score: 3.5},
		{Name: "Operations", Score: 2.8},
		{Name: "Technology", Score: 4.2},
		{Name: "People", Score: 3.1},
		{Name: "Governance", Score: 2.5},
	}

	// Generate spider chart with professional config
	spiderConfig := CreateProfessionalConfig()
	spiderPath, err := generator.GenerateCustomSpiderChart(
		domains,
		5, // Max level
		"Organizational Maturity Assessment - Spider Chart",
		"maturity_spider_chart.png",
		&spiderConfig,
	)

	if err != nil {
		fmt.Printf("Error generating spider chart: %v\n", err)
		return
	}

	fmt.Printf("Spider chart generated successfully: %s\n", spiderPath)

	// Generate rays chart with modern config
	raysConfig := CreateModernRaysConfig()
	raysPath, err := generator.GenerateCustomRaysChart(
		domains,
		5, // Max level
		"Organizational Maturity Assessment - Rays Chart",
		"maturity_rays_chart.png",
		&raysConfig,
	)

	if err != nil {
		fmt.Printf("Error generating rays chart: %v\n", err)
		return
	}

	fmt.Printf("Rays chart generated successfully: %s\n", raysPath)
}

// ValidateChartData validates spider chart data before generation
func ValidateChartData(data SpiderChartData) error {
	if len(data.Domains) == 0 {
		return fmt.Errorf("no domains provided")
	}

	if len(data.Domains) > 12 {
		return fmt.Errorf("too many domains (%d), maximum supported is 12", len(data.Domains))
	}

	if data.MaxLevel <= 0 {
		return fmt.Errorf("max level must be positive, got %d", data.MaxLevel)
	}

	if data.MaxLevel > 10 {
		return fmt.Errorf("max level too high (%d), maximum supported is 10", data.MaxLevel)
	}

	// Validate domain scores
	for i, domain := range data.Domains {
		if domain.Name == "" {
			return fmt.Errorf("domain %d has empty name", i)
		}

		if domain.Score < 0 {
			return fmt.Errorf("domain '%s' has negative score: %f", domain.Name, domain.Score)
		}

		if domain.Score > float64(data.MaxLevel) {
			return fmt.Errorf("domain '%s' score (%f) exceeds max level (%d)",
				domain.Name, domain.Score, data.MaxLevel)
		}
	}

	return nil
}

// GetChartDimensions calculates optimal chart dimensions based on number of domains
func GetChartDimensions(numDomains int) (width, height, centerX, centerY, maxRadius int) {
	switch {
	case numDomains <= 3:
		return 600, 600, 300, 300, 200
	case numDomains <= 6:
		return 800, 800, 400, 400, 300
	case numDomains <= 9:
		return 1000, 1000, 500, 500, 350
	default:
		return 1200, 1200, 600, 600, 400
	}
}
