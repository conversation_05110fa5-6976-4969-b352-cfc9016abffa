package charts

import (
	"fmt"
	"math"
	"os"
	"strings"
)

// SV<PERSON><PERSON><PERSON>hart generates fancy SVG-based rays charts
type SVGRays<PERSON>hart struct {
	config RaysChartConfig
}

// NewSVGR<PERSON><PERSON><PERSON> creates a new SVG rays chart generator
func NewSVGRaysChart(config RaysChartConfig) *SVGRaysChart {
	return &SVGRaysChart{config: config}
}

// Generate creates a fancy SVG rays chart
func (src *SVGRaysChart) Generate(data SpiderChartData, title, outputPath string) error {
	svg := src.generateSVG(data, title)
	
	// Write SVG to file
	file, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create SVG file: %w", err)
	}
	defer file.Close()
	
	_, err = file.WriteString(svg)
	if err != nil {
		return fmt.Errorf("failed to write SVG: %w", err)
	}
	
	return nil
}

// generateSVG creates the SVG content with fancy rays
func (src *SVGRaysChart) generateSVG(data SpiderChartData, title string) string {
	var svg strings.Builder
	
	// SVG header
	svg.WriteString(fmt.Sprintf(`<svg width="%d" height="%d" xmlns="http://www.w3.org/2000/svg">`, 
		src.config.Width, src.config.Height))
	
	// Add definitions for gradients, patterns, and effects
	svg.WriteString(src.generateDefinitions(data))
	
	// Background
	svg.WriteString(fmt.Sprintf(`<rect width="100%%" height="100%%" fill="rgb(%d,%d,%d)"/>`,
		src.config.BackgroundColor.R, src.config.BackgroundColor.G, src.config.BackgroundColor.B))
	
	// Grid circles
	if src.config.ShowGrid {
		svg.WriteString(src.generateGrid(data))
	}
	
	// Fancy rays
	svg.WriteString(src.generateFancyRays(data))
	
	// Labels
	if src.config.ShowLabels {
		svg.WriteString(src.generateLabels(data))
	}
	
	// Title
	if title != "" {
		svg.WriteString(src.generateTitle(title))
	}
	
	svg.WriteString("</svg>")
	return svg.String()
}

// generateDefinitions creates SVG definitions for gradients and patterns
func (src *SVGRaysChart) generateDefinitions(data SpiderChartData) string {
	var defs strings.Builder
	defs.WriteString("<defs>")
	
	// Create radial gradients for each ray
	for i, domain := range data.Domains {
		rayColor := src.getRayColor(i, domain.Score, data.MaxLevel)
		
		// Radial gradient for 3D effect
		defs.WriteString(fmt.Sprintf(`
			<radialGradient id="rayGradient%d" cx="0%%" cy="0%%" r="100%%">
				<stop offset="0%%" style="stop-color:rgb(%d,%d,%d);stop-opacity:0.9"/>
				<stop offset="50%%" style="stop-color:rgb(%d,%d,%d);stop-opacity:0.7"/>
				<stop offset="100%%" style="stop-color:rgb(%d,%d,%d);stop-opacity:0.3"/>
			</radialGradient>`,
			i,
			min(255, int(float64(rayColor.R)*1.3)), min(255, int(float64(rayColor.G)*1.3)), min(255, int(float64(rayColor.B)*1.3)),
			rayColor.R, rayColor.G, rayColor.B,
			max(0, int(float64(rayColor.R)*0.7)), max(0, int(float64(rayColor.G)*0.7)), max(0, int(float64(rayColor.B)*0.7))))
		
		// Linear gradient for ray segments
		defs.WriteString(fmt.Sprintf(`
			<linearGradient id="rayLinear%d" x1="0%%" y1="0%%" x2="100%%" y2="0%%">
				<stop offset="0%%" style="stop-color:rgb(%d,%d,%d);stop-opacity:0.1"/>
				<stop offset="30%%" style="stop-color:rgb(%d,%d,%d);stop-opacity:0.8"/>
				<stop offset="70%%" style="stop-color:rgb(%d,%d,%d);stop-opacity:0.9"/>
				<stop offset="100%%" style="stop-color:rgb(%d,%d,%d);stop-opacity:0.6"/>
			</linearGradient>`,
			i,
			rayColor.R, rayColor.G, rayColor.B,
			rayColor.R, rayColor.G, rayColor.B,
			rayColor.R, rayColor.G, rayColor.B,
			rayColor.R, rayColor.G, rayColor.B))
	}
	
	// Drop shadow filter
	defs.WriteString(`
		<filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
			<feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.3)"/>
		</filter>`)
	
	// Glow effect filter
	defs.WriteString(`
		<filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
			<feGaussianBlur stdDeviation="3" result="coloredBlur"/>
			<feMerge>
				<feMergeNode in="coloredBlur"/>
				<feMergeNode in="SourceGraphic"/>
			</feMerge>
		</filter>`)
	
	defs.WriteString("</defs>")
	return defs.String()
}

// generateFancyRays creates sophisticated ray segments
func (src *SVGRaysChart) generateFancyRays(data SpiderChartData) string {
	var rays strings.Builder
	centerX, centerY := float64(src.config.CenterX), float64(src.config.CenterY)
	numDomains := len(data.Domains)
	
	if numDomains == 0 {
		return ""
	}
	
	for i, domain := range data.Domains {
		angle := 2*math.Pi*float64(i)/float64(numDomains) - math.Pi/2
		rayLength := src.calculateRadius(domain.Score, data.MaxLevel, data)
		
		// Create segmented ray with expanding scales
		rays.WriteString(src.generateSegmentedRay(i, centerX, centerY, angle, rayLength, domain.Score, data.MaxLevel))
	}
	
	return rays.String()
}

// generateSegmentedRay creates a ray with expanding segments (like StockSavvy)
func (src *SVGRaysChart) generateSegmentedRay(rayIndex int, centerX, centerY, angle, length, score float64, maxLevel int) string {
	var ray strings.Builder
	
	// Number of segments based on score
	numSegments := int(math.Ceil(score))
	if numSegments > maxLevel {
		numSegments = maxLevel
	}
	
	segmentLength := length / float64(maxLevel)
	
	for segment := 0; segment < numSegments; segment++ {
		// Calculate segment position
		segmentStart := float64(segment) * segmentLength
		segmentEnd := float64(segment+1) * segmentLength
		
		// Calculate segment width (expanding outward)
		baseWidth := src.config.RayWidth
		widthMultiplier := 1.0 + float64(segment)*0.3 // Expand by 30% per segment
		segmentWidth := baseWidth * widthMultiplier
		
		// Calculate segment positions
		startX := centerX + segmentStart*math.Cos(angle)
		startY := centerY + segmentStart*math.Sin(angle)
		endX := centerX + segmentEnd*math.Cos(angle)
		endY := centerY + segmentEnd*math.Sin(angle)
		
		// Calculate perpendicular offset for width
		perpX := -math.Sin(angle) * segmentWidth / 2
		perpY := math.Cos(angle) * segmentWidth / 2
		
		// Create segment as a polygon with rounded ends
		ray.WriteString(fmt.Sprintf(`
			<polygon points="%.2f,%.2f %.2f,%.2f %.2f,%.2f %.2f,%.2f"
				fill="url(#rayLinear%d)"
				stroke="none"
				filter="url(#glow)"
				opacity="%.2f"/>`,
			startX-perpX, startY-perpY,
			startX+perpX, startY+perpY,
			endX+perpX, endY+perpY,
			endX-perpX, endY-perpY,
			rayIndex,
			0.8-float64(segment)*0.1)) // Fade out towards the end
		
		// Add segment separator (small gap)
		if segment < numSegments-1 {
			gapX := centerX + (segmentEnd+1)*math.Cos(angle)
			gapY := centerY + (segmentEnd+1)*math.Sin(angle)
			ray.WriteString(fmt.Sprintf(`
				<circle cx="%.2f" cy="%.2f" r="2"
					fill="rgba(255,255,255,0.3)"/>`,
				gapX, gapY))
		}
	}
	
	// Add end cap with glow effect
	if numSegments > 0 {
		endX := centerX + length*math.Cos(angle)
		endY := centerY + length*math.Sin(angle)
		ray.WriteString(fmt.Sprintf(`
			<circle cx="%.2f" cy="%.2f" r="%.2f"
				fill="url(#rayGradient%d)"
				filter="url(#dropShadow)"/>`,
			endX, endY, src.config.RayWidth/2, rayIndex))
	}
	
	return ray.String()
}

// Helper functions
func (src *SVGRaysChart) calculateRadius(score float64, maxLevel int, data SpiderChartData) float64 {
	// Same logic as the original rays chart
	ratio := score / float64(maxLevel)
	return float64(src.config.MinRadius) + ratio*float64(src.config.MaxRadius-src.config.MinRadius)
}

func (src *SVGRaysChart) getRayColor(index int, score float64, maxLevel int) RayColor {
	if len(src.config.RayColors) == 0 {
		return RayColor{R: 100, G: 100, B: 100}
	}
	
	baseColor := src.config.RayColors[index%len(src.config.RayColors)]
	return RayColor{R: baseColor.R, G: baseColor.G, B: baseColor.B}
}

type RayColor struct {
	R, G, B uint8
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// Additional methods for grid, labels, title generation would go here...
func (src *SVGRaysChart) generateGrid(data SpiderChartData) string {
	// Implementation for grid circles
	return ""
}

func (src *SVGRaysChart) generateLabels(data SpiderChartData) string {
	// Implementation for labels
	return ""
}

func (src *SVGRaysChart) generateTitle(title string) string {
	// Implementation for title
	return ""
}
