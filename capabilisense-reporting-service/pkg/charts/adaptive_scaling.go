package charts

import (
	"fmt"
	"image/color"
	"math"
)

// AdaptiveScalingConfig controls the adaptive scaling behavior
type AdaptiveScalingConfig struct {
	// Scaling behavior
	UseAdaptiveScaling bool    // Enable adaptive scaling
	ZoomThreshold      float64 // If max score < this, zoom in (e.g., 2.5)
	ZoomFactor         float64 // How much to zoom (e.g., 1.5 means zoom to 1.5x max score)
	MinZoomRange       float64 // Minimum range to zoom to (e.g., 2.0)

	// Visual indicators for unused range
	ShowUnusedRange      bool       // Show red gradient for unused range
	UnusedRangeColor     color.RGBA // Base color for unused range (red)
	UnusedRangeIntensity float64    // How intense the unused range color is (0-1)
	UnusedRangeWidth     float64    // Width of unused range indicator (pixels)

	// Grid adaptation
	AdaptGridToZoom     bool    // Adapt grid lines to zoomed range
	ShowOriginalGrid    bool    // Show original grid lines faintly
	OriginalGridOpacity float64 // Opacity for original grid lines

	// Labels and indicators
	ShowZoomIndicator  bool // Show text indicating zoom level
	ShowActualRange    bool // Show actual score range in labels
	ZoomIndicatorColor color.RGBA
}

// DefaultAdaptiveScalingConfig returns sensible defaults
func DefaultAdaptiveScalingConfig() AdaptiveScalingConfig {
	return AdaptiveScalingConfig{
		UseAdaptiveScaling:   true,
		ZoomThreshold:        2.5,
		ZoomFactor:           1.3,
		MinZoomRange:         2.0,
		ShowUnusedRange:      true,
		UnusedRangeColor:     color.RGBA{220, 53, 69, 120}, // Bootstrap danger red with transparency
		UnusedRangeIntensity: 0.6,
		UnusedRangeWidth:     8.0,
		AdaptGridToZoom:      true,
		ShowOriginalGrid:     true,
		OriginalGridOpacity:  0.3,
		ShowZoomIndicator:    true,
		ShowActualRange:      true,
		ZoomIndicatorColor:   color.RGBA{108, 117, 125, 255}, // Bootstrap secondary gray
	}
}

// ScalingResult contains the results of adaptive scaling calculations
type ScalingResult struct {
	// Scaling parameters
	IsZoomed         bool    // Whether zooming was applied
	OriginalMaxScore float64 // Original maximum score in data
	ZoomedRange      float64 // The range we're zooming to
	ScalingFactor    float64 // Factor to apply to scores

	// Visual zones
	UsedRangeEnd     float64 // Where the used range ends (in original scale)
	UnusedRangeStart float64 // Where the unused range starts (in original scale)
	UnusedRangeEnd   float64 // Where the unused range ends (original max level)

	// Grid information
	ZoomedGridLevels   []float64 // Grid levels for zoomed view
	OriginalGridLevels []float64 // Original grid levels to show faintly

	// Display information
	ZoomDescription  string // Human-readable zoom description
	RangeDescription string // Description of actual vs possible range
}

// CalculateAdaptiveScaling analyzes data and determines optimal scaling
func CalculateAdaptiveScaling(data SpiderChartData, config AdaptiveScalingConfig) ScalingResult {
	result := ScalingResult{
		OriginalMaxScore: findMaxScore(data),
		UnusedRangeEnd:   float64(data.MaxLevel),
	}

	// Check if we should apply zooming
	if !config.UseAdaptiveScaling || result.OriginalMaxScore >= config.ZoomThreshold {
		// No zooming needed - use linear scaling
		result.IsZoomed = false
		result.ZoomedRange = float64(data.MaxLevel)
		result.ScalingFactor = 1.0
		result.UsedRangeEnd = float64(data.MaxLevel)
		result.UnusedRangeStart = float64(data.MaxLevel)
		result.ZoomDescription = "Full range view"
		result.RangeDescription = "Using full scale"
		return result
	}

	// Apply adaptive zooming
	result.IsZoomed = true

	// Calculate zoom range
	result.ZoomedRange = math.Max(
		result.OriginalMaxScore*config.ZoomFactor,
		config.MinZoomRange,
	)

	// Ensure we don't zoom beyond the original max level
	if result.ZoomedRange > float64(data.MaxLevel) {
		result.ZoomedRange = float64(data.MaxLevel)
		result.IsZoomed = false
	}

	// Calculate scaling factor
	result.ScalingFactor = float64(data.MaxLevel) / result.ZoomedRange

	// Define visual zones
	result.UsedRangeEnd = result.ZoomedRange
	result.UnusedRangeStart = result.ZoomedRange

	// Generate grid levels
	result.ZoomedGridLevels = generateGridLevels(result.ZoomedRange, 4) // 4-5 grid levels
	result.OriginalGridLevels = generateGridLevels(float64(data.MaxLevel), data.MaxLevel)

	// Generate descriptions
	result.ZoomDescription = fmt.Sprintf("Zoomed to %.1f (max score: %.1f)", result.ZoomedRange, result.OriginalMaxScore)
	result.RangeDescription = fmt.Sprintf("Showing 0-%.1f of 0-%d scale", result.ZoomedRange, data.MaxLevel)

	return result
}

// ApplyAdaptiveRadius calculates radius using adaptive scaling
func ApplyAdaptiveRadius(score float64, scaling ScalingResult, minRadius, maxRadius int) float64 {
	if !scaling.IsZoomed {
		// Standard linear scaling
		ratio := score / scaling.UnusedRangeEnd
		return float64(minRadius) + ratio*float64(maxRadius-minRadius)
	}

	// Adaptive scaling - zoom into the used range
	if score > scaling.ZoomedRange {
		score = scaling.ZoomedRange // Clamp to zoomed range
	}

	ratio := score / scaling.ZoomedRange
	return float64(minRadius) + ratio*float64(maxRadius-minRadius)
}

// GetAdaptiveRayColor returns color with adaptive intensity based on scaling
func GetAdaptiveRayColor(baseColor color.RGBA, score float64, scaling ScalingResult, config AdaptiveScalingConfig) color.RGBA {
	if !scaling.IsZoomed {
		return baseColor
	}

	// For zoomed view, maintain color intensity but indicate if score is in "danger zone"
	intensity := 1.0

	// If score is very low relative to the original scale, reduce intensity slightly
	originalRatio := score / scaling.UnusedRangeEnd
	if originalRatio < 0.4 { // Less than 40% of original scale
		intensity = 0.7 + originalRatio*0.3 // Scale from 70% to 100% intensity
	}

	return color.RGBA{
		R: uint8(float64(baseColor.R) * intensity),
		G: uint8(float64(baseColor.G) * intensity),
		B: uint8(float64(baseColor.B) * intensity),
		A: baseColor.A,
	}
}

// DrawUnusedRangeIndicator draws the red gradient for unused range
func DrawUnusedRangeIndicator(img interface{}, centerX, centerY int, scaling ScalingResult,
	config AdaptiveScalingConfig, minRadius, maxRadius int) {

	if !scaling.IsZoomed || !config.ShowUnusedRange {
		return
	}

	// Calculate where unused range starts and ends in pixels
	usedRangeRadius := float64(minRadius) + float64(maxRadius-minRadius)
	unusedStartRadius := usedRangeRadius
	unusedEndRadius := float64(maxRadius)

	// Draw concentric circles with red gradient for unused range
	numRings := int((unusedEndRadius - unusedStartRadius) / config.UnusedRangeWidth)
	if numRings < 1 {
		numRings = 1
	}

	for ring := 0; ring < numRings; ring++ {
		ringRadius := unusedStartRadius + float64(ring)*config.UnusedRangeWidth
		if ringRadius > unusedEndRadius {
			ringRadius = unusedEndRadius
		}

		// Calculate opacity - stronger near the used range, fading outward
		ringRatio := float64(ring) / float64(numRings)
		opacity := config.UnusedRangeIntensity * (1.0 - ringRatio*0.7)

		ringColor := color.RGBA{
			R: config.UnusedRangeColor.R,
			G: config.UnusedRangeColor.G,
			B: config.UnusedRangeColor.B,
			A: uint8(float64(config.UnusedRangeColor.A) * opacity),
		}

		// Draw ring (implementation depends on chart type)
		drawRing(img, centerX, centerY, int(ringRadius), ringColor, config.UnusedRangeWidth)
	}
}

// Helper functions
func findMaxScore(data SpiderChartData) float64 {
	maxScore := 0.0
	for _, domain := range data.Domains {
		if domain.Score > maxScore {
			maxScore = domain.Score
		}
	}
	return maxScore
}

func generateGridLevels(maxValue float64, maxLevels int) []float64 {
	levels := make([]float64, 0)

	// Generate nice round numbers for grid levels
	step := maxValue / 4.0 // Aim for 4-5 levels

	// Round step to nice number
	if step < 0.5 {
		step = 0.25
	} else if step < 1.0 {
		step = 0.5
	} else if step < 2.0 {
		step = 1.0
	} else if step < 5.0 {
		step = 2.0
	} else {
		step = 5.0
	}

	for level := step; level <= maxValue; level += step {
		levels = append(levels, level)
		if len(levels) >= maxLevels {
			break
		}
	}

	return levels
}

// drawRing is a placeholder - actual implementation depends on chart type
func drawRing(img interface{}, centerX, centerY, radius int, color color.RGBA, width float64) {
	// This would be implemented differently for each chart type
	// For now, it's a placeholder that each chart type would implement
}

// AdaptiveGridInfo contains information for drawing adaptive grids
type AdaptiveGridInfo struct {
	ZoomedLevels    []GridLevel
	OriginalLevels  []GridLevel
	ShowZoomInfo    bool
	ZoomDescription string
}

type GridLevel struct {
	Value  float64
	Radius int
	Color  color.RGBA
	Width  float64
	Label  string
}

// GenerateAdaptiveGrid creates grid information for adaptive scaling
func GenerateAdaptiveGrid(scaling ScalingResult, config AdaptiveScalingConfig,
	minRadius, maxRadius int) AdaptiveGridInfo {

	info := AdaptiveGridInfo{
		ShowZoomInfo:    config.ShowZoomIndicator,
		ZoomDescription: scaling.ZoomDescription,
	}

	// Generate zoomed grid levels
	for _, level := range scaling.ZoomedGridLevels {
		radius := int(ApplyAdaptiveRadius(level, scaling, minRadius, maxRadius))
		info.ZoomedLevels = append(info.ZoomedLevels, GridLevel{
			Value:  level,
			Radius: radius,
			Color:  color.RGBA{180, 180, 180, 255}, // Normal grid color
			Width:  1.5,
			Label:  fmt.Sprintf("%.1f", level),
		})
	}

	// Generate original grid levels (faint)
	if config.ShowOriginalGrid && scaling.IsZoomed {
		for _, level := range scaling.OriginalGridLevels {
			if level > scaling.ZoomedRange { // Only show levels beyond zoomed range
				radius := int(float64(minRadius) + (level/scaling.UnusedRangeEnd)*float64(maxRadius-minRadius))
				gridColor := color.RGBA{200, 200, 200, uint8(255 * config.OriginalGridOpacity)}
				info.OriginalLevels = append(info.OriginalLevels, GridLevel{
					Value:  level,
					Radius: radius,
					Color:  gridColor,
					Width:  0.8,
					Label:  fmt.Sprintf("%.0f", level),
				})
			}
		}
	}

	return info
}
