package dataextraction

import (
	"fmt"
	"time"
)

// DataExtractor defines the interface for extracting assessment data
type DataExtractor interface {
	// ExtractAnalysisResults retrieves raw assessment data for a specific project and run
	ExtractAnalysisResults(projectID, runID string) (*AnalysisResults, error)

	// ExtractFrameworkData retrieves framework structure and configuration
	ExtractFrameworkData(frameworkID string) (*FrameworkData, error)

	// ValidateDataIntegrity checks if the extracted data is complete and valid
	ValidateDataIntegrity(data *AnalysisResults) error
}

// MockDataExtractor provides mock data for development and testing
type MockDataExtractor struct {
	// Configuration for mock data generation
	MockOrgName   string
	MockProjectID string
	MockRunID     string
}

// NewMockDataExtractor creates a new mock data extractor
func NewMockDataExtractor() *MockDataExtractor {
	return &MockDataExtractor{
		MockOrgName:   "MockCorp Inc.",
		MockProjectID: "proj-123",
		MockRunID:     "run-456",
	}
}

// ExtractAnalysisResults returns mock analysis results
func (m *MockDataExtractor) ExtractAnalysisResults(projectID, runID string) (*AnalysisResults, error) {
	// Generate mock data
	now := time.Now()
	evidenceDate := now.AddDate(0, 0, -5) // 5 days ago

	return &AnalysisResults{
		ProjectID:      projectID,
		RunID:          runID,
		OrganizationID: "org-789",
		AssessmentDate: now,
		EvidenceDate:   evidenceDate,
		DomainScores: map[string]float64{
			"vision":  4.0,
			"people":  3.5,
			"process": 2.5,
			"tech":    2.2,
		},
		FrameworkData: FrameworkData{
			FrameworkID: "capability-framework-v1",
			Version:     "1.0",
			Domains: []Domain{
				{
					ID:          "vision",
					Name:        "Strategic Vision",
					Description: "Strategic planning and vision capabilities",
					Weight:      0.25,
					Score:       4.0,
				},
				{
					ID:          "people",
					Name:        "People & Culture",
					Description: "Human resources and organizational culture",
					Weight:      0.25,
					Score:       3.5,
				},
				{
					ID:          "process",
					Name:        "Process Excellence",
					Description: "Business process optimization and automation",
					Weight:      0.25,
					Score:       2.5,
				},
				{
					ID:          "tech",
					Name:        "Technology",
					Description: "Technology infrastructure and capabilities",
					Weight:      0.25,
					Score:       2.2,
				},
			},
		},
		RawResponses: []AssessmentResponse{
			{
				QuestionID:   "q1",
				ResponseText: "We have a clear strategic vision",
				Score:        4.0,
				Timestamp:    now,
			},
			{
				QuestionID:   "q2",
				ResponseText: "Our team is well-trained",
				Score:        3.5,
				Timestamp:    now,
			},
		},
		Metadata: map[string]interface{}{
			"assessor_count":  5,
			"completion_rate": 0.95,
		},
	}, nil
}

// ExtractFrameworkData returns mock framework data
func (m *MockDataExtractor) ExtractFrameworkData(frameworkID string) (*FrameworkData, error) {
	return &FrameworkData{
		FrameworkID: frameworkID,
		Version:     "1.0",
		Domains: []Domain{
			{
				ID:          "vision",
				Name:        "Strategic Vision",
				Description: "Strategic planning and vision capabilities",
				Weight:      0.25,
			},
			{
				ID:          "people",
				Name:        "People & Culture",
				Description: "Human resources and organizational culture",
				Weight:      0.25,
			},
			{
				ID:          "process",
				Name:        "Process Excellence",
				Description: "Business process optimization and automation",
				Weight:      0.25,
			},
			{
				ID:          "tech",
				Name:        "Technology",
				Description: "Technology infrastructure and capabilities",
				Weight:      0.25,
			},
		},
		ScoringRules: map[string]interface{}{
			"scale_min":          1.0,
			"scale_max":          5.0,
			"aggregation_method": "weighted_average",
		},
		WeightingScheme: map[string]float64{
			"vision":  0.25,
			"people":  0.25,
			"process": 0.25,
			"tech":    0.25,
		},
	}, nil
}

// ValidateDataIntegrity validates the extracted data
func (m *MockDataExtractor) ValidateDataIntegrity(data *AnalysisResults) error {
	if data == nil {
		return fmt.Errorf("analysis results cannot be nil")
	}

	if data.ProjectID == "" {
		return fmt.Errorf("project ID cannot be empty")
	}

	if data.RunID == "" {
		return fmt.Errorf("run ID cannot be empty")
	}

	if len(data.DomainScores) == 0 {
		return fmt.Errorf("domain scores cannot be empty")
	}

	// Validate score ranges
	for domain, score := range data.DomainScores {
		if score < 1.0 || score > 5.0 {
			return fmt.Errorf("invalid score %f for domain %s (must be between 1.0 and 5.0)", score, domain)
		}
	}

	return nil
}
