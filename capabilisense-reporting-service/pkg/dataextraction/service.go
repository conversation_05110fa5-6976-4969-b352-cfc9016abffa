package dataextraction

import (
	"fmt"
	"log"
	"sort"
)

// Service handles the core business logic for Stage A data extraction
type Service struct {
	repo Repository
}

// NewService creates a new service instance
func NewService(repo Repository) *Service {
	return &Service{repo: repo}
}

// GenerateReportData generates the complete Stage A output for a given project and optional run
func (s *Service) GenerateReportData(projectID string, optionalRunID string) (*StageAOutput, error) {
	// Get analysis run
	var analysisRun *AnalysisRun
	var err error

	if optionalRunID != "" {
		analysisRun, err = s.repo.GetAnalysisRunByID(projectID, optionalRunID)
	} else {
		analysisRun, err = s.repo.GetLatestAnalysisRun(projectID)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get analysis run: %w", err)
	}

	// Get framework
	framework, err := s.repo.GetFrameworkByUUID(analysisRun.FrameworkUUID)
	if err != nil {
		return nil, fmt.Errorf("failed to get framework: %w", err)
	}

	// Detect and parse framework content based on structure
	frameworkStructure := framework.DetectFrameworkStructure()
	log.Printf("Detected framework structure: %s", frameworkStructure)

	var hierarchyMap map[string]*EntityNode
	var frameworkOverview FrameworkOverview
	var topLevelDomains []string

	if frameworkStructure == "alternative" {
		// Handle alternative framework structure
		altContent, err := framework.GetAlternativeFrameworkContent()
		if err != nil {
			return nil, fmt.Errorf("failed to parse alternative framework content: %w", err)
		}

		hierarchyMap, topLevelDomains = s.buildAlternativeHierarchyMap(altContent)
		frameworkOverview = FrameworkOverview{
			EntityName:         altContent.Entity.Name,
			EntityDescription:  altContent.Entity.Description,
			TermAliases:        altContent.TermAliases,
			TopLevelGroupNames: topLevelDomains,
		}
	} else {
		// Handle standard framework structure
		frameworkContent, err := framework.GetFrameworkContent()
		if err != nil {
			return nil, fmt.Errorf("failed to parse framework content: %w", err)
		}

		hierarchyMap = s.buildHierarchyMap(frameworkContent.Entity)

		// Extract top-level domains
		for _, child := range frameworkContent.Entity.Children {
			if child.Type == "grouping" {
				topLevelDomains = append(topLevelDomains, child.Name)
			}
		}

		frameworkOverview = FrameworkOverview{
			EntityName:         frameworkContent.Entity.Name,
			EntityDescription:  frameworkContent.Entity.Description,
			TermAliases:        frameworkContent.TermAliases,
			TopLevelGroupNames: topLevelDomains,
		}
	}

	// Get assessments
	indicatorAssessments, err := s.repo.GetIndicatorAssessments(analysisRun.RunID)
	if err != nil {
		return nil, fmt.Errorf("failed to get indicator assessments: %w", err)
	}

	capabilityAssessments, err := s.repo.GetCapabilityAssessments(analysisRun.RunID)
	if err != nil {
		return nil, fmt.Errorf("failed to get capability assessments: %w", err)
	}

	// Get processed documents
	_, err = s.repo.GetProcessedDocuments(projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to get processed documents: %w", err)
	}

	// Parse document names from analysis run
	documentNames, err := analysisRun.GetDocumentNames()
	if err != nil {
		return nil, fmt.Errorf("failed to parse document names: %w", err)
	}

	// Calculate overall maturity (median of leaf capability scores)
	overallMaturity := s.calculateOverallMaturity(capabilityAssessments, hierarchyMap)

	// Calculate domain scores for spider chart
	domainScores := s.calculateDomainScoresFromHierarchy(topLevelDomains, capabilityAssessments, hierarchyMap)

	// Identify key strengths (top 3 domains)
	keyStrengths := s.identifyKeyStrengths(domainScores, capabilityAssessments, indicatorAssessments, hierarchyMap, 3)

	// Identify critical areas (bottom 3 domains)
	criticalAreas := s.identifyCriticalAreas(domainScores, capabilityAssessments, indicatorAssessments, hierarchyMap, 3)

	// Build context for AI
	contextForAI := s.buildContextForAI(domainScores, capabilityAssessments, indicatorAssessments, hierarchyMap, documentNames, frameworkOverview)

	// Determine evidence as of date (use start time for now, could be enhanced)
	evidenceAsOfDate := analysisRun.StartTime.Format("2006-01-02")

	// Build final output
	output := &StageAOutput{
		ReportMetadata: ReportMetadata{
			ProjectID:        projectID,
			RunID:            analysisRun.RunID,
			AssessmentDate:   analysisRun.StartTime,
			EvidenceAsOfDate: evidenceAsOfDate,
			FrameworkID:      framework.FrameworkUUID,
			FrameworkName:    framework.Name,
		},
		OverallMaturity:            overallMaturity,
		DomainScoresForSpiderChart: domainScores,
		KeyStrengths:               keyStrengths,
		CriticalAreasFocus:         criticalAreas,
		ContextForSpotlightAndAI:   contextForAI,
	}

	return output, nil
}

// buildHierarchyMap creates a map for easy navigation of the framework hierarchy
func (s *Service) buildHierarchyMap(entity EntityNode) map[string]*EntityNode {
	hierarchyMap := make(map[string]*EntityNode)

	var traverse func(node *EntityNode, parent *EntityNode)
	traverse = func(node *EntityNode, parent *EntityNode) {
		// Create a copy of the node to avoid reference issues
		nodeCopy := *node
		hierarchyMap[node.ID] = &nodeCopy

		// Recursively traverse children (standard structure)
		for i := range node.Children {
			traverse(&node.Children[i], node)
		}
	}

	traverse(&entity, nil)
	return hierarchyMap
}

// buildAlternativeHierarchyMap creates a hierarchy map from the alternative framework structure
func (s *Service) buildAlternativeHierarchyMap(content AlternativeFrameworkContent) (map[string]*EntityNode, []string) {
	hierarchyMap := make(map[string]*EntityNode)
	var topLevelDomains []string

	// Convert alternative structure to standard EntityNode structure
	var convertGrouping func(grouping GroupingNode, level int) *EntityNode
	convertGrouping = func(grouping GroupingNode, level int) *EntityNode {
		node := &EntityNode{
			ID:          grouping.GroupID,
			Name:        grouping.GroupName,
			Description: grouping.Description,
			Type:        "grouping",
			Children:    []EntityNode{},
		}

		// Add to hierarchy map
		hierarchyMap[grouping.GroupID] = node

		// Track top-level domains (level 1 groupings)
		if level == 1 {
			topLevelDomains = append(topLevelDomains, grouping.GroupName)
		}

		// Convert nested groupings
		for _, subGrouping := range grouping.Groupings {
			childNode := convertGrouping(subGrouping, level+1)
			node.Children = append(node.Children, *childNode)
		}

		// Convert capabilities
		for _, capability := range grouping.Capabilities {
			capNode := &EntityNode{
				ID:          capability.CapabilityID,
				Name:        capability.CapabilityName,
				Description: capability.Description,
				Type:        "capability",
				Children:    []EntityNode{},
			}

			// Add to hierarchy map
			hierarchyMap[capability.CapabilityID] = capNode

			// Convert indicators
			for _, indicator := range capability.Indicators {
				indNode := EntityNode{
					ID:          indicator.IndicatorID,
					Name:        indicator.IndicatorName,
					Description: indicator.Description,
					Type:        "indicator",
					Levels:      indicator.Levels,
				}

				// Add to hierarchy map
				hierarchyMap[indicator.IndicatorID] = &indNode
				capNode.Children = append(capNode.Children, indNode)
			}

			node.Children = append(node.Children, *capNode)
		}

		return node
	}

	// Process all top-level groupings
	for _, grouping := range content.Entity.Groupings {
		convertGrouping(grouping, 1)
	}

	return hierarchyMap, topLevelDomains
}

// calculateOverallMaturity calculates the median score of all leaf capabilities
func (s *Service) calculateOverallMaturity(capabilityAssessments []CapabilityAssessment, hierarchyMap map[string]*EntityNode) OverallMaturityData {
	// Get all leaf capability scores
	var leafScores []float64

	for _, assessment := range capabilityAssessments {
		if node, exists := hierarchyMap[assessment.CapID]; exists {
			// Check if this is a leaf capability (no children or children are indicators)
			isLeaf := len(node.Children) == 0
			if !isLeaf {
				// Check if all children are indicators
				allChildrenAreIndicators := true
				for _, child := range node.Children {
					if child.Type != "indicator" {
						allChildrenAreIndicators = false
						break
					}
				}
				isLeaf = allChildrenAreIndicators
			}

			if isLeaf {
				leafScores = append(leafScores, float64(assessment.AggregatedLevel))
			}
		}
	}

	// Calculate median
	if len(leafScores) == 0 {
		return OverallMaturityData{Score: 0.0, ScaleMax: 5.0}
	}

	sort.Float64s(leafScores)
	var median float64
	n := len(leafScores)
	if n%2 == 0 {
		median = (leafScores[n/2-1] + leafScores[n/2]) / 2
	} else {
		median = leafScores[n/2]
	}

	return OverallMaturityData{
		Score:    median,
		ScaleMax: 5.0,
	}
}

// calculateDomainScores calculates average scores for top-level domains
func (s *Service) calculateDomainScores(rootEntity EntityNode, capabilityAssessments []CapabilityAssessment, hierarchyMap map[string]*EntityNode) []DomainScore {
	var domainScores []DomainScore

	// Create a map for quick capability lookup
	capabilityScores := make(map[string]int)
	for _, assessment := range capabilityAssessments {
		capabilityScores[assessment.CapID] = assessment.AggregatedLevel
	}

	// Process each top-level domain (child of root entity)
	for _, domain := range rootEntity.Children {
		if domain.Type == "grouping" {
			leafCapabilities := s.findLeafCapabilities(domain, hierarchyMap)

			if len(leafCapabilities) > 0 {
				var totalScore float64
				var count int

				for _, capID := range leafCapabilities {
					if score, exists := capabilityScores[capID]; exists {
						totalScore += float64(score)
						count++
					}
				}

				if count > 0 {
					averageScore := totalScore / float64(count)
					domainScores = append(domainScores, DomainScore{
						DomainName:      domain.Name,
						AverageScore:    averageScore,
						CapabilityCount: count,
					})
				}
			}
		}
	}

	// Sort by average score descending
	sort.Slice(domainScores, func(i, j int) bool {
		return domainScores[i].AverageScore > domainScores[j].AverageScore
	})

	return domainScores
}

// calculateDomainScoresFromHierarchy calculates domain scores using the hierarchy map and domain names
func (s *Service) calculateDomainScoresFromHierarchy(topLevelDomains []string, capabilityAssessments []CapabilityAssessment, hierarchyMap map[string]*EntityNode) []DomainScore {
	var domainScores []DomainScore

	// Create a map for quick capability lookup
	capabilityScores := make(map[string]int)
	for _, assessment := range capabilityAssessments {
		capabilityScores[assessment.CapID] = assessment.AggregatedLevel
	}

	// Process each top-level domain
	for _, domainName := range topLevelDomains {
		// Find the domain node by name
		var domainNode *EntityNode
		for _, node := range hierarchyMap {
			if node.Name == domainName && node.Type == "grouping" {
				domainNode = node
				break
			}
		}

		if domainNode != nil {
			leafCapabilities := s.findLeafCapabilitiesFromNode(*domainNode, hierarchyMap)

			if len(leafCapabilities) > 0 {
				var totalScore float64
				var count int

				for _, capID := range leafCapabilities {
					if score, exists := capabilityScores[capID]; exists {
						totalScore += float64(score)
						count++
					}
				}

				if count > 0 {
					averageScore := totalScore / float64(count)
					domainScores = append(domainScores, DomainScore{
						DomainName:      domainName,
						AverageScore:    averageScore,
						CapabilityCount: count,
					})
				}
			}
		}
	}

	// Sort by average score descending
	sort.Slice(domainScores, func(i, j int) bool {
		return domainScores[i].AverageScore > domainScores[j].AverageScore
	})

	return domainScores
}

// findLeafCapabilitiesFromNode finds all leaf capabilities under a given node using hierarchy map
func (s *Service) findLeafCapabilitiesFromNode(node EntityNode, hierarchyMap map[string]*EntityNode) []string {
	var leafCapabilities []string

	var traverse func(nodeID string)
	traverse = func(nodeID string) {
		if currentNode, exists := hierarchyMap[nodeID]; exists {
			if currentNode.Type == "capability" {
				// Check if this is a leaf capability
				isLeaf := len(currentNode.Children) == 0
				if !isLeaf {
					// Check if all children are indicators
					allChildrenAreIndicators := true
					for _, child := range currentNode.Children {
						if child.Type != "indicator" {
							allChildrenAreIndicators = false
							break
						}
					}
					isLeaf = allChildrenAreIndicators
				}

				if isLeaf {
					leafCapabilities = append(leafCapabilities, nodeID)
				}
			}

			// Recursively traverse children
			for _, child := range currentNode.Children {
				traverse(child.ID)
			}
		}
	}

	traverse(node.ID)
	return leafCapabilities
}

// findLeafCapabilities finds all leaf capabilities under a given node
func (s *Service) findLeafCapabilities(node EntityNode, hierarchyMap map[string]*EntityNode) []string {
	var leafCapabilities []string

	var traverse func(nodeID string)
	traverse = func(nodeID string) {
		if currentNode, exists := hierarchyMap[nodeID]; exists {
			if currentNode.Type == "capability" {
				// Check if this is a leaf capability
				isLeaf := len(currentNode.Children) == 0
				if !isLeaf {
					// Check if all children are indicators
					allChildrenAreIndicators := true
					for _, child := range currentNode.Children {
						if child.Type != "indicator" {
							allChildrenAreIndicators = false
							break
						}
					}
					isLeaf = allChildrenAreIndicators
				}

				if isLeaf {
					leafCapabilities = append(leafCapabilities, nodeID)
				}
			}

			// Recursively traverse children
			for _, child := range currentNode.Children {
				traverse(child.ID)
			}
		}
	}

	traverse(node.ID)
	return leafCapabilities
}

// identifyKeyStrengths identifies the top N domains as key strengths
func (s *Service) identifyKeyStrengths(domainScores []DomainScore, capabilityAssessments []CapabilityAssessment, indicatorAssessments []IndicatorAssessment, hierarchyMap map[string]*EntityNode, topN int) []KeyStrength {
	var keyStrengths []KeyStrength

	// Take top N domains
	for i := 0; i < topN && i < len(domainScores); i++ {
		domain := domainScores[i]

		// Build AI insight context for this strength
		aiContext := s.buildAIInsightContextForStrength(domain, capabilityAssessments, indicatorAssessments, hierarchyMap)

		keyStrengths = append(keyStrengths, KeyStrength{
			DomainName:       domain.DomainName,
			AverageScore:     domain.AverageScore,
			AIInsightContext: aiContext,
		})
	}

	return keyStrengths
}

// identifyCriticalAreas identifies the bottom N domains as critical areas
func (s *Service) identifyCriticalAreas(domainScores []DomainScore, capabilityAssessments []CapabilityAssessment, indicatorAssessments []IndicatorAssessment, hierarchyMap map[string]*EntityNode, bottomN int) []CriticalArea {
	var criticalAreas []CriticalArea

	// Take bottom N domains
	start := len(domainScores) - bottomN
	if start < 0 {
		start = 0
	}

	for i := start; i < len(domainScores); i++ {
		domain := domainScores[i]

		// Build AI insight context for this critical area
		aiContext := s.buildAIInsightContextForCriticalArea(domain, capabilityAssessments, indicatorAssessments, hierarchyMap)

		criticalAreas = append(criticalAreas, CriticalArea{
			DomainName:       domain.DomainName,
			AverageScore:     domain.AverageScore,
			AIInsightContext: aiContext,
		})
	}

	return criticalAreas
}

// buildAIInsightContextForStrength builds AI context for a strength domain
func (s *Service) buildAIInsightContextForStrength(domain DomainScore, capabilityAssessments []CapabilityAssessment, indicatorAssessments []IndicatorAssessment, hierarchyMap map[string]*EntityNode) AIInsightContext {
	// Find contributing capabilities (top performers in this domain)
	contributingCapabilities := s.findContributingCapabilities(domain.DomainName, capabilityAssessments, hierarchyMap, true, 3)

	// Find positive indicators summary
	positiveIndicators := s.findPositiveIndicators(domain.DomainName, indicatorAssessments, hierarchyMap, 5)

	return AIInsightContext{
		ContributingCapabilities:  contributingCapabilities,
		PositiveIndicatorsSummary: positiveIndicators,
	}
}

// buildAIInsightContextForCriticalArea builds AI context for a critical area domain
func (s *Service) buildAIInsightContextForCriticalArea(domain DomainScore, capabilityAssessments []CapabilityAssessment, indicatorAssessments []IndicatorAssessment, hierarchyMap map[string]*EntityNode) AIInsightContext {
	// Find contributing capabilities (worst performers in this domain)
	contributingCapabilities := s.findContributingCapabilities(domain.DomainName, capabilityAssessments, hierarchyMap, false, 3)

	// Find bottleneck indicators with detailed level comparison
	bottleneckIndicators := s.findBottleneckIndicators(domain.DomainName, indicatorAssessments, hierarchyMap, 5)

	return AIInsightContext{
		ContributingCapabilities:    contributingCapabilities,
		BottleneckIndicatorsDetails: bottleneckIndicators,
	}
}

// findContributingCapabilities finds capabilities that contribute to a domain's score
func (s *Service) findContributingCapabilities(domainName string, capabilityAssessments []CapabilityAssessment, hierarchyMap map[string]*EntityNode, findBest bool, limit int) []ContributingCapability {
	var capabilities []ContributingCapability

	// Find the domain node
	var domainNode *EntityNode
	for _, node := range hierarchyMap {
		if node.Name == domainName && node.Type == "grouping" {
			domainNode = node
			break
		}
	}

	if domainNode == nil {
		return capabilities
	}

	// Find all capabilities under this domain
	domainCapabilities := s.findLeafCapabilities(*domainNode, hierarchyMap)

	// Create capability score list
	type capabilityScore struct {
		ID          string
		Name        string
		Score       float64
		Description string
	}

	var capScores []capabilityScore
	for _, capID := range domainCapabilities {
		for _, assessment := range capabilityAssessments {
			if assessment.CapID == capID {
				if node, exists := hierarchyMap[capID]; exists {
					capScores = append(capScores, capabilityScore{
						ID:          capID,
						Name:        node.Name,
						Score:       float64(assessment.AggregatedLevel),
						Description: node.Description,
					})
				}
				break
			}
		}
	}

	// Sort by score
	if findBest {
		sort.Slice(capScores, func(i, j int) bool {
			return capScores[i].Score > capScores[j].Score
		})
	} else {
		sort.Slice(capScores, func(i, j int) bool {
			return capScores[i].Score < capScores[j].Score
		})
	}

	// Take top/bottom N
	for i := 0; i < limit && i < len(capScores); i++ {
		cap := capScores[i]
		capabilities = append(capabilities, ContributingCapability{
			ID:          cap.ID,
			Name:        cap.Name,
			Score:       cap.Score,
			Description: cap.Description,
		})
	}

	return capabilities
}

// findPositiveIndicators finds high-scoring indicators in a domain
func (s *Service) findPositiveIndicators(domainName string, indicatorAssessments []IndicatorAssessment, hierarchyMap map[string]*EntityNode, limit int) []IndicatorSummary {
	var indicators []IndicatorSummary

	// Find the domain node
	var domainNode *EntityNode
	for _, node := range hierarchyMap {
		if node.Name == domainName && node.Type == "grouping" {
			domainNode = node
			break
		}
	}

	if domainNode == nil {
		return indicators
	}

	// Find all indicators under this domain
	domainIndicators := s.findAllIndicators(*domainNode, hierarchyMap)

	// Create indicator score list
	type indicatorScore struct {
		Name      string
		Score     int
		Reasoning string
	}

	var indScores []indicatorScore
	for _, indID := range domainIndicators {
		for _, assessment := range indicatorAssessments {
			if assessment.IndID == indID && assessment.AssessedLevel >= 4 { // High scores only
				if node, exists := hierarchyMap[indID]; exists {
					reasoning := ""
					if assessment.Reasoning != nil {
						reasoning = *assessment.Reasoning
					}
					indScores = append(indScores, indicatorScore{
						Name:      node.Name,
						Score:     assessment.AssessedLevel,
						Reasoning: reasoning,
					})
				}
				break
			}
		}
	}

	// Sort by score descending
	sort.Slice(indScores, func(i, j int) bool {
		return indScores[i].Score > indScores[j].Score
	})

	// Take top N
	for i := 0; i < limit && i < len(indScores); i++ {
		ind := indScores[i]
		indicators = append(indicators, IndicatorSummary{
			Name:      ind.Name,
			Score:     ind.Score,
			Reasoning: ind.Reasoning,
		})
	}

	return indicators
}

// findAllIndicators finds all indicators under a given node
func (s *Service) findAllIndicators(node EntityNode, hierarchyMap map[string]*EntityNode) []string {
	var indicators []string

	var traverse func(nodeID string)
	traverse = func(nodeID string) {
		if currentNode, exists := hierarchyMap[nodeID]; exists {
			if currentNode.Type == "indicator" {
				indicators = append(indicators, nodeID)
			}

			// Recursively traverse children
			for _, child := range currentNode.Children {
				traverse(child.ID)
			}
		}
	}

	traverse(node.ID)
	return indicators
}

// findBottleneckIndicators finds low-scoring indicators with level comparison details
func (s *Service) findBottleneckIndicators(domainName string, indicatorAssessments []IndicatorAssessment, hierarchyMap map[string]*EntityNode, limit int) []BottleneckIndicatorDetail {
	var bottlenecks []BottleneckIndicatorDetail

	// Find the domain node
	var domainNode *EntityNode
	for _, node := range hierarchyMap {
		if node.Name == domainName && node.Type == "grouping" {
			domainNode = node
			break
		}
	}

	if domainNode == nil {
		return bottlenecks
	}

	// Find all indicators under this domain
	domainIndicators := s.findAllIndicators(*domainNode, hierarchyMap)

	// Create bottleneck list
	type bottleneckInfo struct {
		CapabilityName   string
		IndicatorName    string
		CurrentScore     int
		CurrentReasoning string
		IndicatorNode    *EntityNode
	}

	var bottleneckList []bottleneckInfo
	for _, indID := range domainIndicators {
		for _, assessment := range indicatorAssessments {
			if assessment.IndID == indID && assessment.AssessedLevel <= 2 { // Low scores only
				if indNode, exists := hierarchyMap[indID]; exists {
					// Find parent capability
					capabilityName := "Unknown Capability"
					for _, capAssessment := range indicatorAssessments {
						if capAssessment.CapID != "" {
							if capNode, capExists := hierarchyMap[capAssessment.CapID]; capExists {
								// Check if this indicator belongs to this capability
								for _, child := range capNode.Children {
									if child.ID == indID {
										capabilityName = capNode.Name
										break
									}
								}
							}
						}
					}

					reasoning := ""
					if assessment.Reasoning != nil {
						reasoning = *assessment.Reasoning
					}

					bottleneckList = append(bottleneckList, bottleneckInfo{
						CapabilityName:   capabilityName,
						IndicatorName:    indNode.Name,
						CurrentScore:     assessment.AssessedLevel,
						CurrentReasoning: reasoning,
						IndicatorNode:    indNode,
					})
				}
				break
			}
		}
	}

	// Sort by current score ascending (worst first)
	sort.Slice(bottleneckList, func(i, j int) bool {
		return bottleneckList[i].CurrentScore < bottleneckList[j].CurrentScore
	})

	// Take top N and build detailed bottleneck info
	for i := 0; i < limit && i < len(bottleneckList); i++ {
		bottleneck := bottleneckList[i]

		// Find next level information
		nextLevelTarget := bottleneck.CurrentScore + 1
		nextLevelDescription := ""
		nextLevelCriteria := ""

		if len(bottleneck.IndicatorNode.Levels) > nextLevelTarget-1 {
			nextLevel := bottleneck.IndicatorNode.Levels[nextLevelTarget-1]
			nextLevelDescription = nextLevel.Description
			nextLevelCriteria = nextLevel.Criteria
		}

		bottlenecks = append(bottlenecks, BottleneckIndicatorDetail{
			CapabilityName:       bottleneck.CapabilityName,
			IndicatorName:        bottleneck.IndicatorName,
			CurrentScore:         bottleneck.CurrentScore,
			CurrentReasoning:     bottleneck.CurrentReasoning,
			NextLevelTarget:      nextLevelTarget,
			NextLevelDescription: nextLevelDescription,
			NextLevelCriteria:    nextLevelCriteria,
		})
	}

	return bottlenecks
}

// buildContextForAI builds the comprehensive context for AI processing
func (s *Service) buildContextForAI(domainScores []DomainScore, capabilityAssessments []CapabilityAssessment, indicatorAssessments []IndicatorAssessment, hierarchyMap map[string]*EntityNode, documentNames DocumentNamesList, frameworkOverview FrameworkOverview) ContextForAI {
	// Build all domain scores summary
	var allDomainScores []DomainScoreSummary
	for _, domain := range domainScores {
		allDomainScores = append(allDomainScores, DomainScoreSummary{
			DomainName:   domain.DomainName,
			AverageScore: domain.AverageScore,
		})
	}

	// Build all leaf capability scores
	var allLeafCapabilities []LeafCapabilityScore
	for _, assessment := range capabilityAssessments {
		if node, exists := hierarchyMap[assessment.CapID]; exists {
			// Check if this is a leaf capability
			isLeaf := len(node.Children) == 0
			if !isLeaf {
				allChildrenAreIndicators := true
				for _, child := range node.Children {
					if child.Type != "indicator" {
						allChildrenAreIndicators = false
						break
					}
				}
				isLeaf = allChildrenAreIndicators
			}

			if isLeaf {
				// Find parent domain
				domainName := s.findParentDomainFromNames(assessment.CapID, hierarchyMap, frameworkOverview.TopLevelGroupNames)

				allLeafCapabilities = append(allLeafCapabilities, LeafCapabilityScore{
					ID:          assessment.CapID,
					Name:        node.Name,
					Score:       float64(assessment.AggregatedLevel),
					Domain:      domainName,
					Description: node.Description,
				})
			}
		}
	}

	// Find key low-scoring capabilities with reasoning
	var lowScoringCapabilities []LowScoringCapabilityDetail

	// Sort capabilities by score
	sort.Slice(allLeafCapabilities, func(i, j int) bool {
		return allLeafCapabilities[i].Score < allLeafCapabilities[j].Score
	})

	// Take bottom 10 capabilities
	limit := 10
	if len(allLeafCapabilities) < limit {
		limit = len(allLeafCapabilities)
	}

	for i := 0; i < limit; i++ {
		cap := allLeafCapabilities[i]

		// Find indicators for this capability
		var indicators []IndicatorWithReasoning
		if capNode, exists := hierarchyMap[cap.ID]; exists {
			for _, child := range capNode.Children {
				if child.Type == "indicator" {
					for _, assessment := range indicatorAssessments {
						if assessment.IndID == child.ID {
							reasoning := ""
							if assessment.Reasoning != nil {
								reasoning = *assessment.Reasoning
							}
							indicators = append(indicators, IndicatorWithReasoning{
								ID:        child.ID,
								Name:      child.Name,
								Score:     assessment.AssessedLevel,
								Reasoning: reasoning,
							})
							break
						}
					}
				}
			}
		}

		lowScoringCapabilities = append(lowScoringCapabilities, LowScoringCapabilityDetail{
			ID:         cap.ID,
			Name:       cap.Name,
			Score:      cap.Score,
			Domain:     cap.Domain,
			Indicators: indicators,
		})
	}

	// Framework overview is already provided as parameter

	return ContextForAI{
		AllDomainScoresSummary:                 allDomainScores,
		AllLeafCapabilityScores:                allLeafCapabilities,
		KeyLowScoringCapabilitiesWithReasoning: lowScoringCapabilities,
		DocumentNamesProcessed:                 []string(documentNames),
		FrameworkOverview:                      frameworkOverview,
	}
}

// findParentDomain finds the top-level domain (grouping) that contains a given capability
func (s *Service) findParentDomain(capabilityID string, hierarchyMap map[string]*EntityNode, rootEntity EntityNode) string {
	// Traverse from root to find which top-level domain contains this capability
	for _, domain := range rootEntity.Children {
		if domain.Type == "grouping" {
			if s.containsCapability(domain, capabilityID, hierarchyMap) {
				return domain.Name
			}
		}
	}
	return "Unknown Domain"
}

// containsCapability checks if a domain contains a specific capability
func (s *Service) containsCapability(domain EntityNode, capabilityID string, hierarchyMap map[string]*EntityNode) bool {
	var traverse func(nodeID string) bool
	traverse = func(nodeID string) bool {
		if nodeID == capabilityID {
			return true
		}

		if node, exists := hierarchyMap[nodeID]; exists {
			for _, child := range node.Children {
				if traverse(child.ID) {
					return true
				}
			}
		}
		return false
	}

	return traverse(domain.ID)
}

// findParentDomainFromNames finds the top-level domain that contains a given capability using domain names
func (s *Service) findParentDomainFromNames(capabilityID string, hierarchyMap map[string]*EntityNode, topLevelDomainNames []string) string {
	// For each top-level domain name, find the corresponding node and check if it contains the capability
	for _, domainName := range topLevelDomainNames {
		// Find the domain node by name
		for _, node := range hierarchyMap {
			if node.Name == domainName && node.Type == "grouping" {
				if s.containsCapability(*node, capabilityID, hierarchyMap) {
					return domainName
				}
				break
			}
		}
	}
	return "Unknown Domain"
}
