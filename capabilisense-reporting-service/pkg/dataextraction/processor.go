package dataextraction

import (
	"fmt"
	"math"
	"sort"
	"time"
)

// DataProcessor handles algorithmic processing of raw assessment data
type DataProcessor struct {
	// Configuration for processing algorithms
	StrengthThreshold  float64 // Minimum score to be considered a strength
	CriticalThreshold  float64 // Maximum score to be considered critical
	TopStrengthsCount  int     // Number of top strengths to identify
	CriticalAreasCount int     // Number of critical areas to identify
}

// NewDataProcessor creates a new data processor with default settings
func NewDataProcessor() *DataProcessor {
	return &DataProcessor{
		StrengthThreshold:  3.5, // Scores >= 3.5 are strengths
		CriticalThreshold:  2.5, // Scores <= 2.5 are critical areas
		TopStrengthsCount:  3,   // Top 3 strengths
		CriticalAreasCount: 3,   // Top 3 critical areas
	}
}

// ProcessAnalysisData performs algorithmic processing on raw analysis data
func (dp *DataProcessor) ProcessAnalysisData(analysisResults *AnalysisResults) (*ProcessedData, error) {
	if analysisResults == nil {
		return nil, fmt.Errorf("analysis results cannot be nil")
	}

	// Calculate overall score
	overallScore := dp.calculateOverallScore(analysisResults.DomainScores, analysisResults.FrameworkData.WeightingScheme)

	// Create domain rankings
	domainRankings := dp.createDomainRankings(analysisResults.DomainScores, analysisResults.FrameworkData.Domains)

	// Identify top strengths
	topStrengths := dp.identifyTopStrengths(domainRankings, analysisResults.FrameworkData.Domains)

	// Identify critical areas
	criticalAreas := dp.identifyCriticalAreas(domainRankings, analysisResults.FrameworkData.Domains)

	// Calculate score distribution
	scoreDistribution := dp.calculateScoreDistribution(analysisResults.DomainScores)

	// Generate comparison metrics (placeholder for now)
	comparisonMetrics := dp.generateComparisonMetrics(analysisResults)

	// Create processing metadata
	processingMetadata := ProcessingMetadata{
		ProcessedAt:      time.Now(),
		ProcessorVersion: "1.0.0",
		AlgorithmConfig: map[string]interface{}{
			"strength_threshold":   dp.StrengthThreshold,
			"critical_threshold":   dp.CriticalThreshold,
			"top_strengths_count":  dp.TopStrengthsCount,
			"critical_areas_count": dp.CriticalAreasCount,
		},
		QualityMetrics: map[string]float64{
			"data_completeness": dp.calculateDataCompleteness(analysisResults),
			"score_variance":    dp.calculateScoreVariance(analysisResults.DomainScores),
		},
	}

	return &ProcessedData{
		AnalysisResults:    *analysisResults,
		OverallScore:       overallScore,
		DomainRankings:     domainRankings,
		TopStrengths:       topStrengths,
		CriticalAreas:      criticalAreas,
		ScoreDistribution:  scoreDistribution,
		ComparisonMetrics:  comparisonMetrics,
		ProcessingMetadata: processingMetadata,
	}, nil
}

// calculateOverallScore computes the weighted average of domain scores
func (dp *DataProcessor) calculateOverallScore(domainScores map[string]float64, weights map[string]float64) float64 {
	var weightedSum, totalWeight float64

	for domain, score := range domainScores {
		weight := weights[domain]
		if weight == 0 {
			weight = 1.0 / float64(len(domainScores)) // Equal weight if not specified
		}
		weightedSum += score * weight
		totalWeight += weight
	}

	if totalWeight == 0 {
		return 0
	}

	return weightedSum / totalWeight
}

// createDomainRankings creates ranked list of domains by score
func (dp *DataProcessor) createDomainRankings(domainScores map[string]float64, domains []Domain) []DomainRanking {
	rankings := make([]DomainRanking, 0, len(domainScores))

	// Create domain name mapping
	domainNames := make(map[string]string)
	for _, domain := range domains {
		domainNames[domain.ID] = domain.Name
	}

	// Create rankings
	for domainID, score := range domainScores {
		rankings = append(rankings, DomainRanking{
			DomainID:   domainID,
			DomainName: domainNames[domainID],
			Score:      score,
		})
	}

	// Sort by score (descending)
	sort.Slice(rankings, func(i, j int) bool {
		return rankings[i].Score > rankings[j].Score
	})

	// Assign ranks and percentiles
	for i := range rankings {
		rankings[i].Rank = i + 1
		rankings[i].Percentile = float64(len(rankings)-i) / float64(len(rankings)) * 100
	}

	return rankings
}

// identifyTopStrengths identifies domains that are strengths
func (dp *DataProcessor) identifyTopStrengths(rankings []DomainRanking, domains []Domain) []DomainInsight {
	insights := make([]DomainInsight, 0)

	// Create domain mapping for additional info
	domainMap := make(map[string]Domain)
	for _, domain := range domains {
		domainMap[domain.ID] = domain
	}

	count := 0
	for _, ranking := range rankings {
		if ranking.Score >= dp.StrengthThreshold && count < dp.TopStrengthsCount {
			domain := domainMap[ranking.DomainID]
			insights = append(insights, DomainInsight{
				DomainID:   ranking.DomainID,
				DomainName: ranking.DomainName,
				Score:      ranking.Score,
				KeyFindings: []string{
					fmt.Sprintf("High performance in %s (Score: %.1f)", ranking.DomainName, ranking.Score),
					fmt.Sprintf("Ranked #%d among all domains", ranking.Rank),
				},
				DataPoints: []string{
					fmt.Sprintf("Score: %.1f/5.0", ranking.Score),
					fmt.Sprintf("Percentile: %.0f%%", ranking.Percentile),
				},
				Metadata: map[string]interface{}{
					"domain_description": domain.Description,
					"rank":               ranking.Rank,
					"percentile":         ranking.Percentile,
				},
			})
			count++
		}
	}

	return insights
}

// identifyCriticalAreas identifies domains that need attention
func (dp *DataProcessor) identifyCriticalAreas(rankings []DomainRanking, domains []Domain) []DomainInsight {
	insights := make([]DomainInsight, 0)

	// Create domain mapping for additional info
	domainMap := make(map[string]Domain)
	for _, domain := range domains {
		domainMap[domain.ID] = domain
	}

	// Sort by score (ascending) to get lowest scores first
	criticalRankings := make([]DomainRanking, len(rankings))
	copy(criticalRankings, rankings)
	sort.Slice(criticalRankings, func(i, j int) bool {
		return criticalRankings[i].Score < criticalRankings[j].Score
	})

	count := 0
	for _, ranking := range criticalRankings {
		if ranking.Score <= dp.CriticalThreshold && count < dp.CriticalAreasCount {
			domain := domainMap[ranking.DomainID]
			insights = append(insights, DomainInsight{
				DomainID:   ranking.DomainID,
				DomainName: ranking.DomainName,
				Score:      ranking.Score,
				KeyFindings: []string{
					fmt.Sprintf("Improvement opportunity in %s (Score: %.1f)", ranking.DomainName, ranking.Score),
					fmt.Sprintf("Below critical threshold of %.1f", dp.CriticalThreshold),
				},
				DataPoints: []string{
					fmt.Sprintf("Score: %.1f/5.0", ranking.Score),
					fmt.Sprintf("Gap to target: %.1f points", dp.StrengthThreshold-ranking.Score),
				},
				Metadata: map[string]interface{}{
					"domain_description": domain.Description,
					"rank":               ranking.Rank,
					"improvement_needed": dp.StrengthThreshold - ranking.Score,
				},
			})
			count++
		}
	}

	return insights
}

// calculateScoreDistribution creates a distribution of scores
func (dp *DataProcessor) calculateScoreDistribution(domainScores map[string]float64) map[string]int {
	distribution := map[string]int{
		"1.0-1.9": 0,
		"2.0-2.9": 0,
		"3.0-3.9": 0,
		"4.0-4.9": 0,
		"5.0":     0,
	}

	for _, score := range domainScores {
		switch {
		case score >= 1.0 && score < 2.0:
			distribution["1.0-1.9"]++
		case score >= 2.0 && score < 3.0:
			distribution["2.0-2.9"]++
		case score >= 3.0 && score < 4.0:
			distribution["3.0-3.9"]++
		case score >= 4.0 && score < 5.0:
			distribution["4.0-4.9"]++
		case score == 5.0:
			distribution["5.0"]++
		}
	}

	return distribution
}

// generateComparisonMetrics generates comparison metrics (placeholder implementation)
func (dp *DataProcessor) generateComparisonMetrics(analysisResults *AnalysisResults) map[string]interface{} {
	return map[string]interface{}{
		"benchmark_comparison": "Not available",
		"industry_average":     "Not available",
		"peer_comparison":      "Not available",
		"historical_trend":     "Not available",
		"maturity_level":       dp.determineMaturityLevel(analysisResults.DomainScores),
	}
}

// calculateDataCompleteness calculates the completeness of the data
func (dp *DataProcessor) calculateDataCompleteness(analysisResults *AnalysisResults) float64 {
	totalFields := 5.0 // ProjectID, RunID, OrganizationID, DomainScores, FrameworkData
	completedFields := 0.0

	if analysisResults.ProjectID != "" {
		completedFields++
	}
	if analysisResults.RunID != "" {
		completedFields++
	}
	if analysisResults.OrganizationID != "" {
		completedFields++
	}
	if len(analysisResults.DomainScores) > 0 {
		completedFields++
	}
	if analysisResults.FrameworkData.FrameworkID != "" {
		completedFields++
	}

	return completedFields / totalFields
}

// calculateScoreVariance calculates the variance in domain scores
func (dp *DataProcessor) calculateScoreVariance(domainScores map[string]float64) float64 {
	if len(domainScores) == 0 {
		return 0
	}

	// Calculate mean
	var sum float64
	for _, score := range domainScores {
		sum += score
	}
	mean := sum / float64(len(domainScores))

	// Calculate variance
	var variance float64
	for _, score := range domainScores {
		variance += math.Pow(score-mean, 2)
	}
	variance /= float64(len(domainScores))

	return variance
}

// determineMaturityLevel determines the overall maturity level based on scores
func (dp *DataProcessor) determineMaturityLevel(domainScores map[string]float64) string {
	var sum float64
	for _, score := range domainScores {
		sum += score
	}
	average := sum / float64(len(domainScores))

	switch {
	case average >= 4.5:
		return "Advanced"
	case average >= 3.5:
		return "Developing"
	case average >= 2.5:
		return "Basic"
	case average >= 1.5:
		return "Initial"
	default:
		return "Incomplete"
	}
}
