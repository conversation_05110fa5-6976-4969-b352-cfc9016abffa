package dataextraction

import "time"

// AnalysisResults represents the raw assessment data from the database
type AnalysisResults struct {
	ProjectID      string                 `json:"project_id"`
	RunID          string                 `json:"run_id"`
	OrganizationID string                 `json:"organization_id"`
	AssessmentDate time.Time              `json:"assessment_date"`
	EvidenceDate   time.Time              `json:"evidence_date"`
	DomainScores   map[string]float64     `json:"domain_scores"`
	FrameworkData  FrameworkData          `json:"framework_data"`
	RawResponses   []AssessmentResponse   `json:"raw_responses"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// FrameworkData represents the framework structure and scoring
type FrameworkData struct {
	FrameworkID     string                 `json:"framework_id"`
	Version         string                 `json:"version"`
	Domains         []Domain               `json:"domains"`
	ScoringRules    map[string]interface{} `json:"scoring_rules"`
	WeightingScheme map[string]float64     `json:"weighting_scheme"`
}

// Domain represents a capability domain in the framework
type Domain struct {
	ID          string     `json:"id"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Weight      float64    `json:"weight"`
	Categories  []Category `json:"categories"`
	Score       float64    `json:"score"`
}

// Category represents a category within a domain
type Category struct {
	ID          string     `json:"id"`
	Name        string     `json:"name"`
	Description string     `json:"description"`
	Weight      float64    `json:"weight"`
	Questions   []Question `json:"questions"`
	Score       float64    `json:"score"`
}

// Question represents an assessment question
type Question struct {
	ID       string                 `json:"id"`
	Text     string                 `json:"text"`
	Type     string                 `json:"type"` // "multiple_choice", "scale", "text"
	Options  []string               `json:"options,omitempty"`
	Weight   float64                `json:"weight"`
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// AssessmentResponse represents a response to an assessment question
type AssessmentResponse struct {
	QuestionID   string                 `json:"question_id"`
	ResponseText string                 `json:"response_text"`
	Score        float64                `json:"score"`
	Timestamp    time.Time              `json:"timestamp"`
	UserID       string                 `json:"user_id,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// ProcessedData represents data after algorithmic processing
type ProcessedData struct {
	AnalysisResults    AnalysisResults        `json:"analysis_results"`
	OverallScore       float64                `json:"overall_score"`
	DomainRankings     []DomainRanking        `json:"domain_rankings"`
	TopStrengths       []DomainInsight        `json:"top_strengths"`
	CriticalAreas      []DomainInsight        `json:"critical_areas"`
	ScoreDistribution  map[string]int         `json:"score_distribution"`
	ComparisonMetrics  map[string]interface{} `json:"comparison_metrics"`
	ProcessingMetadata ProcessingMetadata     `json:"processing_metadata"`
}

// DomainRanking represents a domain's ranking and score
type DomainRanking struct {
	DomainID   string  `json:"domain_id"`
	DomainName string  `json:"domain_name"`
	Score      float64 `json:"score"`
	Rank       int     `json:"rank"`
	Percentile float64 `json:"percentile"`
}

// DomainInsight represents insights about a specific domain
type DomainInsight struct {
	DomainID    string                 `json:"domain_id"`
	DomainName  string                 `json:"domain_name"`
	Score       float64                `json:"score"`
	KeyFindings []string               `json:"key_findings"`
	DataPoints  []string               `json:"data_points"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ProcessingMetadata contains metadata about the data processing
type ProcessingMetadata struct {
	ProcessedAt      time.Time              `json:"processed_at"`
	ProcessorVersion string                 `json:"processor_version"`
	AlgorithmConfig  map[string]interface{} `json:"algorithm_config"`
	QualityMetrics   map[string]float64     `json:"quality_metrics"`
}
