package dataextraction

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// MockRepository is a mock implementation of the Repository interface
type MockRepository struct {
	mock.Mock
}

func (m *MockRepository) GetLatestAnalysisRun(projectID string) (*AnalysisRun, error) {
	args := m.Called(projectID)
	return args.Get(0).(*AnalysisRun), args.Error(1)
}

func (m *MockRepository) GetAnalysisRunByID(projectID string, runID string) (*AnalysisRun, error) {
	args := m.Called(projectID, runID)
	return args.Get(0).(*AnalysisRun), args.Error(1)
}

func (m *MockRepository) GetFrameworkByUUID(frameworkUUID string) (*Framework, error) {
	args := m.Called(frameworkUUID)
	return args.Get(0).(*Framework), args.Error(1)
}

func (m *MockRepository) GetIndicatorAssessments(runID string) ([]IndicatorAssessment, error) {
	args := m.Called(runID)
	return args.Get(0).([]IndicatorAssessment), args.Error(1)
}

func (m *MockRepository) GetCapabilityAssessments(runID string) ([]CapabilityAssessment, error) {
	args := m.Called(runID)
	return args.Get(0).([]CapabilityAssessment), args.Error(1)
}

func (m *MockRepository) GetProcessedDocuments(projectID string) ([]ProcessedDocument, error) {
	args := m.Called(projectID)
	return args.Get(0).([]ProcessedDocument), args.Error(1)
}

func (m *MockRepository) GetDocumentHeads(projectID string) ([]DocumentHead, error) {
	args := m.Called(projectID)
	return args.Get(0).([]DocumentHead), args.Error(1)
}

func (m *MockRepository) GetCachedInsight(projectID, runID, insightType string) (*CachedInsight, error) {
	args := m.Called(projectID, runID, insightType)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*CachedInsight), args.Error(1)
}

func (m *MockRepository) SaveCachedInsight(insight *CachedInsight) error {
	args := m.Called(insight)
	return args.Error(0)
}

func (m *MockRepository) GetCachedStageBOutput(projectID, runID string) (*CachedStageBOutput, error) {
	args := m.Called(projectID, runID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*CachedStageBOutput), args.Error(1)
}

func (m *MockRepository) SaveCachedStageBOutput(output *CachedStageBOutput) error {
	args := m.Called(output)
	return args.Error(0)
}

func (m *MockRepository) Close() error {
	args := m.Called()
	return args.Error(0)
}

func TestNewService(t *testing.T) {
	mockRepo := &MockRepository{}
	service := NewService(mockRepo)

	assert.NotNil(t, service)
	assert.Equal(t, mockRepo, service.repo)
}

func TestGenerateReportData(t *testing.T) {
	mockRepo := &MockRepository{}
	service := NewService(mockRepo)

	// Mock data
	projectID := "test-project"
	runID := "test-run-123"
	frameworkUUID := "framework-456"

	analysisRun := &AnalysisRun{
		RunID:         runID,
		ProjectID:     projectID,
		FrameworkUUID: frameworkUUID,
		StartTime:     time.Now(),
		Status:        "COMPLETE",
		DocumentNames: []byte(`["doc1.pdf", "doc2.docx"]`),
	}

	framework := &Framework{
		FrameworkUUID: frameworkUUID,
		Name:          "Test Framework",
		Content: []byte(`{
			"entity": {
				"id": "root",
				"name": "Test Framework",
				"description": "A test framework",
				"type": "entity",
				"children": [
					{
						"id": "domain1",
						"name": "Domain 1",
						"description": "First domain",
						"type": "grouping",
						"children": [
							{
								"id": "cap1",
								"name": "Capability 1",
								"description": "First capability",
								"type": "capability",
								"children": [
									{
										"id": "ind1",
										"name": "Indicator 1",
										"description": "First indicator",
										"type": "indicator",
										"levels": [
											{
												"level": 1,
												"name": "Initial",
												"description": "Basic level",
												"criteria": "Basic criteria"
											},
											{
												"level": 2,
												"name": "Developing",
												"description": "Developing level",
												"criteria": "Developing criteria"
											}
										]
									}
								]
							}
						]
					}
				]
			},
			"term_aliases": {
				"Grouping": "Domain",
				"Capability": "Skill",
				"Indicator": "Metric"
			}
		}`),
	}

	capabilityAssessments := []CapabilityAssessment{
		{
			RunID:           runID,
			CapID:           "cap1",
			AggregatedLevel: 3,
		},
	}

	indicatorAssessments := []IndicatorAssessment{
		{
			RunID:         runID,
			CapID:         "cap1",
			IndID:         "ind1",
			AssessedLevel: 3,
			Reasoning:     stringPtr("Good performance"),
		},
	}

	documents := []ProcessedDocument{
		{
			DocName:   "doc1.pdf",
			ProjectID: projectID,
			Status:    "COMPLETE",
		},
	}

	// Set up mock expectations
	mockRepo.On("GetAnalysisRunByID", projectID, runID).Return(analysisRun, nil)
	mockRepo.On("GetFrameworkByUUID", frameworkUUID).Return(framework, nil)
	mockRepo.On("GetIndicatorAssessments", runID).Return(indicatorAssessments, nil)
	mockRepo.On("GetCapabilityAssessments", runID).Return(capabilityAssessments, nil)
	mockRepo.On("GetProcessedDocuments", projectID).Return(documents, nil)

	// Call the method
	result, err := service.GenerateReportData(projectID, runID)

	// Assertions
	require.NoError(t, err)
	require.NotNil(t, result)

	// Check report metadata
	assert.Equal(t, projectID, result.ReportMetadata.ProjectID)
	assert.Equal(t, runID, result.ReportMetadata.RunID)
	assert.Equal(t, frameworkUUID, result.ReportMetadata.FrameworkID)
	assert.Equal(t, "Test Framework", result.ReportMetadata.FrameworkName)

	// Check overall maturity
	assert.Equal(t, 3.0, result.OverallMaturity.Score)
	assert.Equal(t, 5.0, result.OverallMaturity.ScaleMax)

	// Check domain scores
	assert.Len(t, result.DomainScoresForSpiderChart, 1)
	assert.Equal(t, "Domain 1", result.DomainScoresForSpiderChart[0].DomainName)
	assert.Equal(t, 3.0, result.DomainScoresForSpiderChart[0].AverageScore)

	// Check context for AI
	assert.NotNil(t, result.ContextForSpotlightAndAI)
	assert.Len(t, result.ContextForSpotlightAndAI.DocumentNamesProcessed, 2)
	assert.Contains(t, result.ContextForSpotlightAndAI.DocumentNamesProcessed, "doc1.pdf")
	assert.Contains(t, result.ContextForSpotlightAndAI.DocumentNamesProcessed, "doc2.docx")

	// Verify all mock expectations were met
	mockRepo.AssertExpectations(t)
}

func TestCalculateOverallMaturity(t *testing.T) {
	service := NewService(nil)

	// Test data
	capabilityAssessments := []CapabilityAssessment{
		{CapID: "cap1", AggregatedLevel: 2},
		{CapID: "cap2", AggregatedLevel: 4},
		{CapID: "cap3", AggregatedLevel: 3},
	}

	hierarchyMap := map[string]*EntityNode{
		"cap1": {ID: "cap1", Type: "capability", Children: []EntityNode{{Type: "indicator"}}},
		"cap2": {ID: "cap2", Type: "capability", Children: []EntityNode{{Type: "indicator"}}},
		"cap3": {ID: "cap3", Type: "capability", Children: []EntityNode{{Type: "indicator"}}},
	}

	result := service.calculateOverallMaturity(capabilityAssessments, hierarchyMap)

	// Should be median of [2, 3, 4] = 3
	assert.Equal(t, 3.0, result.Score)
	assert.Equal(t, 5.0, result.ScaleMax)
}

func TestBuildHierarchyMap(t *testing.T) {
	service := NewService(nil)

	entity := EntityNode{
		ID:   "root",
		Name: "Root",
		Type: "entity",
		Children: []EntityNode{
			{
				ID:   "child1",
				Name: "Child 1",
				Type: "grouping",
				Children: []EntityNode{
					{
						ID:   "grandchild1",
						Name: "Grandchild 1",
						Type: "capability",
					},
				},
			},
		},
	}

	hierarchyMap := service.buildHierarchyMap(entity)

	assert.Len(t, hierarchyMap, 3)
	assert.Contains(t, hierarchyMap, "root")
	assert.Contains(t, hierarchyMap, "child1")
	assert.Contains(t, hierarchyMap, "grandchild1")

	assert.Equal(t, "Root", hierarchyMap["root"].Name)
	assert.Equal(t, "Child 1", hierarchyMap["child1"].Name)
	assert.Equal(t, "Grandchild 1", hierarchyMap["grandchild1"].Name)
}

func TestFindLeafCapabilities(t *testing.T) {
	service := NewService(nil)

	hierarchyMap := map[string]*EntityNode{
		"domain1": {
			ID:   "domain1",
			Type: "grouping",
			Children: []EntityNode{
				{ID: "cap1", Type: "capability"},
				{ID: "cap2", Type: "capability"},
			},
		},
		"cap1": {
			ID:       "cap1",
			Type:     "capability",
			Children: []EntityNode{{ID: "ind1", Type: "indicator"}},
		},
		"cap2": {
			ID:       "cap2",
			Type:     "capability",
			Children: []EntityNode{{ID: "ind2", Type: "indicator"}},
		},
	}

	domain := EntityNode{
		ID:   "domain1",
		Type: "grouping",
		Children: []EntityNode{
			{ID: "cap1", Type: "capability"},
			{ID: "cap2", Type: "capability"},
		},
	}

	leafCapabilities := service.findLeafCapabilities(domain, hierarchyMap)

	assert.Len(t, leafCapabilities, 2)
	assert.Contains(t, leafCapabilities, "cap1")
	assert.Contains(t, leafCapabilities, "cap2")
}

// Helper function to create string pointers
func stringPtr(s string) *string {
	return &s
}
