package dataextraction

import "time"

// StageAOutput represents the complete JSON output for Stage A
type StageAOutput struct {
	ReportMetadata              ReportMetadata              `json:"report_metadata"`
	OverallMaturity             OverallMaturityData         `json:"overall_maturity"`
	DomainScoresForSpiderChart  []DomainScore               `json:"domain_scores_for_spider_chart"`
	KeyStrengths                []KeyStrength               `json:"key_strengths"`
	CriticalAreasFocus          []CriticalArea              `json:"critical_areas_focus"`
	ContextForSpotlightAndAI    ContextForAI                `json:"context_for_spotlight_and_solutions_ai"`
}

// ReportMetadata contains metadata about the assessment report
type ReportMetadata struct {
	ProjectID         string    `json:"project_id"`
	RunID             string    `json:"run_id"`
	AssessmentDate    time.Time `json:"assessment_date"`    // From AnalysisResults.start_time
	EvidenceAsOfDate  string    `json:"evidence_as_of_date"` // From document metadata or assessment context
	FrameworkID       string    `json:"framework_id"`
	FrameworkName     string    `json:"framework_name"`
}

// OverallMaturityData contains the overall maturity score
type OverallMaturityData struct {
	Score    float64 `json:"score"`     // Median of all leaf capability scores
	ScaleMax float64 `json:"scale_max"` // Maximum possible score (typically 5.0)
}

// DomainScore represents a top-level domain score for the spider chart
type DomainScore struct {
	DomainName       string  `json:"domain_name"`
	AverageScore     float64 `json:"average_score"`
	CapabilityCount  int     `json:"capability_count"`
}

// KeyStrength represents a top-performing domain
type KeyStrength struct {
	DomainName       string           `json:"domain_name"`
	AverageScore     float64          `json:"average_score"`
	AIInsightContext AIInsightContext `json:"ai_insight_context"`
}

// CriticalArea represents a low-performing domain that needs focus
type CriticalArea struct {
	DomainName       string           `json:"domain_name"`
	AverageScore     float64          `json:"average_score"`
	AIInsightContext AIInsightContext `json:"ai_insight_context"`
}

// AIInsightContext provides data for Stage B to generate insights
type AIInsightContext struct {
	ContributingCapabilities   []ContributingCapability   `json:"contributing_capabilities"`
	PositiveIndicatorsSummary  []IndicatorSummary         `json:"positive_indicators_summary,omitempty"`  // For strengths
	BottleneckIndicatorsDetails []BottleneckIndicatorDetail `json:"bottleneck_indicators_details,omitempty"` // For focus areas
}

// ContributingCapability represents a capability that contributes to a domain's score
type ContributingCapability struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Score       float64 `json:"score"`
	Description string  `json:"description"`
}

// IndicatorSummary represents a high-scoring indicator for strengths
type IndicatorSummary struct {
	Name      string `json:"name"`
	Score     int    `json:"score"`
	Reasoning string `json:"reasoning"`
}

// BottleneckIndicatorDetail represents detailed information about low-scoring indicators
type BottleneckIndicatorDetail struct {
	CapabilityName         string `json:"capability_name"`
	IndicatorName          string `json:"indicator_name"`
	CurrentScore           int    `json:"current_score"`
	CurrentReasoning       string `json:"current_reasoning"`
	NextLevelTarget        int    `json:"next_level_target"`
	NextLevelDescription   string `json:"next_level_description"`
	NextLevelCriteria      string `json:"next_level_criteria"`
}

// ContextForAI provides broader data for holistic AI prompts
type ContextForAI struct {
	AllDomainScoresSummary           []DomainScoreSummary           `json:"all_domain_scores_summary"`
	AllLeafCapabilityScores          []LeafCapabilityScore          `json:"all_leaf_capability_scores"`
	KeyLowScoringCapabilitiesWithReasoning []LowScoringCapabilityDetail `json:"key_low_scoring_capabilities_with_reasoning"`
	DocumentNamesProcessed           []string                       `json:"document_names_processed"`
	FrameworkOverview                FrameworkOverview              `json:"framework_overview"`
}

// DomainScoreSummary represents a simplified domain score
type DomainScoreSummary struct {
	DomainName   string  `json:"domain_name"`
	AverageScore float64 `json:"average_score"`
}

// LeafCapabilityScore represents a leaf capability with its score
type LeafCapabilityScore struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Score       float64 `json:"score"`
	Domain      string  `json:"domain"`
	Description string  `json:"description"`
}

// LowScoringCapabilityDetail represents a low-scoring capability with detailed reasoning
type LowScoringCapabilityDetail struct {
	ID         string                    `json:"id"`
	Name       string                    `json:"name"`
	Score      float64                   `json:"score"`
	Domain     string                    `json:"domain"`
	Indicators []IndicatorWithReasoning  `json:"indicators"`
}

// IndicatorWithReasoning represents an indicator with its score and reasoning
type IndicatorWithReasoning struct {
	ID        string `json:"id"`
	Name      string `json:"name"`
	Score     int    `json:"score"`
	Reasoning string `json:"reasoning"`
}

// FrameworkOverview provides high-level information about the framework
type FrameworkOverview struct {
	EntityName           string            `json:"entity_name"`
	EntityDescription    string            `json:"entity_description"`
	TermAliases          map[string]string `json:"term_aliases"`
	TopLevelGroupNames   []string          `json:"top_level_group_names"`
}
