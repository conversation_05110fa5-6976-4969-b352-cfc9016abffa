package api

import (
	"encoding/json"
	"log"
	"net/http"
	"time"

	"capabilisense-reporting-service/pkg/dataextraction"
)

// <PERSON><PERSON> holds the service dependencies
type <PERSON><PERSON> struct {
	service *dataextraction.Service
}

// NewHandler creates a new API handler
func NewHandler(service *dataextraction.Service) *Handler {
	return &Handler{service: service}
}

// ErrorResponse represents an API error response
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Code    int    `json:"code"`
}

// SuccessResponse represents a successful API response
type SuccessResponse struct {
	Data      interface{} `json:"data"`
	Timestamp time.Time   `json:"timestamp"`
	RequestID string      `json:"request_id,omitempty"`
}

// GenerateReportDataHandler handles the main Stage A API endpoint
func (h *Handler) GenerateReportDataHandler(w http.ResponseWriter, r *http.Request) {
	// Set CORS headers
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type")
	w.Header().Set("Content-Type", "application/json")

	// Handle preflight requests
	if r.Method == "OPTIONS" {
		w.WriteHeader(http.StatusOK)
		return
	}

	// Only allow GET requests
	if r.Method != "GET" {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed", "Only GET requests are supported")
		return
	}

	// Extract query parameters
	projectID := r.URL.Query().Get("project_id")
	runID := r.URL.Query().Get("run_id") // Optional

	// Validate required parameters
	if projectID == "" {
		h.writeErrorResponse(w, http.StatusBadRequest, "Missing required parameter", "project_id is required")
		return
	}

	// Log the request
	log.Printf("Stage A API request: project_id=%s, run_id=%s", projectID, runID)

	// Call the service
	startTime := time.Now()
	reportData, err := h.service.GenerateReportData(projectID, runID)
	processingTime := time.Since(startTime)

	if err != nil {
		log.Printf("Stage A API error: %v (processing time: %v)", err, processingTime)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Failed to generate report data", err.Error())
		return
	}

	// Log success
	log.Printf("Stage A API success: project_id=%s, run_id=%s, processing_time=%v", projectID, runID, processingTime)

	// Write successful response
	h.writeSuccessResponse(w, reportData, "")
}

// HealthCheckHandler handles health check requests
func (h *Handler) HealthCheckHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "GET" {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed", "Only GET requests are supported")
		return
	}

	response := map[string]interface{}{
		"status":    "healthy",
		"service":   "capabilisense-stage-a-api",
		"timestamp": time.Now(),
		"version":   "1.0.0",
	}

	h.writeSuccessResponse(w, response, "")
}

// StatusHandler provides detailed status information
func (h *Handler) StatusHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")

	if r.Method != "GET" {
		h.writeErrorResponse(w, http.StatusMethodNotAllowed, "Method not allowed", "Only GET requests are supported")
		return
	}

	response := map[string]interface{}{
		"service": map[string]interface{}{
			"name":        "CapabiliSense Stage A - Report Data Generation",
			"version":     "1.0.0",
			"description": "Extracts and processes assessment data for AI insight generation",
			"stage":       "A",
		},
		"status": map[string]interface{}{
			"health":    "healthy",
			"timestamp": time.Now(),
			"uptime":    time.Since(time.Now()), // This would be calculated from service start time in a real implementation
		},
		"endpoints": map[string]interface{}{
			"generate_report_data": "/api/v1/report-data",
			"health_check":         "/health",
			"status":               "/status",
		},
		"api_info": map[string]interface{}{
			"version":     "v1",
			"description": "Stage A API for CapabiliSense PDF Reporting Service",
			"parameters": map[string]interface{}{
				"project_id": map[string]interface{}{
					"required":    true,
					"type":        "string",
					"description": "The project identifier",
				},
				"run_id": map[string]interface{}{
					"required":    false,
					"type":        "string",
					"description": "Optional specific run ID. If not provided, uses the latest completed run.",
				},
			},
		},
	}

	h.writeSuccessResponse(w, response, "")
}

// writeErrorResponse writes a standardized error response
func (h *Handler) writeErrorResponse(w http.ResponseWriter, statusCode int, error string, message string) {
	w.WriteHeader(statusCode)

	response := ErrorResponse{
		Error:   error,
		Message: message,
		Code:    statusCode,
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Failed to encode error response: %v", err)
		http.Error(w, "Internal server error", http.StatusInternalServerError)
	}
}

// writeSuccessResponse writes a standardized success response
func (h *Handler) writeSuccessResponse(w http.ResponseWriter, data interface{}, requestID string) {
	w.WriteHeader(http.StatusOK)

	response := SuccessResponse{
		Data:      data,
		Timestamp: time.Now(),
		RequestID: requestID,
	}

	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Failed to encode success response: %v", err)
		h.writeErrorResponse(w, http.StatusInternalServerError, "Encoding error", "Failed to encode response")
	}
}

// SetupRoutes sets up the HTTP routes for the API
func SetupRoutes(service *dataextraction.Service) *http.ServeMux {
	handler := NewHandler(service)
	mux := http.NewServeMux()

	// API routes
	mux.HandleFunc("/api/v1/report-data", handler.GenerateReportDataHandler)

	// Health and status routes
	mux.HandleFunc("/health", handler.HealthCheckHandler)
	mux.HandleFunc("/status", handler.StatusHandler)

	// Root route
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		if r.URL.Path != "/" {
			http.NotFound(w, r)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		response := map[string]interface{}{
			"service":     "CapabiliSense Stage A API",
			"version":     "1.0.0",
			"description": "Report Data Generation Service",
			"endpoints": map[string]string{
				"generate_report_data": "/api/v1/report-data?project_id=<project_id>&run_id=<optional_run_id>",
				"health_check":         "/health",
				"status":               "/status",
			},
			"documentation": "See /status for detailed API information",
		}

		json.NewEncoder(w).Encode(response)
	})

	return mux
}

// LoggingMiddleware logs HTTP requests
func LoggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()

		// Create a response writer wrapper to capture status code
		wrapper := &responseWriter{ResponseWriter: w, statusCode: http.StatusOK}

		// Call the next handler
		next.ServeHTTP(wrapper, r)

		// Log the request
		duration := time.Since(start)
		log.Printf("%s %s %d %v %s", r.Method, r.URL.Path, wrapper.statusCode, duration, r.RemoteAddr)
	})
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}
