package dataextraction

import (
	"database/sql"
	"fmt"

	_ "github.com/mattn/go-sqlite3" // SQLite driver
)

// Repository defines the interface for data access
type Repository interface {
	// GetLatestAnalysisRun gets the most recent analysis run for a project
	GetLatestAnalysisRun(projectID string) (*AnalysisRun, error)

	// GetAnalysisRunByID gets a specific analysis run by run_id
	GetAnalysisRunByID(projectID string, runID string) (*AnalysisRun, error)

	// GetFrameworkByUUID gets a framework by its UUID
	GetFrameworkByUUID(frameworkUUID string) (*Framework, error)

	// GetIndicatorAssessments gets all indicator assessments for a run
	GetIndicatorAssessments(runID string) ([]IndicatorAssessment, error)

	// GetCapabilityAssessments gets all capability assessments for a run
	GetCapabilityAssessments(runID string) ([]CapabilityAssessment, error)

	// GetProcessedDocuments gets all processed documents for a project
	GetProcessedDocuments(projectID string) ([]ProcessedDocument, error)

	// Close closes the repository connection
	Close() error
}

// DBRepository implements Repository using direct database access
type DBRepository struct {
	db *sql.DB
}

// NewDBRepository creates a new database repository
func NewDBRepository(databasePath string) (*DBRepository, error) {
	db, err := sql.Open("sqlite3", databasePath)
	if err != nil {
		return nil, fmt.Errorf("failed to open database: %w", err)
	}

	// Test the connection
	if err := db.Ping(); err != nil {
		return nil, fmt.Errorf("failed to ping database: %w", err)
	}

	return &DBRepository{db: db}, nil
}

// GetLatestAnalysisRun gets the most recent analysis run for a project
func (r *DBRepository) GetLatestAnalysisRun(projectID string) (*AnalysisRun, error) {
	query := `
		SELECT run_id, project_id, task_id, framework_uuid, document_names,
		       status, error_message, start_time, end_time, results_summary,
		       progress_step, progress_percentage
		FROM analysis_runs
		WHERE project_id = ? AND status = 'COMPLETE'
		ORDER BY start_time DESC
		LIMIT 1
	`

	var run AnalysisRun
	var documentNamesStr, resultsSummaryStr sql.NullString

	err := r.db.QueryRow(query, projectID).Scan(
		&run.RunID, &run.ProjectID, &run.TaskID, &run.FrameworkUUID,
		&documentNamesStr, &run.Status, &run.ErrorMessage, &run.StartTime,
		&run.EndTime, &resultsSummaryStr, &run.ProgressStep, &run.ProgressPercentage,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("no completed analysis runs found for project %s", projectID)
		}
		return nil, fmt.Errorf("failed to get latest analysis run: %w", err)
	}

	// Convert string JSON to RawMessage
	if documentNamesStr.Valid {
		run.DocumentNames = []byte(documentNamesStr.String)
	}
	if resultsSummaryStr.Valid {
		run.ResultsSummary = []byte(resultsSummaryStr.String)
	}

	return &run, nil
}

// GetAnalysisRunByID gets a specific analysis run by run_id
func (r *DBRepository) GetAnalysisRunByID(projectID string, runID string) (*AnalysisRun, error) {
	query := `
		SELECT run_id, project_id, task_id, framework_uuid, document_names,
		       status, error_message, start_time, end_time, results_summary,
		       progress_step, progress_percentage
		FROM analysis_runs
		WHERE project_id = ? AND run_id = ?
	`

	var run AnalysisRun
	var documentNamesStr, resultsSummaryStr sql.NullString

	err := r.db.QueryRow(query, projectID, runID).Scan(
		&run.RunID, &run.ProjectID, &run.TaskID, &run.FrameworkUUID,
		&documentNamesStr, &run.Status, &run.ErrorMessage, &run.StartTime,
		&run.EndTime, &resultsSummaryStr, &run.ProgressStep, &run.ProgressPercentage,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("analysis run %s not found for project %s", runID, projectID)
		}
		return nil, fmt.Errorf("failed to get analysis run: %w", err)
	}

	// Convert string JSON to RawMessage
	if documentNamesStr.Valid {
		run.DocumentNames = []byte(documentNamesStr.String)
	}
	if resultsSummaryStr.Valid {
		run.ResultsSummary = []byte(resultsSummaryStr.String)
	}

	return &run, nil
}

// GetFrameworkByUUID gets a framework by its UUID
func (r *DBRepository) GetFrameworkByUUID(frameworkUUID string) (*Framework, error) {
	query := `
		SELECT framework_uuid, project_id, name, description, upload_time, content
		FROM frameworks
		WHERE framework_uuid = ?
	`

	var framework Framework
	var contentStr sql.NullString

	err := r.db.QueryRow(query, frameworkUUID).Scan(
		&framework.FrameworkUUID, &framework.ProjectID, &framework.Name,
		&framework.Description, &framework.UploadTime, &contentStr,
	)

	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("framework %s not found", frameworkUUID)
		}
		return nil, fmt.Errorf("failed to get framework: %w", err)
	}

	// Convert string JSON to RawMessage
	if contentStr.Valid {
		framework.Content = []byte(contentStr.String)
	}

	return &framework, nil
}

// GetIndicatorAssessments gets all indicator assessments for a run
func (r *DBRepository) GetIndicatorAssessments(runID string) ([]IndicatorAssessment, error) {
	query := `
		SELECT assessment_id, run_id, cap_id, ind_id, assessed_level, reasoning,
		       evidence_chunk_ids, model_name, tokens_used, assessment_timestamp
		FROM indicator_assessments
		WHERE run_id = ?
		ORDER BY cap_id, ind_id
	`

	rows, err := r.db.Query(query, runID)
	if err != nil {
		return nil, fmt.Errorf("failed to query indicator assessments: %w", err)
	}
	defer rows.Close()

	var assessments []IndicatorAssessment
	for rows.Next() {
		var assessment IndicatorAssessment
		var evidenceChunkIDsStr sql.NullString

		err := rows.Scan(
			&assessment.AssessmentID, &assessment.RunID, &assessment.CapID,
			&assessment.IndID, &assessment.AssessedLevel, &assessment.Reasoning,
			&evidenceChunkIDsStr, &assessment.ModelName, &assessment.TokensUsed,
			&assessment.AssessmentTimestamp,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan indicator assessment: %w", err)
		}

		// Convert string JSON to RawMessage
		if evidenceChunkIDsStr.Valid {
			assessment.EvidenceChunkIDs = []byte(evidenceChunkIDsStr.String)
		}

		assessments = append(assessments, assessment)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating indicator assessments: %w", err)
	}

	return assessments, nil
}

// GetCapabilityAssessments gets all capability assessments for a run
func (r *DBRepository) GetCapabilityAssessments(runID string) ([]CapabilityAssessment, error) {
	query := `
		SELECT capability_assessment_id, run_id, cap_id, aggregated_level
		FROM capability_assessments
		WHERE run_id = ?
		ORDER BY cap_id
	`

	rows, err := r.db.Query(query, runID)
	if err != nil {
		return nil, fmt.Errorf("failed to query capability assessments: %w", err)
	}
	defer rows.Close()

	var assessments []CapabilityAssessment
	for rows.Next() {
		var assessment CapabilityAssessment
		err := rows.Scan(
			&assessment.CapabilityAssessmentID, &assessment.RunID,
			&assessment.CapID, &assessment.AggregatedLevel,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan capability assessment: %w", err)
		}
		assessments = append(assessments, assessment)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating capability assessments: %w", err)
	}

	return assessments, nil
}

// GetProcessedDocuments gets all processed documents for a project
func (r *DBRepository) GetProcessedDocuments(projectID string) ([]ProcessedDocument, error) {
	query := `
		SELECT doc_id, project_id, doc_name, checksum, language_iso_code,
		       language_bcp47_tag, chunk_count, status, originating_run_id,
		       created_at, superseded_at
		FROM processed_documents
		WHERE project_id = ? AND status = 'COMPLETE'
		ORDER BY created_at DESC
	`

	rows, err := r.db.Query(query, projectID)
	if err != nil {
		return nil, fmt.Errorf("failed to query processed documents: %w", err)
	}
	defer rows.Close()

	var documents []ProcessedDocument
	for rows.Next() {
		var doc ProcessedDocument
		err := rows.Scan(
			&doc.DocID, &doc.ProjectID, &doc.DocName, &doc.Checksum,
			&doc.LanguageISOCode, &doc.LanguageBCP47Tag, &doc.ChunkCount,
			&doc.Status, &doc.OriginatingRunID, &doc.CreatedAt, &doc.SupersededAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan processed document: %w", err)
		}
		documents = append(documents, doc)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating processed documents: %w", err)
	}

	return documents, nil
}

// Close closes the repository connection
func (r *DBRepository) Close() error {
	return r.db.Close()
}
