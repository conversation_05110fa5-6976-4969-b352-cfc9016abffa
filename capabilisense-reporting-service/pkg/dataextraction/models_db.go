package dataextraction

import (
	"encoding/json"
	"time"
)

// AnalysisRun represents a row from the analysis_runs table
type AnalysisRun struct {
	RunID              string          `db:"run_id" json:"run_id"`
	ProjectID          string          `db:"project_id" json:"project_id"`
	TaskID             string          `db:"task_id" json:"task_id"`
	FrameworkUUID      string          `db:"framework_uuid" json:"framework_uuid"`
	DocumentNames      json.RawMessage `db:"document_names" json:"document_names"`
	Status             string          `db:"status" json:"status"`
	ErrorMessage       *string         `db:"error_message" json:"error_message,omitempty"`
	StartTime          time.Time       `db:"start_time" json:"start_time"`
	EndTime            *time.Time      `db:"end_time" json:"end_time,omitempty"`
	ResultsSummary     json.RawMessage `db:"results_summary" json:"results_summary"`
	ProgressStep       *string         `db:"progress_step" json:"progress_step,omitempty"`
	ProgressPercentage *float64        `db:"progress_percentage" json:"progress_percentage,omitempty"`
}

// Framework represents a row from the frameworks table
type Framework struct {
	FrameworkUUID string          `db:"framework_uuid" json:"framework_uuid"`
	ProjectID     string          `db:"project_id" json:"project_id"`
	Name          string          `db:"name" json:"name"`
	Description   *string         `db:"description" json:"description,omitempty"`
	UploadTime    time.Time       `db:"upload_time" json:"upload_time"`
	Content       json.RawMessage `db:"content" json:"content"`
}

// IndicatorAssessment represents a row from the indicator_assessments table
type IndicatorAssessment struct {
	AssessmentID        int             `db:"assessment_id" json:"assessment_id"`
	RunID               string          `db:"run_id" json:"run_id"`
	CapID               string          `db:"cap_id" json:"cap_id"`
	IndID               string          `db:"ind_id" json:"ind_id"`
	AssessedLevel       int             `db:"assessed_level" json:"assessed_level"`
	Reasoning           *string         `db:"reasoning" json:"reasoning,omitempty"`
	EvidenceChunkIDs    json.RawMessage `db:"evidence_chunk_ids" json:"evidence_chunk_ids"`
	ModelName           *string         `db:"model_name" json:"model_name,omitempty"`
	TokensUsed          *int            `db:"tokens_used" json:"tokens_used,omitempty"`
	AssessmentTimestamp *time.Time      `db:"assessment_timestamp" json:"assessment_timestamp,omitempty"`
}

// CapabilityAssessment represents a row from the capability_assessments table
type CapabilityAssessment struct {
	CapabilityAssessmentID int    `db:"capability_assessment_id" json:"capability_assessment_id"`
	RunID                  string `db:"run_id" json:"run_id"`
	CapID                  string `db:"cap_id" json:"cap_id"`
	AggregatedLevel        int    `db:"aggregated_level" json:"aggregated_level"`
}

// ProcessedDocument represents a row from the processed_documents table
type ProcessedDocument struct {
	DocID            string     `db:"doc_id" json:"doc_id"`
	ProjectID        string     `db:"project_id" json:"project_id"`
	DocName          string     `db:"doc_name" json:"doc_name"`
	Checksum         string     `db:"checksum" json:"checksum"`
	LanguageISOCode  *string    `db:"language_iso_code" json:"language_iso_code,omitempty"`
	LanguageBCP47Tag *string    `db:"language_bcp47_tag" json:"language_bcp47_tag,omitempty"`
	ChunkCount       int        `db:"chunk_count" json:"chunk_count"`
	Status           string     `db:"status" json:"status"`
	OriginatingRunID *string    `db:"originating_run_id" json:"originating_run_id,omitempty"`
	CreatedAt        time.Time  `db:"created_at" json:"created_at"`
	SupersededAt     *time.Time `db:"superseded_at" json:"superseded_at,omitempty"`
}

// DocumentHead represents the first chunk of a document containing organization context
type DocumentHead struct {
	DocID     string    `db:"doc_id" json:"doc_id"`
	DocName   string    `db:"doc_name" json:"doc_name"`
	Content   string    `db:"content" json:"content"`
	CreatedAt time.Time `db:"created_at" json:"created_at"`
}

// CachedInsight represents a cached AI insight
type CachedInsight struct {
	ProjectID    string    `db:"project_id" json:"project_id"`
	RunID        string    `db:"run_id" json:"run_id"`
	InsightType  string    `db:"insight_type" json:"insight_type"`
	InsightData  string    `db:"insight_data" json:"insight_data"`
	TokensUsed   int       `db:"tokens_used" json:"tokens_used"`
	ProviderUsed string    `db:"provider_used" json:"provider_used"`
	UsedFallback bool      `db:"used_fallback" json:"used_fallback"`
	CreatedAt    time.Time `db:"created_at" json:"created_at"`
}

// CachedStageBOutput represents a complete cached Stage B output
type CachedStageBOutput struct {
	ProjectID        string    `db:"project_id" json:"project_id"`
	RunID            string    `db:"run_id" json:"run_id"`
	StageBOutput     string    `db:"stage_b_output" json:"stage_b_output"`
	TotalTokensUsed  int       `db:"total_tokens_used" json:"total_tokens_used"`
	ProvidersUsed    string    `db:"providers_used" json:"providers_used"`
	FallbacksUsed    string    `db:"fallbacks_used" json:"fallbacks_used"`
	ProcessingTimeMs int       `db:"processing_time_ms" json:"processing_time_ms"`
	CreatedAt        time.Time `db:"created_at" json:"created_at"`
}

// TermAliases represents the term aliases structure which can contain nested objects
type TermAliases struct {
	Grouping        string            `json:"Grouping,omitempty"`
	Capability      string            `json:"Capability,omitempty"`
	Indicator       string            `json:"Indicator,omitempty"`
	MaturityLevel   string            `json:"Maturity Level,omitempty"`
	DependencyTypes map[string]string `json:"dependency_types,omitempty"`
	// Additional fields can be added as needed
	AdditionalFields map[string]interface{} `json:"-"` // For any other fields
}

// ToMap converts TermAliases to a simple map[string]string for backward compatibility
func (ta TermAliases) ToMap() map[string]string {
	result := make(map[string]string)
	if ta.Grouping != "" {
		result["Grouping"] = ta.Grouping
	}
	if ta.Capability != "" {
		result["Capability"] = ta.Capability
	}
	if ta.Indicator != "" {
		result["Indicator"] = ta.Indicator
	}
	if ta.MaturityLevel != "" {
		result["Maturity Level"] = ta.MaturityLevel
	}
	// Add dependency types with prefix
	for k, v := range ta.DependencyTypes {
		result["dependency_"+k] = v
	}
	return result
}

// FrameworkContent represents the parsed content from the frameworks.content JSON field
type FrameworkContent struct {
	Entity      EntityNode             `json:"entity"`
	TermAliases TermAliases            `json:"term_aliases"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// AlternativeFrameworkContent represents the alternative framework structure with groupings/capabilities
type AlternativeFrameworkContent struct {
	Entity      AlternativeEntityNode  `json:"entity"`
	TermAliases TermAliases            `json:"term_aliases"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// AlternativeEntityNode represents the entity structure with groupings and capabilities arrays
type AlternativeEntityNode struct {
	Name        string                 `json:"name"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Groupings   []GroupingNode         `json:"groupings,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// GroupingNode represents a grouping in the alternative structure
type GroupingNode struct {
	GroupID      string                 `json:"group_id"`
	GroupName    string                 `json:"group_name"`
	Description  string                 `json:"description,omitempty"`
	Level        int                    `json:"level,omitempty"`
	Groupings    []GroupingNode         `json:"groupings,omitempty"`
	Capabilities []CapabilityNode       `json:"capabilities,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
}

// CapabilityNode represents a capability in the alternative structure
type CapabilityNode struct {
	CapabilityID   string                 `json:"capability_id"`
	CapabilityName string                 `json:"capability_name"`
	Description    string                 `json:"description,omitempty"`
	Priority       string                 `json:"priority,omitempty"`
	Indicators     []IndicatorNode        `json:"indicators,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`
}

// IndicatorNode represents an indicator in the alternative structure
type IndicatorNode struct {
	IndicatorID   string                 `json:"indicator_id"`
	IndicatorName string                 `json:"indicator_name"`
	Description   string                 `json:"description,omitempty"`
	Levels        []LevelDefinition      `json:"levels,omitempty"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// EntityNode represents a node in the framework hierarchy (recursive structure)
type EntityNode struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"` // "grouping", "capability", "indicator"
	Children    []EntityNode           `json:"children,omitempty"`
	Levels      []LevelDefinition      `json:"levels,omitempty"` // For indicators
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// LevelDefinition represents a maturity level definition for an indicator
type LevelDefinition struct {
	Level       int    `json:"level"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Criteria    string `json:"criteria"`
}

// DocumentNamesList represents the parsed document_names JSON field
type DocumentNamesList []string

// ResultsSummaryData represents the parsed results_summary JSON field
type ResultsSummaryData struct {
	TotalCapabilities    int                    `json:"total_capabilities,omitempty"`
	TotalIndicators      int                    `json:"total_indicators,omitempty"`
	CompletedAssessments int                    `json:"completed_assessments,omitempty"`
	AverageScore         float64                `json:"average_score,omitempty"`
	Metadata             map[string]interface{} `json:"metadata,omitempty"`
}

// EvidenceChunkIDsList represents the parsed evidence_chunk_ids JSON field
type EvidenceChunkIDsList []string

// GetDocumentNames parses the DocumentNames JSON field
func (ar *AnalysisRun) GetDocumentNames() (DocumentNamesList, error) {
	var names DocumentNamesList
	if ar.DocumentNames != nil {
		err := json.Unmarshal(ar.DocumentNames, &names)
		return names, err
	}
	return DocumentNamesList{}, nil
}

// GetResultsSummary parses the ResultsSummary JSON field
func (ar *AnalysisRun) GetResultsSummary() (ResultsSummaryData, error) {
	var summary ResultsSummaryData
	if ar.ResultsSummary != nil {
		err := json.Unmarshal(ar.ResultsSummary, &summary)
		return summary, err
	}
	return ResultsSummaryData{}, nil
}

// GetFrameworkContent parses the Content JSON field
func (f *Framework) GetFrameworkContent() (FrameworkContent, error) {
	var content FrameworkContent
	if f.Content != nil {
		err := json.Unmarshal(f.Content, &content)
		return content, err
	}
	return FrameworkContent{}, nil
}

// GetAlternativeFrameworkContent parses the Content JSON field as alternative structure
func (f *Framework) GetAlternativeFrameworkContent() (AlternativeFrameworkContent, error) {
	var content AlternativeFrameworkContent
	if f.Content != nil {
		err := json.Unmarshal(f.Content, &content)
		return content, err
	}
	return AlternativeFrameworkContent{}, nil
}

// DetectFrameworkStructure determines which framework structure is being used
func (f *Framework) DetectFrameworkStructure() string {
	if f.Content == nil {
		return "unknown"
	}

	// Try to parse as a generic JSON to check structure
	var generic map[string]interface{}
	if err := json.Unmarshal(f.Content, &generic); err != nil {
		return "unknown"
	}

	// Check if entity has groupings array (alternative structure)
	if entity, ok := generic["entity"].(map[string]interface{}); ok {
		if _, hasGroupings := entity["groupings"]; hasGroupings {
			return "alternative"
		}
		if _, hasChildren := entity["children"]; hasChildren {
			return "standard"
		}
	}

	return "unknown"
}

// GetEvidenceChunkIDs parses the EvidenceChunkIDs JSON field
func (ia *IndicatorAssessment) GetEvidenceChunkIDs() (EvidenceChunkIDsList, error) {
	var chunkIDs EvidenceChunkIDsList
	if ia.EvidenceChunkIDs != nil {
		err := json.Unmarshal(ia.EvidenceChunkIDs, &chunkIDs)
		return chunkIDs, err
	}
	return EvidenceChunkIDsList{}, nil
}
